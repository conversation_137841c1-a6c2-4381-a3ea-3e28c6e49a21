# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/frontend/node_modules
/frontend/.pnp
/frontend/.pnp.js
/frontend/.yarn/install-state.gz

# testing
/frontend/coverage

# next.js
/frontend/.next/
/frontend/out/

# production
/frontend/build

# misc
/frontend/.DS_Store
/frontend/*.pem

# debug
/frontend/npm-debug.log*
/frontend/yarn-debug.log*
/frontend/yarn-error.log*

# local env files
/frontend/.env*.local
/frontend/.env

# vercel
/frontend/.vercel

# typescript
/frontend/*.tsbuildinfo
/frontend/next-env.d.ts

# backend   
/backend/models/*
/backend/log/*.log*
/backend/pdfs/*
/backend/images/*
/backend/__pycache__/*
/backend/backup/*
backend/__pycache__/*
backend/chathist/*

# late-chunking
/late-chunking/*
late-chunking

# ai related
/.ai/*

# ai model
model/*

# others
jina-embTest.py
backup/*
.byaldi/*
novel/*
pdfs/*
fluxplorer/*
mindmap/*
chathist/*
mcp/

#config
backend/.env
llmcal.txt
flashattnTest.py

# mcp
backend/mcp.json*
backend/mcp.json
backend/mcp.json.bak
