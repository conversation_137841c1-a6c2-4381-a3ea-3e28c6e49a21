# Plan: Improved Markdown & LaTeX Rendering

## Problem Analysis
The current implementation for rendering markdown and LaTeX in the chat interface has several limitations:
1. Poor handling of complex LaTeX expressions
2. Inconsistent rendering of code blocks
3. Limited support for markdown features
4. Security concerns with using dangerouslySetInnerHTML
5. No diagram support

## Potential Solutions
1. Enhance the current implementation with better regex patterns
   - Pros: Minimal changes to existing code
   - Cons: Limited capabilities, still uses dangerouslySetInnerHTML, difficult to maintain
  
2. Use react-markdown with rehype-katex
   - Pros: Comprehensive markdown support, proper LaTeX rendering, secure, extensible
   - Cons: May require additional packages, could impact performance
  
3. Use react-latex-next with custom markdown parsing
   - Pros: Specialized LaTeX support
   - Cons: Limited markdown capabilities, requires custom integration

## Selected Solution
**react-markdown with rehype-katex**

This solution provides the best balance of features, security, and maintainability. It allows for proper LaTeX rendering while supporting a wide range of markdown features. The solution can be extended with additional plugins as needed.

## Implementation Plan

### Phase 1: Preparation and Setup
- [x] Verify if required packages are already installed
- [x] Install missing packages:
  - [x] rehype-raw (for HTML rendering)
  - [x] remark-gfm (for GitHub-flavored markdown)
- [x] Create task documentation

### Phase 2: Component Development
- [x] Create a new MarkdownRenderer component
  - [x] Implement react-markdown with necessary plugins
  - [x] Add rehype-katex and remark-math for LaTeX support
  - [x] Add code highlighting with react-syntax-highlighter
  - [x] Add styling for better appearance

### Phase 3: Integration
- [x] Update renderContent function in MCPChat.tsx
  - [x] Replace current implementation with new MarkdownRenderer component
  - [x] Ensure proper integration with message display
  - [x] Fix any TypeScript issues
  - [x] Remove dangerouslySetInnerHTML usage

### Phase 4: Enhancements
- [x] Add support for multi-line LaTeX environments
  - [x] Implement proper handling for align environments
  - [x] Implement proper handling for aligned environments
  - [x] Implement proper handling for cases environments
  - [x] Implement proper handling for matrix environments
- [x] Add comprehensive matrix rendering support
  - [x] Enhanced support for various matrix types (pmatrix, bmatrix, vmatrix)
  - [x] Support for array environments
  - [x] Special handling for determinants
  - [x] Support for matrix equations with assignment
- [x] Fix TypeScript linting issues
  - [x] Resolve syntax highlighter component typing issues
  - [x] Ensure proper typing for all component props
- [ ] Add diagram support (future enhancement)
  - [ ] Resolve marked package dependency issues
  - [ ] Install mermaid and rehype-mermaid
  - [ ] Integrate with MarkdownRenderer

### Phase 5: Testing and Refinement
- [x] Test with various content types
  - [x] Simple markdown
  - [x] Code blocks
  - [⚠️] Complex LaTeX expressions (works for most cases, some limitations)
  - [⚠️] Matrix expressions (works for basic matrices, some complex cases have issues)
- [⚠️] Address rendering edge cases
  - [⚠️] Inline vs. block math handling (mostly working)
  - [⚠️] Matrix with special delimiters (some inconsistencies)
  - [⚠️] Multi-line equations with alignment (partial success)
- [x] Document known limitations

### Phase 6: Future Improvements
- [ ] Revisit complex matrix rendering issues
- [ ] Test and fix multi-line equation alignment problems
- [ ] Downgrade marked package to resolve dependency conflicts
- [ ] Add diagram support with rehype-mermaid

## Risk Mitigation
- If react-markdown impacts performance, consider lazy loading
- If LaTeX rendering has issues, fall back to simpler implementation for complex expressions
- If security issues arise, add additional sanitization

## Success Criteria
- Markdown renders correctly with proper styling
- LaTeX expressions display properly, including multi-line equations
- Code blocks are highlighted correctly
- No security warnings related to dangerouslySetInnerHTML
- Consistent rendering across different message types

## Current Status
⚠️ Task temporarily closed with significant improvements but known limitations. The implementation provides substantially better rendering capabilities than the previous system, but still has issues with some complex matrices and multi-line equations. Dependency conflicts prevented the addition of diagram support. We will revisit this task in the future when these issues can be properly addressed. 