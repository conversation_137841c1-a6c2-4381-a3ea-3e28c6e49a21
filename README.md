# Knowledge Base Q&A System

This project uses the Jina Embedding v3 model to chunk a novel, convert it into embedding values, and create a simple dialogue web UI to answer user's questions about the novel.

## Project Structure

- `frontend/`: React frontend with Shadcn and Tailwind
  - Components for chat interfaces, file uploads, and UI elements
  - Hooks and utilities for API communication
  - Enhanced markdown rendering with LaTeX support
- `backend/`: FastAPI backend
  - API endpoints and data processing
  - PDF processing and image extraction
  - Chat history management
  - Search engine implementation
  - Optimized API client with token-efficient features
- `model/`: Symlink to Jina Embeddings v3 model
- `docs/`: Project documentation and working memory
  - Working memory organized into open/closed/done task folders
  - Templates for standardized documentation
- `templates/`: Template files for various parts of the application
- `notepad/`: Notes and scratch files
- `pdfs/`: Storage for PDF files
- `mcp/`: MCP client implementation for API communication
- `chathist/`: Chat history storage

## Setup

### Frontend

1. Navigate to the `frontend` directory
2. Run `npm install`
3. Run `npm start`

### Backend

1. Navigate to the `backend` directory
2. Create a virtual environment: `python -m venv venv`
3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`
4. Install dependencies: `pip install -r requirements.txt`
5. The Jina Embedding v3 model is linked from the `model/` directory
6. Run the backend: `python main.py` or `python main_v2.py`

### API Optimizations

The MCP client uses Anthropic's token-efficient-tools beta API to reduce token usage and improve latency when working with Claude models and tools.

## Features

1. Document chunking and embedding using Jina Embedding v3
2. Multiple chat interfaces:
   - Standard knowledge base Q&A
   - Local chat with Ollama integration
   - MCP chat for API communication
   - Mindmap generation
   - Colpali processing
   - Fluxplorer interface
3. PDF processing and image extraction
4. File upload and processing
5. Enhanced markdown and LaTeX rendering:
   - Support for complex mathematical notation
   - GitHub-flavored markdown
   - Code syntax highlighting
   - Multiple LaTeX environments (matrices, equations, etc.)

## Usage

1. Open the web UI in your browser (usually at `http://localhost:3000`)
2. Upload a novel file and select the language (English or Chinese)
3. Once the file is processed, you can start asking questions about the novel
4. The system will return relevant chunks from the novel as answers
5. Additional interfaces are available for specific functionality

## Documentation

The project uses a structured working memory system to track tasks and progress:
- `docs/working-memory/open/`: Active tasks currently in progress
- `docs/working-memory/closed/`: Tasks temporarily paused with known limitations
- `docs/working-memory/done/`: Successfully completed tasks

Each task folder contains detailed planning and implementation documentation.
