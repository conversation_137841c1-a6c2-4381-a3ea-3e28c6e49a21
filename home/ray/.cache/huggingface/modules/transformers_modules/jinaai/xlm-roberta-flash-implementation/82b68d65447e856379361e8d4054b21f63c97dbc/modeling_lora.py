@classmethod
def from_pretrained(
    cls,
    pretrained_model_name_or_path: Optional[Union[str, os.PathLike]],
    *model_args,
    config: Optional[Union[PretrainedConfig, str, os.PathLike]] = None,
    cache_dir: Optional[Union[str, os.PathLike]] = None,
    ignore_mismatched_sizes: bool = False,
    force_download: bool = False,
    local_files_only: bool = False,
    token: Optional[Union[str, bool]] = None,
    revision: str = "main",
    use_safetensors: bool = None,
    **kwargs,
):
    # Replace the config check with proper attribute access
    for key in list(kwargs.keys()):
        if hasattr(config, key):
            setattr(config, key, kwargs.pop(key))
            
    if getattr(config, 'load_trained_adapters', False):  # Use getattr with default
        return super().from_pretrained(
            pretrained_model_name_or_path,
            *model_args,
            config=config,
            cache_dir=cache_dir,
            ignore_mismatched_sizes=ignore_mismatched_sizes,
            force_download=force_download,
            local_files_only=local_files_only,
            token=token,
            revision=revision,
            use_safetensors=use_safetensors,
            **kwargs
        )
    else:
        roberta = XLMRobertaModel.from_pretrained(
            pretrained_model_name_or_path, 
            *model_args, 
            use_flash_attn=config.use_flash_attn if hasattr(config, 'use_flash_attn') else False,
            **kwargs
        )
        return cls(config, roberta=roberta) 