        chunks, span_annotations = chunk_by_sentence(document_chunk, lang) # for ex. one document_chunk contains 3 sentences, then there will be 3 chunks and 3 span_annotations
        inputs = tokenizer(document_chunk, return_tensors='pt').to(device) # inputs['input_ids'].shape = torch.Size([1, 739])
        model_output = model(**inputs)        # torch.Size([1, 739, 1024])
        chunk_embeddings = chunked_pooling(model_output, [span_annotations])[0] # get 3 embeddings for 3 chunks, shape = (3, 1024) chunk_embeddings[0].shape=(1024,)

<-> - L2 distance
<#> - (negative) inner product
<=> - cosine distance
<+> - L1 distance (added in 0.7.0)

# get db connection count
SELECT count(*) FROM pg_stat_activity WHERE datname = 'momdb';
select count(*) from jinanovel where file_name = '3BodyProblem.txt';
select distinct file_name from jinanovel;
select count(*) from jinanovel where file_name = '伤寒恒论-清-郑钦安.txt';


#check os session left
sudo lsof -i :8000

# kill all os session
pkill -9 -f main_v2.py


#model lock and progress lock
 Error processing chunk: The size of tensor a (2170) must match the size of tensor b (2152) at non-singleton dimension 1
This error occurs because there's a mismatch in tensor sizes when trying to process chunks in parallel. The issue wasn't present in the single-process version because all chunks were processed sequentially, using the same model instance.
In the multi-threaded version, we're likely encountering race conditions or inconsistencies when multiple threads are trying to use the model simultaneously. Here's what we can do to address this:
Use a thread-safe model wrapper
Ensure consistent tokenization across threads
Implement a retry mechanism for failed chunks
Let's modify the process_chunk function in backend/main_v2.py:
Key changes:
We've added a global model_lock to ensure that only one thread can use the model at a time.
We've added a retry mechanism that will attempt to process the chunk up to 3 times before giving up.
We've moved the tokenization inside the lock to ensure consistency.
We've added padding and truncation to the tokenizer to ensure consistent input sizes.


# index rebuild
REINDEX INDEX CONCURRENTLY jinanovel_embedding_idx1;
REINDEX INDEX CONCURRENTLY jinanovel_embedding_idx;

SELECT phase, round(100.0 * tuples_done / nullif(tuples_total, 0), 1) AS "%" FROM pg_stat_progress_create_index;

momdb=> CREATE INDEX ON jinanovel USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX
momdb=> \d+ jinanovel
                                                   Table "momsc.jinanovel"
   Column   |          Type           | Collation | Nullable | Default | Storage  | Compression | Stats target | Description 
------------+-------------------------+-----------+----------+---------+----------+-------------+--------------+-------------
 file_name  | character varying(64)   |           | not null |         | extended |             |              | 
 chunk_text | character varying(4096) |           | not null |         | extended |             |              | 
 embedding  | vector(1024)            |           |          |         | extended |             |              | 
Indexes:
    "jinanovel_embedding_idx" hnsw (embedding vector_l2_ops)
    "jinanovel_embedding_idx1" hnsw (embedding vector_cosine_ops)
    "jinanovel_embedding_idx2" ivfflat (embedding vector_cosine_ops) WITH (lists='100')
Access method: heap


SELECT * FROM momsc.jinanovel where file_name = '3BodyProblem.txt' LIMIT 10
SELECT * FROM momsc.jinanovel where file_name = '3BodyProblem.txt' LIMIT 10


# issue after create index

I also faced a situation where I was using the HNSW index type with vector_cosine_ops and I would get no results returned for certain queries. This happened especially when using order by (table.embedding <=> 'vectors here' in my SQL queries in combination with limit 10. If I removed the ordering and/or the limit, depending on the case, then I would get results for the same query.

What seems to have resolved the problem for me was recreating the index with with (m = 16, ef_construction = 1000) instead of the default ef_construction value. I also had to do SET hnsw.ef_search = 100; instead of the default.

Seems like if the table with embeddings gets large enough the default value is not enough. But my table has 16640 records now, which is relatively small in my opinion.


SELECT * FROM pg_indexes WHERE tablename = 'jinanovel' AND indexdef LIKE '%pgroonga%';
REINDEX INDEX jinanovel_pgr_idx;
REINDEX INDEX jinanovel_pgr_idx2;

ANALYZE jinanovel;
UPDATE jinanovel SET original_ctid = ctid;


1. In contextual Embedding mode
我们会利用ollama对每个chunk和10240长的子整个doc进行上下文归纳，然后把归纳和chunk结合做embedding存入数据库里，
同时也把归纳放到contextual_content字段里，以便fulltext时候查询。
在查询时候我们对query进行近似查询，但不再根据query生成keyword了，因为发现无关keyword太多，而且本来上下文
已经是llm归纳过了。所以直接query去fulltext search chunk_text and contextual_content, 两者是OR关系
2。 非 contextual Embedding mode
上传数据时候，我们用到了jina的chunk pooling，没有上下文字段的更新了。full text search时候我们会利用query
让LLM生成keywork然后查询


---
<<Add new ColpaliProcessor>>
Here is our app folder structure @folder structure , pls follow the folder structure when we crreate new lib/components/app route. pls create in existing folders.

Now we added new pdf process logic. Created one new class @ColpaliProcessor.py and we defined 2 new restapi entry
one is '/colpali/indexes" to Retrieve available indexes from ColpaliProcessor . these index was done by new pdf process logic and ready for knowledege search
2nd one is '/colpali/query' to Process the query using the loaded index and return the result
above are the changed happend in backend side.

Now let's create new Tab page call 'Copali' beside Search Tab. and in the new Tab page, will call backend  '/colpali/indexes' api and get all available index and show in one dropdown select component ,
also should have one input component where user can input their query about the index knowledge. one sumbit button (show animation when mouse hove on this button), when user click this button,
it will call backend '/colpali/query' restapi, the return will include one image and related ai response. we will show them in different area. firstly image area will show image and user can interacively zoom
in and zoom out this image and 可以用鼠标移动观看的区域如果图太大的化. 其次我们可以仿照 @ResultDisplay.tsx 创建另一个相似的scroll-area来显示text type response.

-------

DEBUG=* npx next dev -p 8505
DEBUG_CONFIG=true npx next dev -p 8505