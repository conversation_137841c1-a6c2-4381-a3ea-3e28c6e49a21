<<code example>>
from dotenv import load_dotenv
import psycopg2
dbname = os.getenv("DBNAME")
user = os.getenv("DBUSER")
password = os.getenv("DBPWD")
host = os.getenv("DBHOST")
port = os.getenv("DBPORT")
load_dotenv()
def insert_speaker_texts(meeting_id,  spoken_at,spoken_end, speaker_id, speaker_text):
    """
    Insert speaker text data into the database for a given meeting ID.
    """
    
    Logger.log("Database", f"Inserting speaker text for meeting {meeting_id}")
    
    conn = psycopg2.connect(dbname=dbname, user=user, password=password, host=host, port=port)
    cursor = conn.cursor()

    emb_text = speaker_text
    emb_vector = get_text_embedding(emb_text, emb_url)
    
    query = """
    INSERT INTO mom_audio_speaker_texts (meeting_id,  spoken_at, spoken_end, speaker_id, speaker_text, created_by, updated_by, embedding)
    VALUES (%s,  %s, %s, %s, %s, 0, 0, %s);
    """

    try:
        cursor.execute(query, (meeting_id, spoken_at, spoken_end, speaker_id, speaker_text, emb_vector))
        conn.commit()
    except psycopg2.errors.UniqueViolation as e:
        Logger.log("Database", f"Duplicate key warning for meeting {meeting_id}: {str(e)}")
    finally:
        cursor.close()
        conn.close()
		
def query_records():
    try:
        data = request.get_json()
        question = data.get('question')
        topk = data.get('topk', 3)
        filter_condition = data.get('filter', '')
        method = data.get('method', 'default')
        
        if not question:
            Logger.log("ERROR", "No question provided in request for querying")
            return jsonify({'error': 'No question provided for querying'}), 400
        
        conn = psycopg2.connect(dbname=dbname, user=user, password=password, host=host, port=port)
        cursor = conn.cursor()
        
        vec = get_embedding(question)
        item = 'ARRAY[' + ','.join([str(f) for f in vec]) + ']::VECTOR(768)'

        if filter_condition:
            if not filter_condition.strip().startswith('AND'):
                filter_condition = 'AND ' + filter_condition.strip()
        else:
            filter_condition = ''

        if method == 'meeting':
            query = """
                SELECT a.meeting_id, a.meeting_name, a.meeting_room, a.meeting_time, a.ai_summary, a.created_by, a.created_at, a.updated_by, a.updated_at, a.main_lang, a.ai_summary_modified, a.summarize_status, a.meeting_agenda, a.summarize_fail_reason, a.speaker_classification_granularity, a.embedding <-> %s AS distance
                FROM mom_meeting_minutes a
                WHERE 1=1 {}
                ORDER BY distance
                LIMIT %s;
            """.format(filter_condition)
        elif method == 'action':
            query = """
                SELECT a.meeting_id, b.meeting_name, a.action_seq, a.assignee, a.action, a.completed, a.created_by, a.created_at, a.updated_by, a.updated_at, a.embedding <-> %s AS distance
                FROM mom_actions a, mom_meeting_minutes b
                WHERE a.meeting_id = b.meeting_id {}
                ORDER BY distance
                LIMIT %s;
            """.format(filter_condition)
        else:
            query = """
                SELECT a.meeting_id, b.meeting_name, a.spoken_at, a.speaker_id, a.speaker_text, a.embedding <-> %s AS distance 
                FROM mom_audio_speaker_texts a, mom_meeting_minutes b
                WHERE a.meeting_id = b.meeting_id {}
                ORDER BY distance
                LIMIT %s;
            """.format(filter_condition)
                                        
        cursor.execute(query % (item, topk))
        
        results = []
        if method == 'meeting':
            for row in cursor.fetchall():
                results.append({
                    'meeting_id': row[0],
                    'meeting_name': row[1],
                    'meeting_room': row[2],
                    'meeting_time': row[3],
                    'ai_summary': row[4],
                    'created_by': row[5],
                    'created_at': row[6],
                    'updated_by': row[7],
                    'updated_at': row[8],
                    'main_lang': row[9],
                    'ai_summary_modified': row[10],
                    'summarize_status': row[11],
                    'meeting_agenda': row[12],
                    'summarize_fail_reason': row[13],
                    'speaker_classification_granularity': row[14],
                    'distance': float(row[15])
                })
        elif method == 'action':
            for row in cursor.fetchall():
                results.append({
                    'meeting_id': row[0],
                    'meeting_name': row[1],  # 'meeting_name': '
                    'action_seq': row[2],
                    'assignee': row[3],
                    'action': row[4],
                    'completed': row[5],
                    'created_by': row[6],
                    'created_at': row[7],
                    'updated_by': row[8],
                    'updated_at': row[9],
                    'distance': float(row[10])
                })
        else:
            for row in cursor.fetchall():
                results.append({
                    'meeting_id': row[0],
                    'meeting_name': row[1],  # 'meeting_name':
                    'spoken_at': row[2],
                    'speaker_id': row[3],
                    'speaker_text': row[4],
                    'distance': float(row[5])
                })
        
        cursor.close()
        conn.close()
        
        Logger.log("INFO", "Records retrieved successfully")
        return jsonify({'records': results})
    except Exception as e:
        Logger.log("ERROR", f"Error retrieving records: {str(e)}")
        return jsonify({'error': 'Error retrieving records'}), 500
		