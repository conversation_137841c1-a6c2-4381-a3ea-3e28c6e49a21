
Initial prompt
It is edge extension plugin project and let me know what is our next step and how to build/compress/deploy into my edge browser

### get folder structure
tree -L 3 -I .bolt -I node_modules

### build/create extension zip file
#1 npm install archiver --save-dev

#2 add compress.js
import fs from 'fs';
import archiver from 'archiver';

const output = fs.createWriteStream('extension.zip');
const archive = archiver('zip', { zlib: { level: 9 } });

output.on('close', () => {
  console.log('Extension has been compressed');
});

archive.pipe(output);
archive.directory('dist/', false);
archive.finalize();

#3. change package.json

  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "compress": "node scripts/compress.js",
    "build:extension": "npm run build && mkdir -p edge-extension && cp manifest.json edge-extension/ && cp -r dist edge-extension/ && cd edge-extension && zip -r ../my-edge-extension.zip . && cd .."
  },


#4. build/create
npm run build
npm run compress


### Prompt 
You are professional interpreter and will help to translate the content provided to native english, and the translated content should be twitter style and add keywork in the end of the content.


### resume previous talk
it is browser extesion plugin, user can input query or paste image, this plugin will talk to model which user select in Settings page with the system prompt user setup in Settings page as well ,
and get response and display. Here is the example 

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

base64_image = encode_image(IMAGE_PATH)

response = client.chat.completions.create(
    model=MODEL,
    messages=[
        {"role": "system", "content": "You are a helpful assistant that responds in Markdown. Help me with my math homework!"},
        {"role": "user", "content": [
            {"type": "text", "text": "What's the area of the triangle?"},
            {"type": "image_url", "image_url": {
                "url": f"data:image/png;base64,{base64_image}"}
            }
        ]}
    ],
    temperature=0.0,
)

print(response.choices[0].message.content)

# Add replicate api token in settings page
Now we are going to add one feature which is to allow user generate image with this chrome extension plugin.
Firstly let's add REPLICATE_API_TOKEN API Key setup in Settings page. And add one checkbox or switcher right beside of this API Key.
If it is enabled, then when people Sumbit user query in Translator page. It will not call openai model but replicate api to generate image
image will be shown in the image area.
Here is the curl example about how to call replicate api.

curl -s -X POST \
  -H "Authorization: Bearer $REPLICATE_API_TOKEN" \
  -H "Content-Type: application/json" \
  -H "Prefer: wait" \
  -d $'{
    "input": {
      "prompt": "a tiny astronaut hatching from an egg on the moon",
      "go_fast": true,
      "num_outputs": 1,
      "aspect_ratio": "1:1",
      "output_format": "webp",
      "output_quality": 80
    }
  }' \
  https://api.replicate.com/v1/models/black-forest-labs/flux-schnell/predictions