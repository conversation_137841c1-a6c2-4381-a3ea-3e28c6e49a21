# Speed Up

## Set npm to prefer offline mode
npm config set prefer-offline true
npm install -g pnpm
pnpm install

## test
### Create a test directory
mkdir speed-test
cd speed-test

### Initialize a new project with some common dependencies
echo '{
  "name": "speed-test",
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "next": "^14.0.0"
  }
}' > package.json


# Debug
{
    "scripts": {
        "dev": "cross-env NODE_OPTIONS='--inspect' next dev -p 8505"
    }
}

npm install --save-dev cross-env

{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Next.js Debug",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/frontend/node_modules/next/dist/bin/next",
            "args": ["dev", "-p", "8505"],
            "cwd": "${workspaceFolder}/frontend",
            "console": "integratedTerminal",
            "env": {
                "NODE_OPTIONS": "--inspect"
            },
            "resolveSourceMapLocations": [
                "${workspaceFolder}/**",
                "!**/node_modules/**"
            ]
        }
    ]
}

export default function handler(req, res) {
    debugger; // Add this line
    console.log('Debug check'); // Add this for verification
    // Your code here
}
-----------------------------------------------------

