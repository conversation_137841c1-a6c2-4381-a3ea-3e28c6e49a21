plan to develop a support helpdesk chat for my application and it will more like float button locate at the right end of screen.
User can click it then popup a small chat window so that user can chat with it .

it should contain new session button to reset the chat. User and AI should have small icon and quite similar like openai chatgpt
.
New session , initially will send for example below to AWS API gateway
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"message":"What is AWS Lambda?", "sessionId": ""}' \
  https://db5abihr70.execute-api.ap-northeast-1.amazonaws.com/test/chat

  AWS API gateway will response like below for example
  {"response":"I am fine!~","sessionId":"2a068062-85fc-4717-8ff7-e5794f4c5197","isNewSession":true}
  will include response and new session id assinged and isNewSession indicate it is new session 

then helpdesk chatbot will use this sessionid to continue the chat.
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"message":"Have a nice day!", "sessionId": "2a068062-85fc-4717-8ff7-e5794f4c5197"}' \
  https://db5abihr70.execute-api.ap-northeast-1.amazonaws.com/test/chat

And AWS API gateway will response like below if it is existing session
  {"response":"thank you","sessionId":"2a068062-85fc-4717-8ff7-e5794f4c5197","isNewSession":false}

