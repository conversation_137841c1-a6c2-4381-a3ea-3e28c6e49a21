import React from 'react';
import { useState } from 'react';

// Custom styling component for the workflow visualization
const SvelteFlowWorkflowVisualizationWithStyles = () => {
  const [activeNodeId, setActiveNodeId] = useState('1');
  const [expandedNodes, setExpandedNodes] = useState({ '1': true });
  
  const toggleNode = (id) => {
    setExpandedNodes({
      ...expandedNodes,
      [id]: !expandedNodes[id]
    });
    setActiveNodeId(id);
  };
  
  // Mock data for the visualization
  const workflowSteps = [
    {
      id: '1',
      title: 'Analyze market and competitive landscape',
      actions: [
        { type: 'command', label: 'Executing command', value: 'mkdir -p /home/<USER>/saas_project/market_research' },
        { type: 'search', label: 'Searching', value: 'writing improvement tools for Twitter content creators' },
        { type: 'browse', label: 'Browsing', value: 'https://typefully.com/' },
        { type: 'browse', label: 'Browsing', value: 'https://typefully.com/pricing' }
      ]
    },
    {
      id: '2',
      title: 'Develop SaaS concept and initial proposal',
      actions: [
        { type: 'command', label: 'Creating document', value: 'touch /home/<USER>/saas_project/market_research/competitors.md' },
        { type: 'command', label: 'Executing command', value: 'touch /home/<USER>/saas_project/initial_proposal.md' }
      ]
    },
    {
      id: '3',
      title: 'Design UI mockups',
      actions: [
        { type: 'command', label: 'Creating directory', value: 'mkdir -p /home/<USER>/saas_project/ui/mockups' }
      ]
    }
  ];
  
  // Icon for different action types
  const getActionIcon = (type) => {
    switch (type) {
      case 'command': return '➜';
      case 'search': return '🔍';
      case 'browse': return '🌐';
      case 'complete': return '✓';
      default: return '•';
    }
  };
  
  return (
    <div className="w-full h-full bg-gray-50 p-6 font-mono">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-2xl font-semibold text-center mb-6">SaaS Project Development Workflow</h2>
        
        <div className="relative bg-white border border-gray-200 rounded-lg shadow-md p-4 h-[600px] overflow-hidden">
          {/* Background dots */}
          <div className="absolute inset-0 overflow-hidden opacity-20">
            {[...Array(100)].map((_, i) => (
              <div 
                key={i} 
                className="absolute w-1 h-1 bg-gray-400 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`
                }}
              ></div>
            ))}
          </div>
          
          {/* Nodes */}
          <div className="relative h-full pt-8">
            {workflowSteps.map((step, index) => (
              <div key={step.id} className="relative mb-16">
                <div 
                  className={`bg-white border rounded-lg overflow-hidden shadow-sm transition-all duration-200 
                    ${activeNodeId === step.id ? 'border-blue-400 shadow-blue-100' : 'border-gray-200'}`}
                >
                  <div 
                    className={`flex items-center p-3 cursor-pointer transition-colors
                      ${activeNodeId === step.id ? 'bg-blue-50' : 'bg-gray-50 hover:bg-gray-100'}`}
                    onClick={() => toggleNode(step.id)}
                  >
                    <div className="mr-3">
                      <span className={`inline-block w-4 h-4 rounded-full 
                        ${activeNodeId === step.id ? 'bg-blue-500' : 'border border-gray-400'}`}>
                        {activeNodeId === step.id && (
                          <span className="flex items-center justify-center h-full text-white text-xs">
                            ●
                          </span>
                        )}
                      </span>
                    </div>
                    <div className="flex-1 font-medium">{step.title}</div>
                    <div className="text-gray-400 text-xs">
                      {expandedNodes[step.id] ? '▼' : '▶'}
                    </div>
                  </div>
                  
                  {expandedNodes[step.id] && (
                    <div className="border-t border-gray-200">
                      <div className="bg-gray-900 text-gray-200 rounded-b-lg overflow-hidden">
                        {step.actions.map((action, actionIndex) => (
                          <div 
                            key={actionIndex} 
                            className="flex items-start py-2 px-3 border-b border-gray-800 text-sm"
                          >
                            <span className={`w-6 mr-2 text-center
                              ${action.type === 'command' ? 'text-green-400' : 
                                action.type === 'search' ? 'text-yellow-400' : 
                                action.type === 'browse' ? 'text-blue-400' : 'text-gray-400'}`}
                            >
                              {getActionIcon(action.type)}
                            </span>
                            <span className="w-32 text-gray-400">{action.label}</span>
                            <span 
                              className={`flex-1 font-mono break-all
                                ${action.type === 'command' ? 'text-green-400' : 
                                  action.type === 'search' ? 'text-yellow-400' : 
                                  action.type === 'browse' ? 'text-blue-400 underline' : 'text-gray-200'}`}
                            >
                              {action.value}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Connector line */}
                {index < workflowSteps.length - 1 && (
                  <div className="absolute left-4 top-full h-16 w-0.5 bg-gray-300">
                    <div className="absolute bottom-0 left-1/2 w-3 h-3 -ml-1.5 bg-gray-300 rounded-full"></div>
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {/* Controls panel */}
          <div className="absolute top-4 right-4 bg-white border border-gray-200 rounded-md shadow-sm p-3 w-48">
            <h4 className="text-sm font-medium mb-2">Workflow Progress</h4>
            <div className="h-2 w-full bg-gray-200 rounded-full overflow-hidden">
              <div className="h-full bg-green-500 rounded-full" style={{ width: '33%' }}></div>
            </div>
            <div className="text-xs text-gray-500 text-right mt-1">33% Complete</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SvelteFlowWorkflowVisualizationWithStyles;