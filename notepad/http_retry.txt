# 添加全局请求计数器和时间窗口
class RateLimiter:
    def __init__(self, max_requests=50, time_window=60):  # 每分钟50个请求
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        
    def can_make_request(self):
        now = time.time()
        # 清理旧的请求记录
        self.requests = [req_time for req_time in self.requests if now - req_time < self.time_window]
        
        if len(self.requests) < self.max_requests:
            self.requests.append(now)
            return True
        return False
    
    def wait_for_next_window(self):
        if self.requests:
            oldest_request = min(self.requests)
            sleep_time = self.time_window - (time.time() - oldest_request)
            if sleep_time > 0:
                time.sleep(sleep_time)

rate_limiter = RateLimiter()

def retry_with_rate_limit(max_retries=3, initial_wait=10):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                if not rate_limiter.can_make_request():
                    Logger.log("Rate Limiter", "Rate limit reached, waiting for next window")
                    rate_limiter.wait_for_next_window()
                
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if "429" in str(e):
                        retries += 1
                        if retries < max_retries:
                            wait_time = initial_wait * (2 ** retries)  # 指数退避
                            Logger.log("Rate Limiter", f"Rate limit hit, waiting {wait_time} seconds before retry {retries}")
                            time.sleep(wait_time)
                        else:
                            Logger.log("Rate Limiter", "Max retries reached, returning default value")
                            return "🙊🙊🙊"
                    else:
                        raise e
            return "🙊🙊🙊"
        return wrapper
    return decorator

@retry_with_rate_limit(max_retries=3, initial_wait=10)
def replace_profanity(text, llm):
