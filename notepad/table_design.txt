-- Active: 1724621105530@@192.168.0.16@5432@momdb
# Below is postgreql table definition.

create table momsc.jinanovel(
  file_name varchar(64) NOT NULL,
  chunk_text TEXT NOT NULL,
  embedding vector(1024)
);

I removed all below index as find wrong result
#CREATE INDEX ON jinanovel USING hnsw (embedding vector_l2_ops) WITH (m = 16, ef_construction = 10000);
#CREATE INDEX ON jinanovel USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 10000);
#CREATE INDEX ON jinanovel USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
#CREATE INDEX ON items USING ivfflat (embedding vector_l2_ops) WITH (lists = 100);
ALTER TABLE momsc.jinanovel ALTER COLUMN chunk_text TYPE text;
create index jinanovel_pgr_idx on jinanovel using pgroonga(chunk_text);
CREATE INDEX jinanovel_pgr_idx2 ON jinanovel USING pgroonga (file_name, chunk_text);
CREATE INDEX jinanovel_pgr_idx3 ON jinanovel USING pgroonga (file_name, chunk_text,contextual_content);

ALTER TABLE jinanovel ADD COLUMN original_ctid tid;
ALTER TABLE jinanovel ADD COLUMN contextual_content TEXT;

# database user permission
postgres=# \c momdb
You are now connected to database "momdb" as user "postgres".
momdb=# create user momusr password 'momusr#345';
CREATE ROLE
momdb=# grant connect on database momdb to momusr;
GRANT
momdb=# create schema momsc;
CREATE SCHEMA
momdb=# alter schema momsc owner to momusr;
ALTER SCHEMA
momdb=# alter user momusr set search_path=momsc,public;
ALTER ROLE
momdb=# grant all privileges on all tables in schema momsc to momusr;

UPDATE jinanovel SET original_ctid = ctid;