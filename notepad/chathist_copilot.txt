Let's add local AI chat into AI Portal @ai-product-portal.tsx .  

#Frontend
The UI and process logic can be similar like @MultiModelChat.tsx but now user only talk to one LLM which is ollama local AI.We will also use websocket and save history records during conversation.   backendUrl value we can get similar like @MultiModelChat.tsx, 
 useEffect(() => {
    const loadConfig = async () => {
      const config = await fetchEnvConfig();
      setBackendUrl(config.backendUrl);
      setModelNames(config.llmModels);
    };
    loadConfig();
  }, []);

but frontend we need to send the olllama api url and ollama model name to backend so that we can dynamically change frontend .env file and make it effect without restart application.


Like @MultiModelChat.tsx, we need to support Latex format and code block these features. Like OpenAI chatgpt and claude
 
#Backend
backend source you can refer to @main_v2.py @websocket_handler.py 

as you can see , frontend wont save history chat message but in backend side , additionally let's save this history whole conversaction into json file and save into backend subfolder chathist , next time we can reload it similar like openai chatgpt. user can select one history conversation and continue or start new chat. 


backend will use api_url to initialize conversation with ollama server. 
Here is some example about how to communciate with ollama with stream mode and how to chat with ollama with image data. 
```
import base64
import ollama

def image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

def process_image_and_text(image_path, prompt):
    encoded_image = image_to_base64(image_path)
    
    response = ollama.chat(
        model="llava",
        messages=[
            {
                "role": "user",
                "content": prompt,
                "images": [encoded_image]
            }
        ]
    )
    
    return response['message']['content']

# Example usage
image_path = "path/to/your/image.jpg"
prompt = "Describe this image in detail."
result = process_image_and_text(image_path, prompt)
print(result)


------------------------------------------
import json
import requests

# NOTE: ollama must be running for this to work, start the ollama app or run `ollama serve`
model = 'stablelm-zephyr' # TODO: update this for whatever model you wish to use

def generate(prompt, context):
    r = requests.post('http://localhost:11434/api/generate',
                      json={
                          'model': model,
                          'prompt': prompt,
                          'context': context,
                      },
                      stream=True)
    r.raise_for_status()

    for line in r.iter_lines():
        body = json.loads(line)
        response_part = body.get('response', '')
        # the response streams one token at a time, print that as we receive it
        print(response_part, end='', flush=True)

        if 'error' in body:
            raise Exception(body['error'])

        if body.get('done', False):
            return body['context']

def main():
    context = [] # the context stores a conversation history, you can use this to make the model more context aware
    while True:
        user_input = input("Enter a prompt: ")
        if not user_input:
            exit()
        print()
        context = generate(user_input, context)
        print()

if __name__ == "__main__":
    main()
```


Let's start 
-------------------------------