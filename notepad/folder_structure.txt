  We have initialized our project. Let's continue and below is folder structure.
pls make sure all frontend related files please put into frontend subfolder and all backend related file,pls put into backend related subfolder.

root
├── backend
│   ├── ColpaliProcessor.py
│   ├── main_v2.py
│   ├── models
│   │   └── jina_embedding_v3 -> ../../model/
│   ├── PdfImage.py
│   ├── pdfs
│   ├── requirements.txt
│   └── search_engine.py
├── frontend
│   ├── app
│   │   ├── favicon.ico
│   │   ├── fonts
│   │   │   ├── GeistMonoVF.woff
│   │   │   └── GeistVF.woff
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components
│   │   ├── ChatInterface.tsx
│   │   ├── FileUpload.tsx
│   │   ├── ResultDisplay.tsx
│   │   ├── EnhancedZoomableImage.tsx
│   │   ├── ColpaliInterface.tsx
│   │   └── ui
│   │       ├── alert.tsx
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── checkbox.tsx
│   │       ├── input.tsx
│   │       ├── label.tsx
│   │       ├── progress.tsx
│   │       ├── scroll-area.tsx
│   │       ├── select.tsx
│   │       ├── slider.tsx
│   │       ├── switch.tsx
│   │       ├── tabs.tsx
│   │       └── textarea.tsx
│   ├── components.json
│   ├── lib
│   │   ├── background_image.ts
│   │   ├── constants.ts
│   │   └── utils.ts
│   ├── next.config.mjs
│   ├── next-env.d.ts
│   ├── package.json
│   ├── package-lock.json
│   ├── postcss.config.mjs
│   ├── README.md
│   ├── tailwind.config.ts
│   └── tsconfig.json
├── model -> /opt/workspace/aibase/jina-embeddings-v3
├── notepad
│   ├── db_operation_ex.txt
│   ├── fast_memo.txt
│   ├── folder_structure.txt
│   ├── llmcal.txt
│   └── table_design.txt
├── README.md
└── requirement.txt