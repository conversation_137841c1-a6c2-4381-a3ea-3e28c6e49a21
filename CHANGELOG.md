# Changelog

## 2025-04-03

### User Interface Enhancement
- Added enhanced Markdown and LaTeX rendering support in frontend components
- Created new `MarkdownRenderer` component with react-markdown, rehype-katex, and remark-math
- Added support for complex mathematical notation including matrices and equations
- Improved code block rendering with syntax highlighting
- Replaced unsafe HTML rendering with secure React components
- Known limitations with complex matrix rendering and diagram support (to be addressed in future)

### Performance Optimization
- Integrated Anthropic's token-efficient-tools beta API in backend/mcp_client.py
- Updated both regular and streaming API calls to use beta.messages.create/stream with betas=["token-efficient-tools-2025-02-19"]
- Reduced token usage and improved latency for tool-based LLM calls

### Documentation
- Updated working memory organization with open/closed/done folders
- Added comprehensive task documentation for completed and temporarily closed tasks
- Updated project structure documentation in `.cursor/rules/structure.mdc` and `.cursor/rules/folder_structure.mdc`
- Updated README.md to reflect current project structure
- Added detailed component descriptions and feature list to README.md

### Project Structure
- Updated documentation of folder and file structure
- Documented symlink from backend models to main model directory

## Previous Changes
- Initial project setup
- Added frontend with <PERSON>act, Shadcn and Tailwind
- Implemented backend with FastAPI
- Integrated Jina Embedding v3 model
- Added multiple chat interfaces (Local chat, MCP chat, Mindmap, Colpali, Fluxplorer)
- Added PDF processing and image extraction features
