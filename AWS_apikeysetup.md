# Setting up API Key Authentication in AWS API Gateway

## Option 1: Using AWS Console (Recommended for beginners)

1. **Create API Key**
   - Go to API Gateway console
   - Click on "API Keys" in the left sidebar
   - Click "Create API key"
   - Enter a name for your key
   - Click "Save"

2. **Create Usage Plan**
   - Go to "Usage Plans" in the left sidebar
   - Click "Create usage plan"
   - Enter plan details:
     ```
     Name: "chat-usage-plan"
     Description: "Usage plan for chat application"
     Throttling: 10 requests per second
     Burst: 20 requests
     Quota: 1000 requests per month
     ```
   - Click "Next"
   - Associate your API stage
   - Add the API key you created
   - Click "Done"

3. **Enable API Key Requirement**
   - Go to your API's resources
   - Select the POST method
   - Click "Method Request"
   - Set "API Key Required" to "true"
   - Click the checkmark to save
   - Deploy your API again

## Option 2: Using AWS CLI

1. **Create API Key**
   ```bash
   aws apigateway create-api-key \
     --name "chat-api-key" \
     --description "API Key for chat application" \
     --enabled
   ```

2. **Create Usage Plan**
   ```bash
   aws apigateway create-usage-plan \
     --name "chat-usage-plan" \
     --description "Usage plan for chat application" \
     --throttle \
         rateLimit=10 \
         burstLimit=20 \
     --quota \
         limit=1000 \
         offset=0 \
         period=MONTH
   ```

3. **Add API Stage to Usage Plan**
   ```bash
   aws apigateway update-usage-plan \
     --usage-plan-id YOUR_USAGE_PLAN_ID \
     --patch-operations \
       op=add,path=/apiStages,value=YOUR_API_ID:test
   ```

4. **Associate API Key with Usage Plan**
   ```bash
   aws apigateway create-usage-plan-key \
     --usage-plan-id YOUR_USAGE_PLAN_ID \
     --key-id YOUR_API_KEY_ID \
     --key-type API_KEY
   ```

## Frontend Integration

1. Get your API key from AWS Console after setup

2. Create `.env.local` in your frontend project root:
   ```
   NEXT_PUBLIC_API_KEY=your_api_key_here
   ```

3. Add API key to your fetch requests:
   ```javascript
   headers: {
     'Content-Type': 'application/json',
     'x-api-key': process.env.NEXT_PUBLIC_API_KEY
   }
   ```

## Security Notes

- Never commit `.env.local` to version control
- Keep your API key secure
- Consider implementing rate limiting and monitoring in your usage plan
- For production use, consider implementing a more secure authentication method (like JWT)
- Monitor API usage through AWS CloudWatch
- Regularly rotate API keys for security

## Troubleshooting

1. If getting 403 errors:
   - Verify API key is correctly set in frontend
   - Check if API key is enabled in AWS
   - Verify Usage Plan is correctly associated

2. If getting CORS errors:
   - Ensure CORS is enabled in API Gateway
   - Add 'x-api-key' to Access-Control-Allow-Headers

3. If rate limiting issues:
   - Check Usage Plan throttling settings
   - Monitor usage in AWS Console
   - Consider adjusting limits based on your needs 