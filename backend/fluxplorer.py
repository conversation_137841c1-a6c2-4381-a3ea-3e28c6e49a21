from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from termcolor import colored
from together import Together
import replicate
import asyncio
import random
import os
import time
from typing import List
from lists import art_styles, things

# Remove the prefix here - it's already defined in main_v2.py
router = APIRouter(tags=["fluxplorer"])

# Debug logging
def log_debug(message: str):
    print(colored(f"[Fluxplorer] {message}", "cyan"))

# Initialize Together client
try:
    client = Together(api_key=os.getenv("TOGETHER_API_KEY"))
    log_debug("Together API client initialized successfully")
except Exception as e:
    print(colored(f"Error initializing Together API client: {str(e)}", "red"))

def get_shuffled_subset(items: List[str], start: int, count: int) -> List[str]:
    """Get a subset of shuffled items"""
    try:
        items_copy = items.copy()
        if start == 0:
            random.shuffle(items_copy)
        end = min(start + count, len(items_copy))
        return items_copy[start:end]
    except Exception as e:
        print(colored(f"Error in get_shuffled_subset: {str(e)}", "red"))
        return items[start:min(start + count, len(items))]


@router.get("/load-more-items", response_class=JSONResponse)
async def load_more_items(start: int, category: str):
    log_debug(f"Loading items for category: {category}, start: {start}")
    try:
        items = art_styles if category == "art_styles" else things
        more_items = get_shuffled_subset(items, start, 150)
        return {
            "items": more_items,
            "hasMore": start + 150 < len(items)
        }
    except Exception as e:
        log_debug(f"Error loading more items: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@router.post("/generate-image")
async def generate_image(request: Request):
    try:
        data = await request.json()
        prompt = data.get("prompt", "")
        aspect = data.get("aspect", "square")
        model = data.get("model", "black-forest-labs/FLUX.1-schnell")
        
        # Map aspect ratios to dimensions
        aspect_ratios = {
            "square": (1024, 1024),
            "landscape": (1024, 768),
            "portrait": (768, 1024)
        }
        
        width, height = aspect_ratios.get(aspect, (1024, 1024))
        
        # Generate image using Together API with selected model
        response = await asyncio.to_thread(
            client.images.generate,
            prompt=prompt,
            model=model,
            steps=4,
            width=width,
            height=height,
            n=1
        )
        
        return JSONResponse({
            "url": response.data[0].url,
            "prompt": prompt,
            "aspect": aspect,
            "model": model,
            "timestamp": asyncio.get_event_loop().time()
        })
        
    except Exception as e:
        print(colored(f"Error generating image: {e}", "red"))
        return JSONResponse({"error": str(e)}, status_code=500) 

@router.post("/generate-video")
async def generate_video(request: Request):
    try:
        data = await request.json()
        prompt = data.get("prompt", "")
        aspect = data.get("aspect", "16:9")
        cfg = data.get("cfg", 3)
        steps = data.get("steps", 30)
        length = data.get("length", 97)
        target_size = data.get("target_size", 640)
        
        # Initialize Replicate client
        replicate_client = replicate.Client(api_token=os.getenv("REPLICATE_API_TOKEN"))
        
        # Create prediction
        prediction = await asyncio.to_thread(
            replicate_client.predictions.create,
            version="983ec70a06fd872ef4c29bb6b728556fc2454125a5b2c68ab51eb8a2a9eaa46a",
            input={
                "prompt": prompt,
                "cfg": cfg,
                "steps": steps,
                "length": length,
                "target_size": target_size,
                "aspect_ratio": aspect,
                "negative_prompt": "low quality, worst quality, deformed, distorted, watermark"
            }
        )
        
        # Poll for completion
        while prediction.status not in ["succeeded", "failed", "canceled"]:
            prediction = await asyncio.to_thread(
                replicate_client.predictions.get,
                prediction.id
            )
            if prediction.status == "failed":
                raise Exception(f"Video generation failed: {prediction.error}")
            elif prediction.status == "succeeded":
                break
            await asyncio.sleep(1)
        
        if prediction.status == "succeeded":
            return JSONResponse({
                "url": prediction.output[0],
                "prompt": prompt,
                "aspect": aspect,
                "timestamp": asyncio.get_event_loop().time()
            })
        else:
            raise Exception(f"Video generation failed: {prediction.error}")
            
    except Exception as e:
        print(colored(f"Error generating video: {e}", "red"))
        return JSONResponse({"error": str(e)}, status_code=500) 