## spacy package setup
# https://bobbyhadz.com/blog/os-error-cant-find-model-en-core-web-sm#:~:text=The%20spaCy%20error%20%22OSError%3A%20%5BE050%5D%20Can%27t%20find%20model,issuing%20the%20python%20-m%20spacy%20download%20en_core_web_sm%20command.
# python -m spacy download en_core_web_sm
# python -m spacy download zh_core_web_sm
# pip install psycopg2-binary python-dotenv
# sudo apt install rustc cargo
# python -m spacy download ja_core_news_sm
# pip install mecab-python3 unidic-lite spacy
from curses.ascii import ESC
from fastapi import FastAPI, UploadFile, File, HTTPException, Query, BackgroundTasks, Body, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import spacy
from spacy.tokens import Doc
from spacy.language import Language
import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModel
import json
import numpy as np
import jieba    
import asyncio
import logging
from logging.handlers import TimedRotatingFileHandler
import os
from pathlib import Path
import psycopg2
from psycopg2.extras import execute_values
from dotenv import load_dotenv
from psycopg2 import sql
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from threading import Lock
import openai
import anthropic
import ollama
from os import getenv
import requests
from typing import List, Dict, Any, Tuple
from flashrank import Ranker, RerankRequest
import MeCab
import re
import cohere
from PdfImage import PDFProcessor
from transformers import AutoModelForCausalLM, AutoTokenizer
from search_engine import SearchEngine
from pathlib import Path
from ColpaliProcessor import ColpaliProcessor
from websocket_handler import WebSocketHandler
from contextlib import asynccontextmanager
from fluxplorer import router as fluxplorer_router
from termcolor import colored
from mindmap_handler import router as mindmap_router
from local_chat_handler import LocalChatHandler
from mcp_chat_handler import MCPChatHandler

load_dotenv()

# Database connection parameters
dbname = os.getenv("DBNAME")
user = os.getenv("DBUSER")
password = os.getenv("DBPWD")
host = os.getenv("DBHOST")
port = os.getenv("DBPORT")
PDF_METHOD = os.getenv('PDF_METHOD', 'legacy')

# Set up logging
log_dir = Path(__file__).parent / "log"
log_dir.mkdir(exist_ok=True)
log_file = log_dir / "app.log"


# Configure the logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)  # Set to DEBUG to capture all log levels

# Create a TimedRotatingFileHandler
file_handler = TimedRotatingFileHandler(
    filename=log_file,
    when="midnight",
    interval=1,
    backupCount=7,  # Keep logs for 7 days
    encoding="utf-8",
)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# Determine the device at the start of the script
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
if device.type == "cuda":
    torch.cuda.empty_cache()

@asynccontextmanager
async def lifespan(app: FastAPI):
    print(colored("\nStarting FastAPI server...", "green"))
    print(colored("\nRegistered routes:", "cyan"))
    for route in app.routes:
        if hasattr(route, 'methods'):
            methods = route.methods
            print(colored(f"  {route.path:<50} [{', '.join(methods)}]", "cyan"))
        else:
            print(colored(f"  {route.path:<50} [WebSocket]", "cyan"))
    
    init_llm_clients()
    yield

app = FastAPI(title="AI Portal Backend", lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the fluxplorer router
app.include_router(
    fluxplorer_router,
    prefix="/fluxplorer",
    tags=["fluxplorer"]
)

# Add this where other routers are included
app.include_router(mindmap_router,
    prefix="/mindmap",
    tags=["mindmap"]
)

class XunziLLM:
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        if self.device == "cuda":
            torch.cuda.empty_cache()
        self.model = AutoModelForCausalLM.from_pretrained(
            "/opt/workspace/aibase/Xunzi-Qwen1.5-7B_chat",
            torch_dtype="auto",
            device_map="cuda" if self.device == "cuda" else None
        )
        self.tokenizer = AutoTokenizer.from_pretrained("/opt/workspace/aibase/Xunzi-Qwen1.5-7B_chat")

    def generate(self, messages, max_new_tokens=1024, temperature=0.8):
        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )
        model_inputs = self.tokenizer([text], return_tensors="pt").to(self.device)
        attention_mask = model_inputs['attention_mask']

        generated_ids = self.model.generate(
            model_inputs.input_ids,
            max_new_tokens=max_new_tokens,
            attention_mask=attention_mask,
            repetition_penalty=1.15,
            pad_token_id=self.tokenizer.eos_token_id,
            eos_token_id=self.tokenizer.eos_token_id,
            do_sample=True,
            temperature=temperature
        )

        generated_ids = [
            output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs.input_ids, generated_ids)
        ]

        response = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]
        return response
    
# Initialize XunziLLM
xunzi_llm = None

# Load Jina Embedding v3 Model, max length 8192 and dim 1024
model_path = "/opt/workspace/app/cursor/jina/model"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
model = AutoModel.from_pretrained(model_path, trust_remote_code=True, use_flash_attn=False,
                                  revision="82b68d65447e856379361e8d4054b21f63c97dbc").to(device)
ranker = Ranker(model_name="ms-marco-MultiBERT-L-12", cache_dir=model_path)

# CONTEXTUAL EMBEDDING Flag
contextual_embedding = True

# Global variables to store upload progress
upload_progress = 0
progress_tracker = None

# Create a global lock for the model
model_lock = Lock()

# Initialize the SearchEngine in the global scope
global_search_engine = SearchEngine()

# Initialize ColpaliProcessor
colpali_processor = ColpaliProcessor()

# 全局变量存储活跃的 WebSocket 连接
active_connections: List[WebSocket] = []

# LLM 客户端字典
llm_clients = {
    'openai': None,
    'anthropic': None,
    'openrouter': None
}

def init_llm_clients():
    global llm_clients
    try:
        llm_clients = {
            'openai': get_llm_client('openai'),
            'anthropic': get_llm_client('claude'),
            'openrouter': get_llm_client('openrouter')
        }
        logger.info("LLM clients initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing LLM clients: {str(e)}")

# Update the websocket endpoint to ensure clients are initialized
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    global llm_clients
    # Ensure LLM clients are initialized
    if not any(llm_clients.values()):
        init_llm_clients()
    handler = WebSocketHandler(llm_clients)
    await handler.handle_websocket(websocket)

local_chat_handler = LocalChatHandler()

@app.websocket("/ws/local-chat")
async def local_chat_websocket(websocket: WebSocket):
    await websocket.accept()
    chat_handler = LocalChatHandler()
    try:
        while True:
            data = await websocket.receive_json()
            await chat_handler.handle_message(websocket, data)
    except WebSocketDisconnect:
        print("Client disconnected")

@app.websocket("/ws/mcp-chat")
async def mcp_chat_websocket(websocket: WebSocket):
    await websocket.accept()
    chat_handler = MCPChatHandler()
    try:
        while True:
            data = await websocket.receive_json()
            await chat_handler.handle_message(websocket, data)
    except WebSocketDisconnect:
        pass

def mean_pooling(model_output, attention_mask):
    token_embeddings = model_output[0]
    input_mask_expanded = (
        attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
    )
    return torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(
        input_mask_expanded.sum(1), min=1e-9
    )

def generate_embedding(input_string: str, method: str="retrieval.query"):
    # Tokenize the input string
    sentences = [input_string]
    encoded_input = tokenizer(sentences, padding=True, truncation=True, return_tensors="pt").to(device)

    # Prepare the task and adapter mask
    task = method
    # task = 'text-matching'
    task_id = model._adaptation_map[task]
    adapter_mask = torch.full((len(sentences),), task_id, dtype=torch.int32).to(device)

    # Generate embeddings
    with torch.no_grad():
        model_output = model(**encoded_input, adapter_mask=adapter_mask)

    # Apply mean pooling
    embeddings = mean_pooling(model_output, encoded_input["attention_mask"])

    # Normalize the embeddings
    embeddings = F.normalize(embeddings, p=2, dim=1)

    # Return the embeddings as a tensor
    return embeddings.cpu().numpy().flatten()

def generate_query_embedding(query_text: str):
    # try:
    #     with model_lock:
         # Tokenize the input string
        inputs = tokenizer(query_text, return_tensors='pt').to(device)
        # Generate embeddings
        with torch.no_grad():
            model_output = model(**inputs)

        # Apply mean pooling
        attention_mask = inputs['attention_mask']
        embeddings = mean_pooling(model_output, attention_mask)

        # Normalize the embeddings
        embeddings = F.normalize(embeddings, p=2, dim=1)

        # Move to CPU and convert to numpy array
        return embeddings.cpu().numpy().flatten()
    
    # except Exception as e:
    #     logger.error(f"Error generating query embedding: {str(e)}")
    #     return None

def generate_query_embedding_v2(query_text: str):
    # Tokenize the input string
    inputs = tokenizer(query_text, return_tensors='pt').to(device)

    # Generate embeddings
    with torch.no_grad():
        model_output = model(**inputs)

    embeddings = model_output.pooler_output
    # Move to CPU, convert to float32, and then to numpy array
    return embeddings.cpu().float().numpy().flatten()

def chunked_pooling(
    model_output, span_annotation: list, max_length=None
):
    token_embeddings = model_output[0]
    outputs = []
    for embeddings, annotations in zip(token_embeddings, span_annotation):
        if (
            max_length is not None
        ):  # remove annotations which go beyond the max-length of the model
            annotations = [
                (start, min(end, max_length - 1))
                for (start, end) in annotations
                if start < (max_length - 1)
            ]
        pooled_embeddings = [
            embeddings[start:end].sum(dim=0) / (end - start)
            for start, end in annotations
            if (end - start) >= 1
        ]
        pooled_embeddings = [
            embedding.float().detach().cpu().numpy() for embedding in pooled_embeddings
        ]
        outputs.append(pooled_embeddings)

    return outputs

def safe_chunked_pooling(model_output, span_annotation: list, max_length=None):
    token_embeddings = model_output[0]
    outputs = []
    for embeddings, annotations in zip(token_embeddings, span_annotation):
        if max_length is not None:
            annotations = [
                (start, min(end, max_length - 1))
                for (start, end) in annotations
                if start < (max_length - 1)
            ]
        pooled_embeddings = []
        for start, end in annotations:
            if end > embeddings.size(0):
                end = embeddings.size(0)
            if start < end:
                pooled_embedding = embeddings[start:end].mean(dim=0)
                pooled_embeddings.append(pooled_embedding)
        
        pooled_embeddings = [
            embedding.float().detach().cpu().numpy() for embedding in pooled_embeddings
        ]
        outputs.append(pooled_embeddings)

    return outputs

# Helper functions (implement these based on the provided code)
def chunk_by_sentence(document, lang, batch_size=None):
    if batch_size is None:
        batch_size = 8192  # no of characters

    if lang == "en":
        nlp = spacy.load("en_core_web_sm")
    elif lang == "zh":
        nlp = spacy.load("zh_core_web_sm")
    elif lang == "ja":
        nlp = spacy.load("ja_core_news_sm")
    else:
        raise HTTPException(status_code=400, detail="Language not supported")
    
    docs = []
    for i in range(0, len(document), batch_size):
        batch = document[i : i + batch_size]
        docs.append(nlp(batch))

    doc = Doc.from_docs(docs)

    span_annotations = []
    chunks = []
    for i, sent in enumerate(doc.sents):
        span_annotations.append((sent.start, sent.end))
        chunks.append(sent.text)
    return chunks, span_annotations

def split_document(document, max_length=8192, lang="en"):
    if lang == "zh":
        words = list(jieba.cut(document))
    elif lang == "ja":
        mecab = MeCab.Tagger("-Owakati")
        words = mecab.parse(document).split()
    else:
        words = document.split()

    chunks = []
    current_chunk = []
    current_length = 0

    for word in words:
        if current_length + len(word) + 1 > max_length:
            chunks.append(''.join(current_chunk) if lang in ["zh", "ja"] else ' '.join(current_chunk))
            current_chunk = [word]
            current_length = len(word)
        else:
            current_chunk.append(word)
            current_length += len(word) + (0 if lang in ["zh", "ja"] else 1)

    if current_chunk:
        chunks.append(''.join(current_chunk) if lang in ["zh", "ja"] else ' '.join(current_chunk))

    return chunks


class ProgressTracker:
    def __init__(self, total_chunks):
        self.lock = threading.Lock()
        self.processed_chunks = 0
        self.total_chunks = total_chunks

    def update(self):
        with self.lock:
            self.processed_chunks += 1
            return int(self.processed_chunks / self.total_chunks * 100)

def process_chunk(doc, lang, file_name, progress_tracker):
    global upload_progress, model, tokenizer
    
    logger.info(f"Starting to process chunk for file: {file_name}, language: {lang}")
    logger.debug(f"Document Chunk size: {len(doc)} characters")
    
    chunk_texts, span_annotations = chunk_by_sentence(doc, lang)
    logger.debug(f"Document Chunk split into {len(chunk_texts)} sentences")
    
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            with model_lock:
                inputs = tokenizer(doc, return_tensors='pt', padding=True, truncation=True, max_length=8192).to(device)
                logger.debug(f"Tokenized input shape: {inputs['input_ids'].shape}")
                
                model_output = model(**inputs)
                logger.debug(f"Model output shape: {model_output[0].shape}")
                
                chunk_embeddings = chunked_pooling(model_output, [span_annotations])[0]
                logger.debug(f"Generated {len(chunk_embeddings)} embeddings")
            
            batch = []
            for chunk_text, embedding in zip(chunk_texts, chunk_embeddings):
                batch.append((file_name, chunk_text, embedding.tolist()))
            
            # Update progress
            upload_progress = progress_tracker.update()
            logger.info(f"Chunk processed. Progress: {upload_progress}%")
            
            return batch
        except Exception as e:
            logger.error(f"Error processing chunk (attempt {attempt + 1}/{max_retries}): {str(e)}")
            if attempt == max_retries - 1:
                raise
    
    return []  # This line should never be reached due to the raise in the loop


def contextual_process_chunk(doc_chunk: str, lang, file_name, progress_tracker):
    global upload_progress, model, tokenizer
    llm = 'ollama'
    
    try:
        
        llm_client = get_llm_client(llm)
        
        logger.info(f"Starting to process chunk for file: {file_name}, language: {lang}")
        logger.debug(f"Document Chunk size: {len(doc_chunk)} characters")
        
        chunk_texts = split_document(doc_chunk, max_length=512, lang=lang)
        
        batch = []
        for chunk_text in chunk_texts:
            # Generate contextual text
            contextual_text = situate_context(doc_chunk, llm_client, llm, chunk_text, lang)
            
            # Combine chunk text with contextual text
            combined_text = f"{chunk_text}\n\n{contextual_text}"
            
            with model_lock:                
                chunk_embedding = generate_query_embedding(combined_text)
            
            batch.append((file_name, chunk_text, chunk_embedding.tolist(), contextual_text))
        
        # Update progress
        upload_progress = progress_tracker.update()
        logger.info(f"Chunk processed. Progress: {upload_progress}%")
            
        return batch
        
    
    except Exception as e:
        # Log the error
        logger.error(f"Error in contextual_process_chunk: {str(e)}")
        raise  # Re-raise the exception to be handled by the caller
    
    
def process_file(content, lang, file_name, max_workers=3):
    global upload_progress, progress_tracker
    
    logger.info(f"Starting file processing for {file_name}...")
    logger.info(f"File size: {len(content)} bytes, Language: {lang}, Max workers: {max_workers}")
    
    document = content.decode("utf-8")
    if contextual_embedding:
        # looks only max 8192 tokens , ollma can work, when max_length 10240, ollama stop working
        document_chunks = split_document(document, max_length=10240, lang=lang)
    else:
        document_chunks = split_document(document, max_length=8192, lang=lang)
    
    total_chunks = len(document_chunks)
    logger.info(f"Document split into {total_chunks} chunks")
    
    progress_tracker = ProgressTracker(total_chunks)
    
    conn = psycopg2.connect(dbname=dbname, user=user, password=password, host=host, port=port)
    cursor = conn.cursor()
    
    try:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            logger.info(f"Starting ThreadPoolExecutor with {max_workers} workers")
            if contextual_embedding:
                future_to_chunk = {executor.submit(contextual_process_chunk, chunk, lang, file_name, progress_tracker): chunk for chunk in document_chunks}
            else:
                future_to_chunk = {executor.submit(process_chunk, chunk, lang, file_name, progress_tracker): chunk for chunk in document_chunks}
            
            for future in as_completed(future_to_chunk):
                try:
                    batch = future.result()
                    if batch:
                        logger.debug(f"Inserting batch of {len(batch)} records into database")
                        if contextual_embedding:
                            execute_values(cursor, """
                                INSERT INTO jinanovel (file_name, chunk_text, embedding, contextual_content)
                                VALUES %s
                            """, batch)
                        else:
                            execute_values(cursor, """
                                INSERT INTO jinanovel (file_name, chunk_text, embedding)
                                VALUES %s
                            """, batch)
                        conn.commit()
                        logger.debug("Batch inserted successfully")
                    else:
                        logger.warning("Empty batch returned, skipping database insertion")
                except Exception as e:
                    logger.error(f"Error processing chunk: {str(e)}")
                    conn.rollback()
                
                if device.type == "cuda":
                    torch.cuda.empty_cache()
                    logger.debug("CUDA cache cleared")
        
        # Add this new code at the end of the try block
        cursor.execute("""
            UPDATE jinanovel
            SET original_ctid = ctid
            WHERE file_name = %s
        """, (file_name,))
        conn.commit()
        
        logger.info(f"File processing completed for {file_name}. original_ctid updated.")
    except Exception as e:
        logger.error(f"Error processing file {file_name}: {str(e)}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()
        logger.info("Database connection closed")
    
    upload_progress = 100
    logger.info("File processing finished. Upload progress set to 100%")

@app.post("/upload")
async def upload_file(
    background_tasks: BackgroundTasks, 
    file: UploadFile = File(...), 
    lang: str = Query(..., pattern="^(zh|en|ja)$"),
    max_workers: int = Query(3, ge=1, le=10),
    contextual_embedding_query: bool = Query(False)
):
    global contextual_embedding, upload_progress
    contextual_embedding = contextual_embedding_query
    
    if not file:
        raise HTTPException(status_code=400, detail="Please upload a file")
    
    allowed_content_types = ["text/plain", "application/pdf"]
    if file.content_type not in allowed_content_types:
        return JSONResponse(
            status_code=422, 
            content={"detail": f"Unsupported file type: {file.content_type}. Please upload a .txt or .pdf file."}
        )
    
    try:
        content = await file.read()
        if len(content) == 0:
            raise HTTPException(status_code=400, detail="The uploaded file is empty")
        
        if PDF_METHOD == 'colpali' and file.content_type == "application/pdf":
            # Check if Colpali index already exists
            index_name = file.filename
            if index_name in ColpaliProcessor.get_indexes():
                upload_progress = 100
                return JSONResponse(
                    status_code=200,
                    content={"message": f"File {file.filename} already exists in the Colpali index."}
                )
            
            # Create Colpali index
            pdf_path = os.path.join(colpali_processor.base_dir, "pdfs", file.filename)
            os.makedirs(os.path.dirname(pdf_path), exist_ok=True)
            with open(pdf_path, "wb") as pdf_file:
                pdf_file.write(content)
            
            background_tasks.add_task(create_colpali_index, pdf_path, index_name)
            return {"message": "Colpali index creation started"}
        
        else:  # Legacy processing
            # Check if file already exists in the database
            conn = psycopg2.connect(dbname=dbname, user=user, password=password, host=host, port=port)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM jinanovel WHERE file_name = %s", (file.filename,))
            file_count = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            
            if file_count > 0:
                upload_progress = 100
                return JSONResponse(
                    status_code=200,
                    content={"message": f"File {file.filename} already exists in the database."}
                )
            
            # Reset upload_progress before starting a new task
            upload_progress = 0

            # Handle PDF files
            if file.content_type == "application/pdf":
                # Save the PDF file temporarily
                temp_pdf_path = os.path.join("/tmp", file.filename)
                with open(temp_pdf_path, "wb") as temp_file:
                    temp_file.write(content)
                
                # Process the PDF file
                ocr_provider = os.getenv("OCR_PROVIDER")
                ocr_model = os.getenv("OCR_MODEL")
                pdf_processor = PDFProcessor(provider=ocr_provider, model_name=ocr_model)
                extracted_text = pdf_processor.process_pdf(temp_pdf_path)
                
                # Clean up the temporary PDF file
                os.remove(temp_pdf_path)
                
                # Use the extracted text as content for further processing
                content = extracted_text.encode('utf-8')

            background_tasks.add_task(process_file, content, lang, file.filename, max_workers)
            return {"message": "File upload started"}

    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        return JSONResponse(
            status_code=500, 
            content={"detail": f"An error occurred while processing the file: {str(e)}"}
        )

def create_colpali_index(pdf_path: str, index_name: str):
    global upload_progress
    try:
        colpali_processor.create_index(pdf_path, index_name)
        upload_progress = 100
        logger.info(f"Colpali index created for {index_name}")
    except Exception as e:
        logger.error(f"Error creating Colpali index: {str(e)}")
        upload_progress = 0

@app.get("/progress")
async def get_progress():
    async def event_generator():
        global upload_progress
        while upload_progress < 100:
            await asyncio.sleep(1)
            yield f"data: {upload_progress}\n\n"  # Yield SSE formatted string for progress
        yield f"event: complete\ndata: Upload complete\n\n"  # Yield SSE formatted string for completion
    return StreamingResponse(event_generator(), media_type="text/event-stream")

async def evaluate_relevance(query: str, results: list, llm_client, provider: str, threshold: int = 2):
    relevant_results = []
    for result in results:
        prompt = f"""
        you are professional librarian who contains deep knowledge about books and world wide knowledge.
        On a scale of 1 to 5, how relevant is the following text to the query based on your real world knowledge.
        Higher scores are more relevant.
        
        Query: '{query}'
        Text: {result['chunk_text']}
        
        Provide only the numeric score without any explanation.
        """
        
        try:
            if provider == "openai":
                response = llm_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": prompt}]
                )
                score = float(response.choices[0].message.content.strip())
            elif provider == "claude":
                response = llm_client.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=10,
                    temperature=0,
                    messages=[{"role": "user", "content": prompt}]
                )
                score = float(response.content[0].text.strip())
            elif provider == "openrouter":
                response = llm_client.chat.completions.create(
                    model="openai/o1-mini",
                    messages=[{"role": "user", "content": prompt}]
                )
                score = float(response.choices[0].message.content.strip())
            elif provider == "ollama":
                response = llm_client.chat(
                    model='llama3.2',
                    messages=[{'role': 'user', 'content': prompt}]
                )
                score = float(response['message']['content'].strip())
            else:
                raise ValueError("Unsupported LLM provider for relevance evaluation.")
            
            if score >= threshold:
                result['relevance_score'] = score
                relevant_results.append(result)
        except Exception as e:
            logger.error(f"Error evaluating relevance: {str(e)}")
    
    return relevant_results


def jina_rerank_documents(query: str, documents: List[Dict], top_n: int, threshold: float) -> List[Dict]:
    url = 'https://api.jina.ai/v1/rerank'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {os.getenv("JINA_API_KEY")}'
    }
    
    # Prepare the documents for the API request
    api_documents = [doc['chunk_text'] for doc in documents]
    
    data = {
        "model": "jina-reranker-v2-base-multilingual",
        "query": query,
        "top_n": top_n,
        "documents": api_documents
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # Raise an exception for bad status codes
        
        results = response.json()['results']
        
        # Process and filter the results
        reranked_documents = []
        for result in results:
            if result['relevance_score'] >= threshold:
                original_doc = documents[result['index']]
                reranked_doc = {
                    'file_name': original_doc['file_name'],
                    'chunk_text': original_doc['chunk_text'],
                    'distance': original_doc.get('distance', 0),  # Include distance if available
                    'search_type': original_doc.get('search_type', 'unknown'),
                    'relevance_score': result['relevance_score'],
                    'url': original_doc.get('url', 'unknown')
                }
                reranked_documents.append(reranked_doc)
        
        return reranked_documents
    
    except requests.RequestException as e:
        logger.error(f"Error calling Jina AI reranking API: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error calling reranking API: {str(e)}")


def flash_rerank_documents(query: str, documents: List[Dict], top_n: int, threshold: float) -> List[Dict]:
    # Prepare the passages for FlashRank
    passages = [
        {
            "id": idx,
            "text": doc['chunk_text']
        }
        for idx, doc in enumerate(documents)
    ]

    # Create the RerankRequest
    rerank_request = RerankRequest(query=query, passages=passages)

    # Perform the reranking
    results = ranker.rerank(rerank_request)

    # Process and filter the results
    reranked_documents = []
    for result in results[:top_n]:  # Limit to top_n results
        if result['score'] >= threshold:
            original_doc = documents[result['id']]
            reranked_doc = {
                'file_name': original_doc['file_name'],
                'chunk_text': original_doc['chunk_text'],
                'distance': original_doc.get('distance', 0),  # Convert to native Python float
                'search_type': original_doc.get('search_type', 'unknown'),
                'relevance_score': float(result['score']),  # Convert to native Python float
                'url': original_doc.get('url', 'unknown')
            }
            reranked_documents.append(reranked_doc)

    return reranked_documents

def cohere_rerank_documents(query: str, documents: List[Dict], top_n: int, threshold: float) -> List[Dict]:
    try:
        # Initialize Cohere client
        co = cohere.ClientV2(os.getenv("COHERE_API_KEY"))
        
        # Prepare the documents for the API request
        api_documents = [doc['chunk_text'] for doc in documents]
        
        # Call Cohere rerank API
        response = co.rerank(
            model="rerank-multilingual-v3.0",
            query=query,
            documents=api_documents,
            top_n=top_n
        )
        
        # Process and filter the results
        reranked_documents = []
        for result in response.results:
            if result.relevance_score >= threshold:
                original_doc = documents[result.index]
                reranked_doc = {
                    'file_name': original_doc['file_name'],
                    'chunk_text': original_doc['chunk_text'],
                    'distance': original_doc.get('distance', 0),  # Include distance if available
                    'search_type': original_doc.get('search_type', 'unknown'),
                    'relevance_score': result.relevance_score,
                    'url': original_doc.get('url', 'unknown')
                }
                reranked_documents.append(reranked_doc)
        
        return reranked_documents
    
    except Exception as e:
        logger.error(f"Error calling Cohere reranking API: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error calling reranking API: {str(e)}")


def situate_context(doc: str, llm_client, llm: str, chunk: str, lang: str) -> str:
    
    if lang == "en":
        lang = "English"
    elif lang == "zh":
        lang = "Chinese"
    elif lang == "ja":
        lang = "Japanese"
    else:
        raise ValueError(f"Unsupported language: {lang}")   
        
    try:
        DOCUMENT_CONTEXT_PROMPT = """
        <document>
        {doc_content}
        </document>
        """

        CHUNK_CONTEXT_PROMPT = """
        Here is the chunk we want to situate within the whole document
        <chunk>
        {chunk_content}
        </chunk>
    
        Please give a short succinct context in {lang} to situate this chunk within the overall document for the purposes of improving search retrieval of the chunk.
        Answer only with the succinct context and nothing else.
        """
        
        if llm == "openai":
            response = llm_client.chat.completions.create(
                max_tokens=2048,
                temperature=0.0,
                model="gpt-4o-mini",
                messages=[
                    {"role": "user", "content": [
                        {
                            "type": "text",
                            "text": DOCUMENT_CONTEXT_PROMPT.format(doc_content=doc),
                        },
                        {
                            "type": "text",
                            "text": CHUNK_CONTEXT_PROMPT.format(chunk_content=chunk, lang=lang),
                        },
                    ]                        
                    }
                ]
            )
            context = response.choices[0].message.content
            
        elif llm == "claude":
            response =  llm_client.beta.prompt_caching.messages.create(
                model="claude-3-5-sonnet-20241022",                
                max_tokens=2048,
                temperature=0.0,
                messages=[{"role": "user", "content": [
                    {
                        "type": "text",
                        "text": DOCUMENT_CONTEXT_PROMPT.format(doc_content=doc),
                        "cache_control": {"type": "ephemeral"} #we will make use of prompt caching for the full documents
                    },
                    {
                        "type": "text",
                        "text": CHUNK_CONTEXT_PROMPT.format(chunk_content=chunk, lang=lang),
                    },
                ]}],
                extra_headers={"anthropic-beta": "prompt-caching-2024-07-31"}
            )
            context = response.content[0].text            
        elif llm == "openrouter":
            response = llm_client.chat.completions.create(
                model="openai/o1-mini",
                max_tokens=2048,
                temperature=0.0,
                messages=[
                    {"role": "user", "content": [
                        {
                            "type": "text",
                            "text": DOCUMENT_CONTEXT_PROMPT.format(doc_content=doc),
                        },
                        {
                            "type": "text",
                            "text": CHUNK_CONTEXT_PROMPT.format(chunk_content=chunk, lang=lang),
                        },
                    ]                        
                    }
                ]
            )
            context = response.choices[0].message.content
            
        elif llm == "ollama":
            prompt = DOCUMENT_CONTEXT_PROMPT.format(doc_content=doc) + CHUNK_CONTEXT_PROMPT.format(chunk_content=chunk, lang=lang)
            response = llm_client.chat(
                model="llama3.2",
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            context = response['message']['content']
        else:
            raise ValueError(f"Unsupported LLM provider: {llm}")

        return context
    except Exception as e:
        logger.error(f"Error situating context: {str(e)}")
        return ""


async def generate_keywords(query: str, llm_client, llm: str) -> List[str]:
    try:
        system_message = """You are an assistant that generates keywords for search queries. Output only 3-5 most important keywords, separated by commas, without any additional explanation or text. If a keyword contains spaces, enclose it in double quotes.
        Example:
        Query: How to make Italian pizza at home
        Output: "homemade pizza", "Italian cuisine", pizza
        
        """
        user_message = f"Generate keywords for the following query: {query}"        
        
        if llm == "openai":
            response = llm_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ]
            )
            raw_keywords = response.choices[0].message.content
            
        elif llm == "claude":
            response =  llm_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=2048,
                temperature=0.7,
                system=f"""{system_message}""",
                messages=[{"role": "user", "content": user_message}]
            )
            raw_keywords = response.content[0].text
            
        elif llm == "openrouter":
            response = llm_client.chat.completions.create(
                model="openai/o1-mini",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ]
            )
            raw_keywords = response.choices[0].message.content
            
        elif llm == "ollama":
            response = llm_client.chat(
                model="llama3.2",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ]
            )
            raw_keywords = response['message']['content']
        elif llm == "xunzi":
            # will setup system/user message specially for xunzi
            system_message = """请根据用户查询，生成3-5个关键词，用单字节逗号隔开，不要任何额外的解释或文本。如果关键词包含空格，请用双引号括起来。
            示例：
            查询：如何在家做意大利披萨
            输出："自制披萨", "意大利美食", "披萨"
            """
            user_message = f"请根据用户的查询，生成关键词：{query}"        
            
            messages = [
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_message}
            ]
            raw_keywords = llm_client.generate(messages)
        else:
            raise ValueError(f"Invalid LLM provider: {llm}")
        
        # 使用正则表达式分割关键词，保留现有的引号
        keywords = re.findall(r'"[^"]+"|[^,]+', raw_keywords)
        
        # 处理每关键词
        processed_keywords = []
        for keyword in keywords:
            keyword = keyword.strip()  # 移除前后空格
            if keyword.startswith('"') and keyword.endswith('"'):
                # 已经有引号的关键词，移除引号后处理，然后重新添加引号
                inner_keyword = keyword[1:-1].strip()
                processed_keywords.append(f'"{inner_keyword}"')
            elif ' ' in keyword:
                # 包含空格但没有引号的关键词，添加引号
                processed_keywords.append(f'"{keyword}"')
            else:
                # 不包含空格的关键词，保持不变
                processed_keywords.append(keyword)
        
        return processed_keywords
    except Exception as e:
        logger.error(f"Error generating keywords: {str(e)}")
        return []


def get_db_connection():
    return psycopg2.connect(dbname=dbname, user=user, password=password, host=host, port=port)

def execute_search_query(cursor, sql: str, params: Tuple, search_type: str) -> List[Dict[str, Any]]:
    cursor.execute(sql, params)
    return [
        {
            "file_name": row[0],
            "chunk_text": re.sub(r'[\n\r\u3000]+', ' ', row[1]).strip(),
            "distance": float(row[2]),
            "search_type": search_type
        }
        for row in cursor.fetchall()
    ]

def get_search_sql(full_docs_search: bool, search_type: str) -> str:
    common_sql = """
    WITH matched_chunks AS (
        SELECT original_ctid, file_name, chunk_text, contextual_content, {distance_func} AS distance
        FROM jinanovel
        {where_clause}
        ORDER BY distance {order}
        LIMIT %s
    ),
    chunk_with_neighbors AS (
        SELECT 
            mc.original_ctid as match_original_ctid,
            mc.file_name,
            mc.distance,
            j.original_ctid,
            j.chunk_text,
            j.contextual_content,
            ROW_NUMBER() OVER (PARTITION BY mc.original_ctid ORDER BY j.original_ctid) as rn
        FROM matched_chunks mc
        JOIN jinanovel j ON 
            j.file_name = mc.file_name
            AND (j.original_ctid::text::point)[0] = (mc.original_ctid::text::point)[0] 
            AND ABS((j.original_ctid::text::point)[1] - (mc.original_ctid::text::point)[1]) <= 1
    )
    SELECT 
        file_name,
        string_agg(chunk_text, E'\n\n' ORDER BY original_ctid) as combined_text,
        distance
    FROM chunk_with_neighbors
    WHERE rn <= 3
    GROUP BY match_original_ctid, file_name, distance
    ORDER BY distance {order}
    """
    
    distance_func = "embedding <=> %s::vector(1024)" if search_type == "vector" else "pgroonga_score(tableoid, ctid)"
    order = "ASC" if search_type == "vector" else "DESC"
    where_clause = "WHERE (chunk_text &@~ %s OR contextual_content &@~ %s)" if search_type == "fulltext" else ""
    
    if not full_docs_search:
        where_clause += " AND " if where_clause else "WHERE "
        where_clause += "file_name = %s"
    
    return common_sql.format(distance_func=distance_func, where_clause=where_clause, order=order)

def get_llm_client(llm: str):
    global xunzi_llm
    
    try:
        if llm == "openai":            
            return openai.OpenAI(api_key=getenv("OPENAI_API_KEY"),
                                 base_url="https://oai.helicone.ai/v1",
                                 default_headers={"Helicone-Auth": f"Bearer {getenv('HELICONE_API_KEY')}"})
        elif llm == "claude":
            anthropic_key = getenv("ANTHROPIC_API_KEY")
            helicone_key = getenv("HELICONE_API_KEY")
            # print(f"anthropic_key: {anthropic_key}, helicone_key: {helicone_key}")
            # return anthropic.Anthropic(api_key=anthropic_key)           
            return anthropic.Anthropic(api_key=anthropic_key,
                                       base_url="https://anthropic.helicone.ai",
                                       default_headers={"Helicone-Auth": f"Bearer {helicone_key}"})
        elif llm == "openrouter":
            return openai.OpenAI(base_url="https://openrouter.ai/api/v1", api_key=getenv("OPENROUTER_API_KEY"))
        elif llm == "ollama":
            return ollama.Client(host='http://************:8234')
        elif llm == "xunzi":
            if xunzi_llm is None:
                xunzi_llm = XunziLLM()
            return xunzi_llm
        else:
            raise ValueError(f"Invalid LLM provider: {llm}")
    except Exception as e:
        logger.error(f"Error initializing LLM client: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error initializing LLM client: {str(e)}")

@app.post("/query")
async def query(
    query: str = Body(...), 
    k: int = Body(10), 
    file_name: str = Body("claude-mcp-call"), 
    llm: str = Body("claude"), 
    threshold: int = Body(3),
    full_docs_search: bool = Body(True),
    rerank_method: str = Body("jina"),
    contextual_embedding_query: bool = Body(True),
    search_engine: str = Body("exa")
):
    global contextual_embedding
    global global_search_engine
    contextual_embedding = contextual_embedding_query
    
    try:
        # Initialize the appropriate LLM client based on the selected provider
        llm_client = get_llm_client(llm)

        # Generate embedding for the query
        query_vector = generate_query_embedding(query)
        vector_str = '[' + ','.join(map(str, query_vector)) + ']'

        # Generate keywords for full-text search
        if contextual_embedding:    
            # if contextual embedding is true, we only use the query as the search query
            keywords = [query]
        else:            
            keywords = await generate_keywords(query, llm_client, llm)
        search_query = ' OR '.join(keywords)

        logger.info(f"Query: {query}, File: {file_name if not full_docs_search else 'All'}, LLM: {llm}, Search Engine: {search_engine}")

        results = []
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                
                # Vector search
                vector_sql = get_search_sql(full_docs_search, "vector")
                vector_params = (vector_str, k) if full_docs_search else (vector_str, file_name, k)
                results.extend(execute_search_query(cursor, vector_sql, vector_params, "vector"))
                
                # Full-text search
                cursor.execute("SET enable_seqscan = off;")
                fulltext_sql = get_search_sql(full_docs_search, "fulltext")
                fulltext_params = (search_query, search_query, k) if full_docs_search else (search_query, search_query, file_name, k)
                results.extend(execute_search_query(cursor, fulltext_sql, fulltext_params, "fulltext"))

        # Add search engine results if a search engine is selected
        if search_engine != "none":
            search_results = global_search_engine.search(search_engine, query)
            results.extend(search_results)

        float_threshold = (threshold - 1) * 0.225
        rerank_func = (
            flash_rerank_documents if rerank_method == "flash" 
            else cohere_rerank_documents if rerank_method == "cohere"
            else jina_rerank_documents
        )        
        relevant_results = rerank_func(query, results, k, float_threshold)
        relevant_results = [{k: (float(v) if isinstance(v, np.floating) else v) for k, v in doc.items()} for doc in relevant_results]

        relevant_ids = set(doc['chunk_text'] for doc in relevant_results)
        other_results = [doc for doc in results if doc['chunk_text'] not in relevant_ids]
        combined_results = relevant_results + other_results

        concatenated_chunks = "\n".join([result["chunk_text"] for result in relevant_results])
        if file_name == "claude-mcp-call": # we don't need summary when the query is from claude-mcp-call
            summary = ""
        else:            
            summary = await generate_summary(query, concatenated_chunks, llm_client, llm)            
        return {
            "results": combined_results,
            "summary": summary,
            "relevant_count": len(relevant_results)
        }
    except Exception as e:
        logger.error(f"Error querying database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error querying database: {str(e)}")

async def generate_summary(query: str, text: str, llm_client, provider: str) -> str:
    try:
        if provider == "openai":
            response =  llm_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": f"""
                     Summarize the following text based on the query in the same language as the query. 
                     Query: {query}
                     """},
                    {"role": "user", "content": text}
                ]
            )
            summary = response.choices[0].message.content.strip()
        
        elif provider == "claude":
            response =  llm_client.messages.create(
                model="claude-3-7-sonnet-20250219",
                max_tokens=4000,
                # temperature=0.7,
                thinking= {
                    "type": "enabled",
                    "budget_tokens": 2000
                },
                system=f"""
                You will summarize the content provided based on the below query in the same language as the query. 
                Query: {query}
                """,
                messages=[{"role": "user", "content": text}]
            )
            # Extract thinking and text blocks from Claude response
            summary = ""
            for block in response.content:
                if block.type == "thinking":
                    summary += f"\n\n## 🧠 Thinking Process\n\n{block.thinking}\n\n"
                elif block.type == "text":
                    summary += f"\n\n## ✓ Final Answer\n\n{block.text}\n\n"
            
            summary = summary.strip()
        
        elif provider == "openrouter":
            response = llm_client.chat.completions.create(
                model="openai/o1-mini",
                messages=[
                    {"role": "system", "content": f"""
                    Summarize the following text based on the query in the same language as the query. 
                    Query: {query}
                    """},
                    {"role": "user", "content": text}
                ]
            )
            summary = response.choices[0].message.content.strip()
            
        elif provider == "ollama":
            response = llm_client.chat(
                model='llama3.2',
                messages=[
                    {
                        'role': 'user',
                        'content': f"""
                        Instructions:
                        1. Check Query and Document language.
                        2. If Query language and Document language are different, translate Query to Document language.
                        3. Summarize the Document based on the Query in the same language as the Document.
                        
                        Query: {query}
                        Document: {text}
                        
        
                        """
                    },
                ]
            )
            summary = response['message']['content'].strip()
        
        elif provider == "xunzi":
            messages = [
                {"role": "system", "content": f"""
                君乃中医大家，请根据询问，务必按照考证文档，用文言文回答患者。不知则不知，切勿胡编乱造。
                只对中医内容进行总结。不要输出西医相关内容。考证文档以外内容，请勿输出。现代文内容，请勿输出。与询问无关内容，请勿输出。
                """},
                {"role": "user", "content": f"""
                问：{query}
                考证：{text}
                
                用文言文总结回答：
                """}
            ]
            summary = llm_client.generate(messages)
        else:
            raise ValueError("Unsupported LLM provider for summarization.")
        
        logger.info(f"Generated summary: {summary}")
        return summary
    except Exception as e:
        logger.error(f"Error generating summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating summary: {str(e)}")

@app.get("/files")
async def get_files():
    try:
        conn = psycopg2.connect(dbname=dbname, user=user, password=password, host=host, port=port)
        cursor = conn.cursor()
        
        cursor.execute("SELECT DISTINCT file_name FROM jinanovel")
        files = [row[0] for row in cursor.fetchall()]
        
        cursor.close()
        conn.close()
        
        return {"files": files}
    except Exception as e:
        logger.error(f"Error fetching files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching files: {str(e)}")

@app.get("/colpali/indexes")
async def get_colpali_indexes():
    # Retrieve available indexes from ColpaliProcessor
    indexes = ColpaliProcessor.get_indexes()
    
    # Log available indexes for debugging purposes
    print(f"Available indexes: {indexes}")  # Debug log
    
    # Return the list of indexes as a JSON response
    return {"indexes": indexes}

@app.post("/colpali/query")
async def colpali_query(query_data: dict):
    # Extract query and index_name from the input data
    query = query_data.get('query')
    index_name = query_data.get('index_name')

    # Validate that both query and index_name are provided
    if not query or not index_name:
        raise HTTPException(status_code=400, detail="Missing query or index_name")

    # Load the specified index using the ColpaliProcessor
    colpali_processor.load_index(index_name)

    # Process the query using the loaded index and return the result
    result = colpali_processor.process_query(query)
    
    # Clean up memory
    colpali_processor.cleanup_memory()
    return result

@app.get("/health")
async def health_check():
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3201)