from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from termcolor import colored
from openai import OpenAI
import os
import json

router = APIRouter(tags=["mindmap"])
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
MODEL = "gpt-4o-mini"
SYSTEM_PROMPT = """You are a mindmap generator. Create a detailed hierarchical mindmap structure for the given topic. 
The response must be a valid JSON object with the following structure:
{
    "nodes": [
        {"id": "root", "label": "Main Topic", "level": 0},
        {"id": "1", "label": "Subtopic 1", "level": 1, "parent": "root"},
        ...
    ]
}
JSON format is required for processing."""

try:
    client = OpenAI()
    print(colored("OpenAI client initialized successfully for mindmap", "green"))
except Exception as e:
    print(colored(f"Error initializing OpenAI client for mindmap: {str(e)}", "red"))

@router.post("/generate")
async def generate_mindmap(request: Request):
    try:
        data = await request.json()
        topic = data.get("topic")
        print(colored(f"Generating mindmap for topic: {topic}", "cyan"))

        completion = client.chat.completions.create(
            model=MODEL,
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": f"Create a detailed mindmap about: {topic}"}
            ],
            response_format={"type": "json_object"}
        )

        mindmap_data = json.loads(completion.choices[0].message.content)
        print(colored("Mindmap generated successfully", "green"))
        return JSONResponse(content=mindmap_data)

    except Exception as e:
        print(colored(f"Error generating mindmap: {str(e)}", "red"))
        return JSONResponse(
            status_code=500,
            content={"error": "Failed to generate mindmap"}
        ) 