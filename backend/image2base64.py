import base64
import os

def image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
    return encoded_string

# 指定图片路径
image_path = "/opt/workspace/app/cursor/jina/backend/background.png"  # 替换为您的图片路径

# 获取文件扩展名
_, extension = os.path.splitext(image_path)
extension = extension.lstrip('.')

# 转换图片为base64
base64_string = image_to_base64(image_path)

# 构建完整的数据URL
data_url = f"data:image/{extension};base64,{base64_string}"

# Save to a file
output_file = "background_image.ts"
with open(output_file, "w") as f:
    f.write("export const BACKGROUND_IMAGE = `" + data_url + "`;")

print(f"Base64 encoded image has been saved to {output_file}")