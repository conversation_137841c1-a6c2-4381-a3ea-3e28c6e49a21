import asyncio
import json
import logging
import os
import argparse
import time
from functools import wraps
from typing import Dict, List, Any, Optional, Tuple, Callable, Union, Awaitable

import anthropic

from mcp import ClientSession
from mcp.client.sse import sse_client

# Export public classes for easier imports
__all__ = [
    "MCPClient", 
    "MCPServerConnection", 
    "MCPToolDefinition", 
    "MCPLLMIntegration", 
    "RateLimiter",
    "retry_with_exponential_backoff",
    "configure_logging"
]

# Rate limiter class for controlling request frequency
class RateLimiter:
    def __init__(self, max_requests=50, time_window=60):  # Default: 50 requests per minute
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        
    def can_make_request(self):
        now = time.time()
        # Clean up old request records
        self.requests = [req_time for req_time in self.requests if now - req_time < self.time_window]
        
        if len(self.requests) < self.max_requests:
            self.requests.append(now)
            return True
        return False
    
    async def wait_for_next_window(self):
        if self.requests:
            oldest_request = min(self.requests)
            sleep_time = self.time_window - (time.time() - oldest_request)
            if sleep_time > 0:
                logger.info(f"Rate limit reached, waiting {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)

# Global rate limiter instance
rate_limiter = RateLimiter()

def retry_with_exponential_backoff(max_retries=3, initial_wait=10):
    """Decorator that implements retry logic with exponential backoff for async functions.
    
    Args:
        max_retries: Maximum number of retry attempts
        initial_wait: Initial wait time in seconds (will be doubled each retry)
        
    Returns:
        Decorator function that wraps the target function with retry logic
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retries = 0
            last_error = None
            
            # Get reference to mcp_client or self.mcp_client if it exists in args
            mcp_client = None
            if args and hasattr(args[0], 'mcp_client'):
                # This is likely a method in MCPLLMIntegration
                mcp_client = args[0].mcp_client
            elif args and isinstance(args[0], MCPClient):
                # This is likely a method in MCPClient
                mcp_client = args[0]
            
            while retries <= max_retries:
                # Check rate limiter
                if not rate_limiter.can_make_request():
                    logger.info("Rate limit reached, waiting for next window")
                    await rate_limiter.wait_for_next_window()
                
                try:
                    return await func(*args, **kwargs)
                except anthropic.RateLimitError as e:
                    retries += 1
                    last_error = e
                    logger.warning(f"Rate limit error: {str(e)} (attempt {retries}/{max_retries+1})")
                    
                    if retries <= max_retries:
                        wait_time = initial_wait * (2 ** (retries - 1))  # Exponential backoff
                        logger.info(f"Retrying in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        
                        # Reconnect if we have access to mcp_client
                        if mcp_client:
                            logger.info("Reconnecting to MCP servers before retry...")
                            await refresh_connections(mcp_client)
                    else:
                        logger.error(f"Max retries reached after rate limit errors")
                        raise
                except Exception as e:
                    # Handle other exceptions
                    retries += 1
                    last_error = e
                    logger.warning(f"Error in execution: {str(e)} (attempt {retries}/{max_retries+1})")
                    
                    # Check if this is a connection issue
                    is_connection_error = any(err in str(e).lower() for err in ["connection", "timeout", "network", "closed", "eof"])
                    
                    if retries <= max_retries:
                        wait_time = initial_wait * (2 ** (retries - 1))  # Exponential backoff
                        logger.info(f"Retrying in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        
                        # Reconnect if we have access to mcp_client
                        if mcp_client:
                            logger.info("Reconnecting to MCP servers before retry...")
                            await refresh_connections(mcp_client)
                    else:
                        logger.error(f"Max retries reached")
                        raise last_error
            
            # This should not be reached due to the raise in the last iteration
            raise last_error if last_error else RuntimeError("Unknown error during retry")
        
        return wrapper
    
    return decorator

async def refresh_connections(mcp_client):
    """Helper function to refresh all MCP server connections.
    
    Args:
        mcp_client: An MCPClient instance
    """
    # Track which servers were reconnected
    reconnected_servers = []
    
    try:
        # First, check all servers for health
        all_servers = list(mcp_client.servers.items())
        for server_id, server in all_servers:
            # If already connected, check if connection is healthy
            if server.is_connected and server.session:
                try:
                    # Ping to check connection health
                    is_healthy = await server.session.ping()
                    if not is_healthy:
                        logger.info(f"Server {server_id} connection is not healthy")
                        await server.disconnect()
                        reconnected_servers.append(server_id)
                except Exception as e:
                    logger.warning(f"Health check failed for server {server_id}: {str(e)}")
                    await server.disconnect()
                    reconnected_servers.append(server_id)
            elif not server.is_connected:
                reconnected_servers.append(server_id)
    
        # Then reconnect any servers that need it
        for server_id in reconnected_servers:
            server = mcp_client.servers.get(server_id)
            if server:
                logger.info(f"Attempting to reconnect to server {server_id}")
                try:
                    # Wait a short time to avoid overwhelming the servers
                    await asyncio.sleep(0.5)
                    success = await server.connect()
                    if success:
                        logger.info(f"Successfully reconnected to server {server_id}")
                    else:
                        logger.warning(f"Failed to reconnect to server {server_id}")
                except Exception as e:
                    logger.warning(f"Error reconnecting to server {server_id}: {str(e)}")
        
        # If at least one server was reconnected, refresh tools cache
        if reconnected_servers:
            logger.info(f"Refreshing tools cache after reconnecting {len(reconnected_servers)} servers")
            try:
                await mcp_client.list_all_tools()
            except Exception as e:
                logger.warning(f"Error refreshing tools after reconnection: {str(e)}")
        
        # Return how many servers were reconnected
        return len(reconnected_servers)
        
    except Exception as e:
        logger.warning(f"Error refreshing MCP connections: {str(e)}")
        return 0

def configure_logging(log_to_file=True, log_to_console=False, log_level=logging.INFO, log_file_path=None):
    """Configure logging settings for the MCP client.
    
    Args:
        log_to_file: Whether to log to a file
        log_to_console: Whether to log to the console
        log_level: The logging level to use
        log_file_path: Optional custom path for the log file
    """
    # Create handlers list based on configuration
    handlers = []
    
    # Add file handler if requested
    if log_to_file:
        if log_file_path is None:
            log_directory = os.path.join(os.path.dirname(os.path.abspath(__file__)), "log")
            # Create logs directory if it doesn't exist
            os.makedirs(log_directory, exist_ok=True)
            log_file_path = os.path.join(log_directory, "mcp_client.log")
        else:
            # Ensure the directory for the custom log file exists
            log_directory = os.path.dirname(os.path.abspath(log_file_path))
            os.makedirs(log_directory, exist_ok=True)
            
        handlers.append(logging.FileHandler(log_file_path, encoding="utf-8"))
    
    # Add console handler if requested
    if log_to_console:
        handlers.append(logging.StreamHandler())
    
    # Configure logging with the specified handlers
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        handlers=handlers,
        force=True  # Force reconfiguration if already configured
    )
    
    return log_file_path  # Return the log file path for reference

# Set up default logging configuration - only to file, not to console
log_file_path = configure_logging(log_to_file=True, log_to_console=False)
logger = logging.getLogger("mcp_client")

class MCPToolDefinition:
    """Class representing a tool definition from an MCP server."""
    
    def __init__(self, server_id: str, name: str, description: str, input_schema: Dict[str, Any]):
        self.server_id = server_id
        self.name = name
        self.description = description
        self.input_schema = input_schema
        
    def __repr__(self) -> str:
        return f"Tool(server={self.server_id}, name={self.name})"
        
    def format_for_llm(self) -> Dict[str, Any]:
        """Format the tool definition for use with LLMs."""
        return {
            "name": self.name,
            "description": self.description,
            "input_schema": self.input_schema
        }

class MCPServerConnection:
    """Class to manage a single MCP server connection."""
    
    def __init__(self, server_id: str, server_url: str, keep_alive_interval: int = 30):
        self.server_id = server_id
        self.server_url = server_url
        self.session: Optional[ClientSession] = None
        self.tools_cache: List[MCPToolDefinition] = []
        self.is_connected = False
        self._connection_task = None
        self._keep_alive_task = None
        self._last_successful_operation = 0  # Track when the last successful operation happened
        self.keep_alive_interval = keep_alive_interval  # Seconds between keep-alive pings
        
    async def _connection_handler(self):
        """Handle the entire connection lifecycle in a single task."""
        try:
            logger.info(f"Starting connection to MCP server {self.server_id} at {self.server_url}")
            
            # Use single async with pattern as in the examples
            async with sse_client(self.server_url) as streams:
                read_stream, write_stream = streams
                
                async with ClientSession(read_stream, write_stream) as session:
                    # Add ping method to session object dynamically
                    session.ping = lambda: self._ping_session(session)
                    
                    # Store reference to session for tool calls
                    self.session = session
                    self.is_connected = True
                    self._last_successful_operation = time.time()
                    
                    # Initialize session
                    await session.initialize()
                    logger.info(f"Successfully connected to MCP server {self.server_id}")
                    
                    # Cache tools
                    await self._refresh_tools_cache_internal(session)
                    
                    # Start the keep-alive task
                    self._start_keep_alive_task()
                    
                    # Wait until disconnection is requested
                    while self.is_connected:
                        try:
                            # Process incoming messages
                            message = await asyncio.wait_for(anext(session.incoming_messages.__aiter__()), 1.0)
                            if isinstance(message, Exception):
                                logger.error(f"Error from server {self.server_id}: {message}")
                            else:
                                logger.debug(f"Message from server {self.server_id}: {message}")
                                self._last_successful_operation = time.time()
                        except asyncio.TimeoutError:
                            # This is expected, just a way to periodically check is_connected
                            pass
                        except StopAsyncIteration:
                            logger.warning(f"Message stream ended for server {self.server_id}")
                            self.is_connected = False
                            break
                        except Exception as e:
                            logger.error(f"Error processing messages for {self.server_id}: {str(e)}")
                            # If we encounter an error, mark as disconnected but don't break
                            # This allows cleanup to happen through the async with blocks
                            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                                self.is_connected = False
        
        except Exception as e:
            logger.error(f"Connection error for server {self.server_id}: {str(e)}")
        finally:
            # Clean up state
            self._stop_keep_alive_task()
            self.session = None
            self.is_connected = False
            logger.info(f"Disconnected from MCP server {self.server_id}")
    
    def _start_keep_alive_task(self):
        """Start the keep-alive background task."""
        if self._keep_alive_task is None or self._keep_alive_task.done():
            self._keep_alive_task = asyncio.create_task(self._keep_alive_loop())
            logger.info(f"Started keep-alive task for server {self.server_id}")
    
    def _stop_keep_alive_task(self):
        """Stop the keep-alive background task."""
        if self._keep_alive_task and not self._keep_alive_task.done():
            self._keep_alive_task.cancel()
            logger.info(f"Stopped keep-alive task for server {self.server_id}")
    
    async def _keep_alive_loop(self):
        """Background task to periodically ping the server to keep the connection alive."""
        try:
            logger.info(f"Keep-alive loop started for server {self.server_id} (interval: {self.keep_alive_interval}s)")
            while self.is_connected and self.session:
                try:
                    # Wait for the specified interval
                    await asyncio.sleep(self.keep_alive_interval)
                    
                    # Check if we're still connected
                    if not self.is_connected or not self.session:
                        logger.debug(f"Connection already closed for {self.server_id}, stopping keep-alive loop")
                        break
                    
                    # Only ping if no recent activity
                    current_time = time.time()
                    time_since_last_op = current_time - self._last_successful_operation
                    
                    if time_since_last_op >= self.keep_alive_interval:
                        logger.debug(f"Sending keep-alive ping to server {self.server_id}")
                        is_healthy = await self.session.ping()
                        
                        if is_healthy:
                            logger.debug(f"Keep-alive ping successful for server {self.server_id}")
                            self._last_successful_operation = current_time
                        else:
                            logger.warning(f"Keep-alive ping failed for server {self.server_id}")
                    else:
                        logger.debug(f"Skipping keep-alive ping for {self.server_id} (recent activity {time_since_last_op:.1f}s ago)")
                except asyncio.CancelledError:
                    logger.debug(f"Keep-alive task cancelled for server {self.server_id}")
                    break
                except Exception as e:
                    logger.warning(f"Error in keep-alive ping for server {self.server_id}: {str(e)}")
                    # Don't break the loop on errors, try again next interval
                    
        except asyncio.CancelledError:
            logger.debug(f"Keep-alive loop cancelled for server {self.server_id}")
        except Exception as e:
            logger.error(f"Error in keep-alive loop for server {self.server_id}: {str(e)}")
    
    async def _ping_session(self, session) -> bool:
        """Check if the session is alive by performing a lightweight operation.
        
        Instead of making a direct ping (which might not be supported by all MCP servers),
        we check if we've recently had successful communication or try to list tools.
        
        Returns:
            bool: True if the session seems healthy, False otherwise
        """
        # If we had a successful operation in the last 5 seconds, consider the connection healthy
        if (time.time() - self._last_successful_operation) < 5:
            return True
            
        try:
            # Try to list tools as a lightweight operation to check connection
            await session.list_tools()
            self._last_successful_operation = time.time()
            return True
        except Exception as e:
            logger.warning(f"Ping failed for server {self.server_id}: {str(e)}")
            return False
    
    async def connect(self) -> bool:
        """Connect to the MCP server.
        
        Returns:
            bool: True if connection is successful, False otherwise.
        """
        # If already connected, first ensure it's really working
        if self.is_connected and self.session:
            try:
                # Try a simple operation to verify the connection is healthy
                await self.session.ping()
                return True
            except Exception as e:
                logger.warning(f"Existing connection to {self.server_id} seems broken: {str(e)}")
                # Connection is not healthy, clean up
                await self.disconnect()
                
        # If we have an existing connection task, cancel it before starting a new one
        if self._connection_task and not self._connection_task.done():
            try:
                self._connection_task.cancel()
                try:
                    await asyncio.wait_for(self._connection_task, timeout=2.0)
                except (asyncio.TimeoutError, asyncio.CancelledError):
                    pass
            except Exception as e:
                logger.warning(f"Error cancelling previous connection task for {self.server_id}: {str(e)}")
                
        # Reset state
        self.is_connected = False
        self.session = None
        
        # Start connection handler in a separate task
        try:
            self._connection_task = asyncio.create_task(self._connection_handler())
            
            # Wait for connection to be established or fail
            for attempt in range(15):  # Wait up to 15 seconds
                if self.is_connected and self.session:
                    return True
                await asyncio.sleep(1.0)
                
            # If we got here, connection didn't succeed in time
            if self._connection_task and not self._connection_task.done():
                # Try to cancel the task if it's still running
                try:
                    self._connection_task.cancel()
                except Exception:
                    pass
                    
            return False
            
        except Exception as e:
            logger.error(f"Error initiating connection to {self.server_id}: {str(e)}")
            return False
    
    async def _refresh_tools_cache_internal(self, session) -> List[MCPToolDefinition]:
        """Internal method to refresh tools cache with provided session."""
        try:
            # List available tools
            tools_response = await session.list_tools()
            
            # Convert to MCPToolDefinition objects
            self.tools_cache = [
                MCPToolDefinition(
                    server_id=self.server_id,
                    name=tool.name,
                    description=tool.description or f"Tool from {self.server_id}",
                    input_schema=tool.inputSchema
                )
                for tool in tools_response.tools
            ]
            
            logger.info(f"Retrieved {len(self.tools_cache)} tools from server {self.server_id}")
            return self.tools_cache
            
        except Exception as e:
            logger.error(f"Failed to list tools for server {self.server_id}: {str(e)}")
            return []
    
    async def refresh_tools_cache(self) -> List[MCPToolDefinition]:
        """Refresh the tools cache for this server.
        
        Returns:
            List[MCPToolDefinition]: List of available tools.
        """
        if not self.is_connected or not self.session:
            logger.warning(f"Cannot refresh tools for disconnected server {self.server_id}")
            return self.tools_cache
            
        try:
            # List available tools using the stored session
            tools_response = await self.session.list_tools()
            
            # Convert to MCPToolDefinition objects
            self.tools_cache = [
                MCPToolDefinition(
                    server_id=self.server_id,
                    name=tool.name,
                    description=tool.description or f"Tool from {self.server_id}",
                    input_schema=tool.inputSchema
                )
                for tool in tools_response.tools
            ]
            
            logger.info(f"Retrieved {len(self.tools_cache)} tools from server {self.server_id}")
            return self.tools_cache
            
        except Exception as e:
            logger.error(f"Failed to list tools for server {self.server_id}: {str(e)}")
            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                self.is_connected = False
            return self.tools_cache
    
    @retry_with_exponential_backoff(max_retries=3, initial_wait=20)
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on this MCP server.
        
        Args:
            tool_name: Name of the tool to call
            params: Parameters to pass to the tool
            
        Returns:
            Tool response as a dictionary
        """
        if not self.is_connected or not self.session:
            try:
                await self.connect()
            except Exception as e:
                return {
                    "success": False,
                    "server_id": self.server_id,
                    "tool": tool_name,
                    "error": f"Cannot connect to server: {str(e)}"
                }
                
            if not self.is_connected or not self.session:
                return {
                    "success": False,
                    "server_id": self.server_id,
                    "tool": tool_name,
                    "error": "Failed to establish connection to server"
                }
        
        try:
            logger.info(f"Executing tool {tool_name} on server {self.server_id}")
            
            result = await self.session.call_tool(tool_name, params)
            
            # Format response for easier consumption
            if hasattr(result, 'content') and len(result.content) > 0:
                # Handle all content items, not just the first one
                all_text_content = []
                for content_item in result.content:
                    if hasattr(content_item, 'text'):
                        all_text_content.append(content_item.text)
                    else:
                        all_text_content.append(str(content_item))
                
                # Join all content items with separators for clarity
                text_content = "\n\n---\n\n".join(all_text_content)
            else:
                text_content = str(result)
            
            return {
                "success": True,
                "server_id": self.server_id,
                "tool": tool_name,
                "content": text_content,
                "raw_result": result
            }
            
        except Exception as e:
            # Check if this might be a connection issue
            if "connection" in str(e).lower() or "timeout" in str(e).lower() or "429" in str(e):
                logger.warning(f"Connection issue detected for server {self.server_id}: {str(e)}")
                self.is_connected = False
                
                # Try to reconnect before letting the decorator handle the retry
                try:
                    await self.disconnect()
                    await self.connect()
                except Exception as reconnect_error:
                    logger.warning(f"Reconnection attempt failed: {str(reconnect_error)}")
                
            # Raise to let the retry decorator handle it
            raise
    
    async def disconnect(self) -> None:
        """Disconnect from the MCP server and clean up resources."""
        if not self.is_connected:
            return
            
        logger.info(f"Disconnecting from server {self.server_id}")
        
        # Stop the keep-alive task first
        self._stop_keep_alive_task()
        
        # Signal the connection handler to stop
        self.is_connected = False
        
        # Wait for connection handler to finish
        if self._connection_task:
            try:
                # Wait with timeout to avoid hanging
                await asyncio.wait_for(self._connection_task, timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(f"Timeout waiting for connection handler to stop for {self.server_id}")
                # Force cancel the task
                self._connection_task.cancel()
            except Exception as e:
                logger.warning(f"Error waiting for connection handler: {str(e)}")
            
            self._connection_task = None
        
        # The connection handler's finally block will reset state
        logger.info(f"Disconnected from MCP server {self.server_id}")

class MCPClient:
    """Main MCP client class that manages multiple server connections."""
    
    def __init__(self, config_path: str = "mcp.json", keep_alive_interval: int = 30):
        """Initialize the MCP client.
        
        Args:
            config_path: Path to the MCP configuration file
            keep_alive_interval: Seconds between keep-alive pings (default: 30)
        """
        self.config_path = config_path
        self.servers: Dict[str, MCPServerConnection] = {}
        self.config: Dict[str, Any] = {}
        self.keep_alive_interval = keep_alive_interval
    
    @classmethod
    async def create(cls, config_path: str = "mcp.json", keep_alive_interval: int = 30) -> 'MCPClient':
        """Factory method to create and initialize an MCP client.
        
        This convenience method creates a client and initializes all server connections.
        
        Args:
            config_path: Path to the MCP configuration file
            keep_alive_interval: Seconds between keep-alive pings (default: 30)
            
        Returns:
            MCPClient: An initialized MCP client
            
        Example:
            ```python
            client = await MCPClient.create("mcp.json")
            tools = await client.list_all_tools()
            ```
        """
        client = cls(config_path, keep_alive_interval)
        try:
            await client.initialize()
            return client
        except Exception as e:
            # Clean up on initialization failure
            await client.close()
            raise RuntimeError(f"Failed to initialize MCP client: {str(e)}") from e
            
    def load_config(self) -> Dict[str, Any]:
        """Load the MCP configuration from file.
        
        Returns:
            Dict: The loaded configuration
        
        Raises:
            FileNotFoundError: If the configuration file doesn't exist
            json.JSONDecodeError: If the configuration file is invalid JSON
        """
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_path}")
                return self.config
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_path}")
            self.config = {"mcpServers": {}}
            raise
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON in configuration file: {self.config_path}")
            self.config = {"mcpServers": {}}
            raise
    
    async def initialize(self) -> None:
        """Initialize connections to all configured MCP servers."""
        # Load configuration if not already loaded
        if not self.config:
            self.load_config()
        
        # Get server configurations
        server_configs = self.config.get("mcpServers", {})
        
        if not server_configs:
            logger.warning("No MCP servers configured")
            return
        
        # Initialize each server connection
        for server_id, server_config in server_configs.items():
            if "url" not in server_config:
                logger.warning(f"Skipping server {server_id}: Missing URL")
                continue
                
            server_url = server_config["url"]
            # Pass the keep_alive_interval when creating connections
            self.servers[server_id] = MCPServerConnection(server_id, server_url, self.keep_alive_interval)
        
        # Connect to all servers concurrently
        connection_tasks = [
            self.servers[server_id].connect() 
            for server_id in self.servers
        ]
        
        # Wait for all connections (or failures)
        await asyncio.gather(*connection_tasks, return_exceptions=True)
        
        # Log connection summary
        connected_servers = sum(1 for server in self.servers.values() if server.is_connected)
        logger.info(f"Connected to {connected_servers}/{len(self.servers)} MCP servers")
    
    async def list_all_tools(self) -> List[MCPToolDefinition]:
        """Get a list of all tools across all connected servers.
        
        Returns:
            List[MCPToolDefinition]: List of all available tools
        """
        all_tools = []
        
        # Refresh tools cache for all servers concurrently
        refresh_tasks = [
            server.refresh_tools_cache() 
            for server in self.servers.values() 
            if server.is_connected
        ]
        
        # Wait for all refreshes
        await asyncio.gather(*refresh_tasks, return_exceptions=True)
        
        # Collect tools from all servers
        for server in self.servers.values():
            if server.is_connected:
                all_tools.extend(server.tools_cache)
        
        return all_tools
    
    async def pretty_tool_list(self) -> str:
        """Generate a markdown-formatted list of all available tools grouped by server.
        
        Returns:
            str: Markdown-formatted string with tools organized by server
        """
        # Get all tools
        tools = await self.list_all_tools()
        
        # Group tools by server_id
        tools_by_server = {}
        for tool in tools:
            if tool.server_id not in tools_by_server:
                tools_by_server[tool.server_id] = []
            tools_by_server[tool.server_id].append(tool.name)
        
        # Create markdown output
        markdown_output = "# Available MCP Tools\n\n"
        
        # Sort servers for consistent output
        for server_id in sorted(tools_by_server.keys()):
            markdown_output += f"## {server_id}\n\n"
            
            # Sort tools alphabetically
            for tool_name in sorted(tools_by_server[server_id]):
                markdown_output += f"- `{tool_name}`\n"
            
            markdown_output += "\n"
        
        return markdown_output
    
    async def find_tool(self, tool_name: str) -> Tuple[Optional[str], Optional[MCPToolDefinition]]:
        """Find a tool by name across all servers.
        
        Args:
            tool_name: Name of the tool to find
            
        Returns:
            Tuple[Optional[str], Optional[MCPToolDefinition]]: Server ID and tool definition,
            or (None, None) if the tool is not found
        """
        # Ensure tools are up to date
        await self.list_all_tools()
        
        for server_id, server in self.servers.items():
            if not server.is_connected:
                continue
                
            for tool in server.tools_cache:
                if tool.name == tool_name:
                    return server_id, tool
        
        return None, None
    
    @retry_with_exponential_backoff(max_retries=3, initial_wait=20)
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool by name on the appropriate server.
        
        Args:
            tool_name: Name of the tool to call
            params: Parameters to pass to the tool
            
        Returns:
            Dict: Tool execution result
            
        Raises:
            ValueError: If the tool is not found on any server
        """
        server_id, _ = await self.find_tool(tool_name)
        
        if not server_id:
            error_msg = f"Tool '{tool_name}' not found on any connected server"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        
        server = self.servers.get(server_id)
        if not server or not server.is_connected:
            error_msg = f"Server {server_id} is not connected"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        
        try:
            return await server.call_tool(tool_name, params)
        except Exception as e:
            # For clarity, translate some errors to user-friendly messages
            if "429" in str(e) or "rate limit" in str(e).lower():
                logger.warning(f"Rate limit reached when calling {tool_name}: {str(e)}")
                raise  # Let the decorator handle the retry
            elif "connection" in str(e).lower() or "timeout" in str(e).lower():
                logger.warning(f"Connection issue when calling {tool_name}: {str(e)}")
                raise  # Let the decorator handle the retry
            else:
                # Other errors are re-raised for the decorator to handle
                logger.error(f"Error calling tool {tool_name}: {str(e)}")
                raise
    
    async def format_tools_for_llm(self) -> List[Dict[str, Any]]:
        """Format all tools across all servers for use with LLMs.
        
        Returns:
            List[Dict[str, Any]]: List of formatted tools
        """
        all_tools = await self.list_all_tools()
        return [tool.format_for_llm() for tool in all_tools]
    
    async def close(self) -> None:
        """Close all server connections."""
        logger.info("Closing all MCP server connections")
        
        # Create a list to track close task exceptions
        close_exceptions = []
        
        # Close each server individually
        for server_id, server in self.servers.items():
            try:
                await server.disconnect()
            except Exception as e:
                error_msg = f"Error closing connection to server {server_id}: {str(e)}"
                logger.error(error_msg)
                close_exceptions.append(error_msg)
        
        # Log summary
        if close_exceptions:
            logger.warning(f"Encountered {len(close_exceptions)} errors while closing connections")
        else:
            logger.info("Successfully closed all MCP server connections")
            
    def create_llm_integration(self, api_key: str = None, model: str = "claude-3-7-sonnet-20250219", max_iterations: int = 10) -> 'MCPLLMIntegration':
        """Create an LLM integration with this client.
        
        This convenience method creates an MCPLLMIntegration object with this client.
        
        Args:
            api_key: Optional API key for Anthropic's API. If not provided, will use environment variables.
            model: The Claude model to use (default: claude-3-7-sonnet-20250219)
            max_iterations: Maximum number of tool call iterations (default: 10)
            
        Returns:
            MCPLLMIntegration: An LLM integration object
            
        Example:
            ```python
            client = await MCPClient.create("mcp.json")
            llm = client.create_llm_integration()
            response = await llm.process_messages([
                {"role": "user", "content": "Please search the web for..."}
            ])
            ```
        """
        return MCPLLMIntegration(self, llm_api_key=api_key, model=model, max_iterations=max_iterations)

class MCPLLMIntegration:
    """Class to integrate MCP tools with LLM services.
    
    This class provides methods to connect MCP tools with Anthropic's Claude API,
    allowing the LLM to use tools through the MCP protocol.
    """
    
    def __init__(self, mcp_client: 'MCPClient', llm_api_key: str = None, model: str = "claude-3-7-sonnet-20250219", max_iterations: int = 10):
        """Initialize the MCP-LLM integration.
        
        Args:
            mcp_client: The MCPClient instance
            llm_api_key: Optional API key for the Anthropic API. If not provided, it will try to use 
                         environment variables ANTHROPIC_API_KEY or LLM_API_KEY
            model: LLM model to use (default: claude-3-7-sonnet-20250219)
            max_iterations: Maximum number of tool call iterations (default: 10)
        """
        self.mcp_client = mcp_client
        self.api_key = llm_api_key or os.environ.get("ANTHROPIC_API_KEY", 
                      os.environ.get("LLM_API_KEY",""))
        self.model = model
        # Initialize the Anthropic client
        self.client = anthropic.Anthropic(api_key=self.api_key)
        # Add tool call history tracking
        self.tool_call_history = []
        # Add tool call callback
        self.tool_call_callback = None
        # Maximum number of tool call iterations
        self.max_iterations = max_iterations
    
    def register_tool_call_callback(self, callback: Callable[[Dict[str, Any]], Union[None, Awaitable[None]]]):
        """Register a callback function to be called when a tool is used.
        
        Args:
            callback: Function that takes a tool call result dictionary and handles it.
                     Can be a regular function or a coroutine function.
        """
        self.tool_call_callback = callback
    
    def get_tool_call_history(self) -> List[Dict[str, Any]]:
        """Get the history of all tool calls made during the conversation.
        
        Returns:
            List[Dict[str, Any]]: A list of tool call results
        """
        return self.tool_call_history
    
    def clear_tool_call_history(self):
        """Clear the tool call history."""
        self.tool_call_history = []
    
    async def _handle_tool_call_result(self, tool_name: str, tool_input: Dict[str, Any], 
                                      tool_id: str, tool_result: Dict[str, Any]):
        """Process a tool call result and trigger any registered callbacks.
        
        Args:
            tool_name: Name of the tool that was called
            tool_input: Parameters that were passed to the tool
            tool_id: ID of the tool use request
            tool_result: Result returned from the tool
        """
        # Create a structured record of the tool call
        tool_call_record = {
            "timestamp": asyncio.get_event_loop().time(),
            "tool_name": tool_name,
            "tool_input": tool_input,
            "tool_id": tool_id,
            "success": tool_result.get("success", False),
            "content": tool_result.get("content", ""),
            "error": tool_result.get("error", "") if not tool_result.get("success", False) else "",
            "raw_result": tool_result
        }
        
        # Add to history
        self.tool_call_history.append(tool_call_record)
        
        # Trigger callback if registered
        if self.tool_call_callback:
            if asyncio.iscoroutinefunction(self.tool_call_callback):
                await self.tool_call_callback(tool_call_record)
            else:
                self.tool_call_callback(tool_call_record)
    
    async def process_messages(self, messages: List[Dict[str, Any]], 
                              max_tokens: int = 8192,
                              thinking_budget: int = 2000) -> Dict[str, Any]:
        """Process messages using the LLM, handling any tool calls.
        
        This method sends messages to the LLM, processes any tool calls the LLM makes,
        and returns the final LLM response after all tool calls have been processed.
        
        Args:
            messages: List of message objects in the Anthropic message format
            max_tokens: Maximum tokens to generate in response
            thinking_budget: Budget for thinking tokens (set to None to disable thinking mode)
            
        Returns:
            Dict: The final LLM response with all content blocks
        """
        # Get tool definitions for LLM
        tool_definitions = await self.mcp_client.format_tools_for_llm()
        
        # Track full conversation history - don't filter system messages here
        # as the _send_to_llm method will handle them
        conversation_history = messages.copy()
        
        try:
            # Initial LLM request - thinking blocks will be included here
            logger.info("Making initial request with thinking mode enabled")
            response = await self._send_to_llm(conversation_history, tool_definitions, max_tokens, thinking_budget)
            
            # Handle tool calls (potentially multiple iterations)
            iteration = 0
            max_iterations = self.max_iterations  # Use instance variable instead of hardcoded value
            
            while response.get("stop_reason") == "tool_use" and iteration < max_iterations:
                iteration += 1
                logger.info(f"Tool use iteration {iteration}")
                
                # Extract tool use blocks
                tool_use_blocks = [block for block in response.get("content", []) 
                                  if block.get("type") == "tool_use"]
                
                # Create blocks for the assistant message in conversation history
                assistant_blocks = []
                
                # Always include ALL blocks from the response (including thinking blocks with signatures)
                # regardless of which iteration we're on
                for block in response.get("content", []):
                    # Only include thinking blocks if they have signatures
                    if block.get("type") in ["thinking", "redacted_thinking"]:
                        if "signature" in block:
                            logger.info("Including thinking block with signature in conversation history")
                            assistant_blocks.append(block)
                        else:
                            logger.warning("Thinking block without signature found - skipping")
                    elif block.get("type") == "tool_use":
                        assistant_blocks.append(block)
                    # Include text blocks as well
                    elif block.get("type") == "text":
                        logger.info("Including text block in conversation history")
                        assistant_blocks.append(block)
                
                # Add assistant response to conversation history
                if assistant_blocks:
                    conversation_history.append({
                        "role": "assistant",
                        "content": assistant_blocks
                    })
                
                # Process each tool use
                for tool_block in tool_use_blocks:
                    tool_name = tool_block.get("name")
                    tool_input = tool_block.get("input", {})
                    tool_id = tool_block.get("id")
                    
                    logger.info(f"Executing tool: {tool_name}")
                    print(f"\n\033[94m--> Executing tool: [{tool_name}] with input: {tool_input}\033[0m")   
                    
                    # Execute the tool
                    tool_result = await self.mcp_client.call_tool(tool_name, tool_input)
                    
                    # Process tool call result with new method
                    await self._handle_tool_call_result(tool_name, tool_input, tool_id, tool_result)
                    
                    # Format tool result for API
                    result_content = tool_result.get("content", "") if tool_result.get("success", False) else f"Error: {tool_result.get('error', 'Unknown error')}"
                    # print(f"\n\033[94m--> Tool result: {result_content}\033[0m")

                    # Add tool result to conversation
                    conversation_history.append({
                        "role": "user",
                        "content": [{
                            "type": "tool_result",
                            "tool_use_id": tool_id,
                            "content": json.dumps(result_content)
                        }]
                    })
                
                # ALWAYS keep thinking mode enabled for ALL calls
                # This is necessary if the conversation history contains thinking blocks
                logger.info("Making subsequent request with thinking mode still enabled")
                response = await self._send_to_llm(
                    conversation_history, 
                    tool_definitions, 
                    max_tokens,
                    thinking_budget
                )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in LLM processing: {str(e)}")
            return {"error": f"LLM processing failed: {str(e)}"}
    
    @retry_with_exponential_backoff(max_retries=3, initial_wait=20)
    async def _send_to_llm(self, messages: List[Dict[str, Any]], 
                          tools: List[Dict[str, Any]],
                          max_tokens: int = 4096,
                          thinking_budget: Optional[int] = 2000) -> Dict[str, Any]:
        """Send a request to the Anthropic Claude API using the official client.
        
        Args:
            messages: List of message objects
            tools: List of tool definitions
            max_tokens: Maximum tokens to generate
            thinking_budget: Budget for thinking tokens, or None to disable thinking
            
        Returns:
            Dict: The LLM response
        """
        # Extract system message if present
        system_message = None
        filtered_messages = []
        
        for msg in messages:
            if msg.get("role") == "system":
                system_message = msg.get("content")
                if isinstance(system_message, list):
                    # If content is a list of blocks, extract text content
                    system_message = " ".join([
                        block.get("text", "") 
                        for block in system_message 
                        if isinstance(block, dict) and block.get("type") == "text"
                    ])
            else:
                filtered_messages.append(msg)
        
        try:
            logger.info(f"Sending request to Anthropic API with {len(tools)} tools")
            
            # Create request parameters
            params = {
                "model": self.model,
                "messages": filtered_messages,
                "tools": tools,
                "max_tokens": max_tokens,
            }
            
            # Add thinking configuration if thinking_budget is provided
            if thinking_budget is not None:
                params["thinking"] = {
                    "type": "enabled",
                    "budget_tokens": thinking_budget
                }
                logger.info(f"Thinking mode enabled with budget: {thinking_budget} tokens")
            else:
                logger.info("Thinking mode disabled for this request")
            
            # Add system message if present
            if system_message:
                params["system"] = system_message
            
            # Add beta parameter for token-efficient-tools
            params["betas"] = ["token-efficient-tools-2025-02-19"]
            
            # Make the API call using the Anthropic client
            response = await asyncio.to_thread(
                self.client.beta.messages.create,
                **params
            )
            
            # Convert the response object to a dictionary for consistent handling
            response_dict = {
                "id": response.id,
                "model": response.model,
                "stop_reason": response.stop_reason,
                "content": [],
                "usage": {
                    "input_tokens": response.usage.input_tokens,
                    "output_tokens": response.usage.output_tokens
                } if hasattr(response, 'usage') else {}
            }
            
            # Convert content blocks to dictionaries
            for block in response.content:
                if block.type == "text":
                    response_dict["content"].append({
                        "type": "text",
                        "text": block.text
                    })
                elif block.type == "tool_use":
                    response_dict["content"].append({
                        "type": "tool_use",
                        "name": block.name,
                        "input": block.input,
                        "id": block.id
                    })
                elif block.type == "thinking":
                    # Make sure to include the signature for thinking blocks
                    thinking_block = {
                        "type": "thinking",
                        "thinking": block.thinking
                    }
                    # Only add signature if it exists in the original response
                    if hasattr(block, 'signature'):
                        thinking_block["signature"] = block.signature
                    response_dict["content"].append(thinking_block)
                elif block.type == "redacted_thinking":
                    response_dict["content"].append({
                        "type": "redacted_thinking"
                    })
            
            return response_dict
            
        except anthropic.BadRequestError as e:
            error_msg = f"Anthropic API error: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg, "details": str(e)}
        except anthropic.RateLimitError as e:
            # Let the decorator handle rate limit errors by re-raising them
            logger.warning(f"Anthropic API rate limit exceeded: {str(e)}")
            raise
        except anthropic.AuthenticationError as e:
            error_msg = f"Anthropic API authentication error: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg, "details": str(e)}
        except Exception as e:
            error_msg = f"Error sending request to Anthropic API: {str(e)}"
            logger.error(error_msg)
            raise  # Let the decorator handle it

    async def process_messages_stream(self, messages: List[Dict[str, Any]], 
                                max_tokens: int = 8192,
                                thinking_budget: int = 2000,
                                callback: Optional[Callable[[Any], Union[None, Awaitable[None]]]] = None) -> Dict[str, Any]:
        """Process messages using the LLM in streaming mode, handling any tool calls.
        
        This version uses the Claude streaming API to receive partial results as they're generated.
        A callback function can be provided to process stream events in real-time.
        
        Args:
            messages: List of message objects in the Anthropic message format
            max_tokens: Maximum tokens to generate in response
            thinking_budget: Budget for thinking tokens (set to None to disable thinking mode)
            callback: Optional callback function that receives stream events. Can be either a regular
                     function or a coroutine function.
            
        Returns:
            Dict: The final LLM response after processing all tool calls
        """
        # Get tool definitions for LLM
        tool_definitions = await self.mcp_client.format_tools_for_llm()
        
        # Track full conversation history - don't filter system messages here
        # as the _send_to_llm_stream method will handle them
        conversation_history = messages.copy()
        
        try:
            # Initial LLM request with streaming
            logger.info("Making initial streaming request with thinking mode enabled")
            response = await self._send_to_llm_stream(conversation_history, 
                                                      tool_definitions, 
                                                      max_tokens, 
                                                      thinking_budget,
                                                      callback)
            
            # Handle tool calls (potentially multiple iterations)
            iteration = 0
            max_iterations = self.max_iterations  # Use instance variable instead of hardcoded value
            
            while response.get("stop_reason") == "tool_use" and iteration < max_iterations:
                iteration += 1
                logger.info(f"Tool use iteration {iteration} (streaming mode)")
                
                # Extract tool use blocks
                tool_use_blocks = [block for block in response.get("content", []) 
                                  if block.get("type") == "tool_use"]
                
                # Create blocks for the assistant message in conversation history
                assistant_blocks = []
                
                # Always include ALL blocks from the response
                for block in response.get("content", []):
                    # Only include thinking blocks if they have signatures
                    if block.get("type") in ["thinking", "redacted_thinking"]:
                        if "signature" in block:
                            logger.info("Including thinking block with signature in conversation history")
                            assistant_blocks.append(block)
                        else:
                            logger.warning("Thinking block without signature found - skipping")
                    elif block.get("type") == "tool_use":
                        assistant_blocks.append(block)
                    # Include text blocks as well
                    elif block.get("type") == "text":
                        logger.info("Including text block in conversation history")
                        assistant_blocks.append(block)
                
                # Add assistant response to conversation history
                if assistant_blocks:
                    conversation_history.append({
                        "role": "assistant",
                        "content": assistant_blocks
                    })
                
                # Process each tool use
                for tool_block in tool_use_blocks:
                    tool_name = tool_block.get("name")
                    tool_input = tool_block.get("input", {})
                    tool_id = tool_block.get("id")
                    
                    logger.info(f"Executing tool: {tool_name}")
                    print(f"\n\033[94m--> Executing tool: [{tool_name}] with input: {tool_input}\033[0m")   
                    
                    # Execute the tool
                    tool_result = await self.mcp_client.call_tool(tool_name, tool_input)
                    
                    # Process tool call result with new method
                    await self._handle_tool_call_result(tool_name, tool_input, tool_id, tool_result)
                    
                    # Format tool result for API
                    result_content = tool_result.get("content", "") if tool_result.get("success", False) else f"Error: {tool_result.get('error', 'Unknown error')}"
                    # print(f"\n\033[94m--> Tool result: {result_content}\033[0m")
                    
                    # Add tool result to conversation
                    conversation_history.append({
                        "role": "user",
                        "content": [{
                            "type": "tool_result",
                            "tool_use_id": tool_id,
                            "content": json.dumps(result_content)
                        }]
                    })
                
                # Keep thinking mode enabled for subsequent requests
                logger.info("Making subsequent streaming request with thinking mode still enabled")
                response = await self._send_to_llm_stream(
                    conversation_history, 
                    tool_definitions, 
                    max_tokens,
                    thinking_budget,
                    callback
                )
            
            return response
            
        except Exception as e:
            logger.error(f"Error in LLM streaming processing: {str(e)}")
            return {"error": f"LLM streaming processing failed: {str(e)}"}
    
    @retry_with_exponential_backoff(max_retries=3, initial_wait=20)
    async def _send_to_llm_stream(self, messages: List[Dict[str, Any]], 
                                tools: List[Dict[str, Any]],
                                max_tokens: int = 4096,
                                thinking_budget: Optional[int] = 2000,
                                callback: Optional[Callable[[Any], Union[None, Awaitable[None]]]] = None) -> Dict[str, Any]:
        """Send a streaming request to the Anthropic Claude API.
        
        Args:
            messages: List of message objects
            tools: List of tool definitions
            max_tokens: Maximum tokens to generate
            thinking_budget: Budget for thinking tokens
            callback: Optional callback function that receives stream events
            
        Returns:
            Dict: The complete LLM response after processing the stream
        """
        # Extract system message if present
        system_message = None
        filtered_messages = []
        
        for msg in messages:
            if msg.get("role") == "system":
                system_message = msg.get("content")
                if isinstance(system_message, list):
                    # If content is a list of blocks, extract text content
                    system_message = " ".join([
                        block.get("text", "") 
                        for block in system_message 
                        if isinstance(block, dict) and block.get("type") == "text"
                    ])
            else:
                filtered_messages.append(msg)
        
        try:
            logger.info(f"Sending streaming request to Anthropic API with {len(tools)} tools")
            
            # Create request parameters
            params = {
                "model": self.model,
                "messages": filtered_messages,
                "tools": tools,
                "max_tokens": max_tokens,
            }
            
            # Add thinking configuration if thinking_budget is provided
            if thinking_budget is not None:
                params["thinking"] = {
                    "type": "enabled",
                    "budget_tokens": thinking_budget
                }
                logger.info(f"Thinking mode enabled with budget: {thinking_budget} tokens (streaming)")
            else:
                logger.info("Thinking mode disabled for this streaming request")
            
            # Add system message if present
            if system_message:
                params["system"] = system_message
            
            # Add beta parameter for token-efficient-tools
            params["betas"] = ["token-efficient-tools-2025-02-19"]
            
            # Prepare dictionary to store response
            response_dict = {
                "id": None,
                "model": self.model,
                "stop_reason": None,
                "content": [],
            }
            
            # Create content blocks dict to track blocks by index
            content_blocks = {}
            
            # Track the current block being constructed
            current_block_data = {}
            
            # Open the stream
            async def process_stream():
                # We need to use the with statement, but in async context
                try:
                    with self.client.beta.messages.stream(**params) as stream:
                        # Process stream events
                        for event in stream:
                            # Update callback if provided
                            if callback:
                                await callback(event)
                                
                            # Process different event types
                            if event.type == "message_start":
                                response_dict["id"] = event.message.id
                                response_dict["model"] = event.message.model
                                
                            elif event.type == "content_block_start":
                                # Initialize a new content block
                                block_index = event.index
                                block_type = event.content_block.type
                                
                                # Create starting structure for this block type
                                if block_type == "thinking":
                                    content_blocks[block_index] = {
                                        "type": "thinking",
                                        "thinking": "",
                                        "signature": ""
                                    }
                                elif block_type == "text":
                                    content_blocks[block_index] = {
                                        "type": "text",
                                        "text": "",
                                    }
                                elif block_type == "tool_use":
                                    # For tool_use blocks, collect JSON data progressively
                                    content_blocks[block_index] = {
                                        "type": "tool_use",
                                        # These will be populated as we receive deltas
                                        "name": "",
                                        "input": {},
                                        "id": "",
                                        # Raw JSON text for progressive building
                                        "_json_text": ""
                                    }
                                    
                            elif event.type == "content_block_delta":
                                # Update existing content block with delta
                                block_index = event.index
                                
                                if event.delta.type == "thinking_delta":
                                    content_blocks[block_index]["thinking"] += event.delta.thinking
                                elif event.delta.type == "text_delta":
                                    content_blocks[block_index]["text"] += event.delta.text
                                elif event.delta.type == "input_json_delta":
                                    # For tool use, we collect the JSON text and parse it at the end
                                    if "_json_text" in content_blocks[block_index]:
                                        content_blocks[block_index]["_json_text"] += event.delta.partial_json
                                    
                            elif event.type == "content_block_stop":
                                # Block is now complete
                                block_index = event.index
                                block_type = content_blocks[block_index]["type"]

                                # For thinking blocks, add signature if present
                                if block_type == "thinking":
                                    content_blocks[block_index]["signature"] = event.content_block.signature

                                # For tool_use blocks, parse the collected JSON text
                                if block_type == "tool_use" and "_json_text" in content_blocks[block_index]:
                                    try:
                                        tool_data = event.content_block
                                        # First try parsing as a valid JSON object
                                        try:
                                            # Update the block with parsed data
                                            content_blocks[block_index]["name"] = tool_data.name
                                            content_blocks[block_index]["input"] = tool_data.input
                                            content_blocks[block_index]["id"] = tool_data.id
                                        except json.JSONDecodeError:
                                            # If it's not valid JSON yet, extract data using regex or string operations
                                            # This is a fallback for partial JSON in streaming mode
                                            logger.warning(f"Could not parse tool use JSON: {content_blocks[block_index]['_json_text']}")
                                            # Set some defaults based on what we have
                                            content_blocks[block_index]["name"] = "unknown_tool"
                                            content_blocks[block_index]["input"] = {}
                                            
                                        # Remove the temporary field used during construction
                                        content_blocks[block_index].pop("_json_text", None)
                                    except Exception as e:
                                        logger.error(f"Error processing tool use block: {str(e)}")
                                
                            elif event.type == "message_delta":
                                # Update message metadata
                                if hasattr(event.delta, "stop_reason"):
                                    response_dict["stop_reason"] = event.delta.stop_reason
                
                except Exception as e:
                    logger.error(f"Error in stream processing: {str(e)}")
                    raise
            
            # Execute the streaming process
            await process_stream()
            
            # Convert tracked content blocks to final response format
            for idx in sorted(content_blocks.keys()):
                response_dict["content"].append(content_blocks[idx])
                
            # Add usage information if available
            # Note: usage stats might not be available in streaming mode
            
            return response_dict
            
        except anthropic.BadRequestError as e:
            error_msg = f"Anthropic API streaming error: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg, "details": str(e)}
        except anthropic.RateLimitError as e:
            # Let the decorator handle rate limit errors by re-raising them
            logger.warning(f"Anthropic API rate limit exceeded in streaming mode: {str(e)}")
            raise
        except anthropic.AuthenticationError as e:
            error_msg = f"Anthropic API authentication error in streaming mode: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg, "details": str(e)}
        except Exception as e:
            error_msg = f"Error sending streaming request to Anthropic API: {str(e)}"
            logger.error(error_msg)
            raise  # Let the decorator handle it


# Example usage
async def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="MCP Client with LLM Integration")
    parser.add_argument("--config", default="mcp.json", help="Path to MCP configuration file")
    parser.add_argument("--stream", action="store_true", default=True, help="Use streaming mode for LLM responses")
    parser.add_argument("--log-file", action="store_true", default=True, help="Log to file")
    parser.add_argument("--log-console", action="store_true", default=False, help="Log to console")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                        help="Logging level")
    parser.add_argument("--log-file-path", help="Custom path for the log file")
    
    args = parser.parse_args()
    
    # Configure logging based on command line arguments
    log_level = getattr(logging, args.log_level)
    log_file_path = configure_logging(
        log_to_file=args.log_file, 
        log_to_console=args.log_console, 
        log_level=log_level,
        log_file_path=args.log_file_path
    )
    
    # Use configuration from command line arguments
    config_path = args.config
    use_streaming_mode = args.stream
    
    print(f"Loading MCP configuration from {config_path}...")
    if args.log_file:
        print(f"Logging to file: {log_file_path}")
    print(f"Console logging: {'enabled' if args.log_console else 'disabled'}, level: {args.log_level}")
    print(f"Streaming mode: {'enabled' if use_streaming_mode else 'disabled'}")
    
    try:
        # Check if config file exists first
        if not os.path.exists(config_path):
            print(f"Error: Configuration file '{config_path}' not found.")
            print("Creating a sample config file...")
            
            # Create a sample config file
            sample_config = {
                "mcpServers": {
                    "local": {
                        "url": "http://localhost:8080/sse"
                    }
                }
            }
            
            with open(config_path, 'w', encoding="utf-8") as f:
                json.dump(sample_config, f, indent=2)
            
            print(f"Created sample config file at {config_path}. Please edit it with your actual MCP server details.")
            return
        
        # Create and initialize client in one step using the factory method    
        print("Initializing MCP client...")
        mcp_client = await MCPClient.create(config_path)
        
        # List all available tools
        print("\nRetrieving available tools...")
        all_tools = await mcp_client.list_all_tools()
        
        print(f"\nAvailable tools across all servers ({len(all_tools)} tools found):")
        for tool in all_tools:
            print(f"  - {tool.name} (from {tool.server_id}): {tool.description}")
        
        # Example: Call a tool
        if all_tools:
            # Use the first available tool as an example
            tool_name = all_tools[0].name
            print(f"\nCalling tool: {tool_name}")
            result = await mcp_client.call_tool(tool_name, {"query": "example query"})
            
            if result.get("success", False):
                print(f"\nTool result: {result.get('content')}")
            else:
                print(f"\nTool execution failed: {result.get('error')}")
        else:
            print("\nNo tools available to call")
        
        # Example: Integrate with LLM
        api_key = os.environ.get("ANTHROPIC_API_KEY", os.environ.get("LLM_API_KEY"))
        
        if api_key:
            print("\n=== Anthropic API Integration ===")
            print(f"Using API key: {api_key[:8]}...{api_key[-4:]}")
            
            # Create LLM integration using the convenience method
            llm_integration = mcp_client.create_llm_integration()
            
            # Example conversation with system message as top-level parameter
            messages = [
                {"role": "system", "content": "You are a helpful assistant with access to tools."},
                {"role": "user", "content": "Pls search the web for the latest news on AI and also check the weather in London"}
            ]
            
            if not use_streaming_mode:
                # NON-STREAMING MODE TEST
                print("\n=== TESTING NON-STREAMING MODE ===")
                print("\nSending LLM request with system message and thinking mode enabled...")
                response = await llm_integration.process_messages(messages)
                
                # Check for errors
                if "error" in response:
                    print(f"\nError in LLM response: {response.get('error')}")
                    if "details" in response:
                        print(f"Error details: {response.get('details')}")
                else:
                    print(f"\n=== RESPONSE INFORMATION ===")
                    print(f"Response ID: {response.get('id')}")
                    print(f"Model used: {response.get('model')}")
                    print(f"Stop reason: {response.get('stop_reason')}")
                    if "usage" in response:
                        usage = response.get("usage", {})
                        print(f"Token usage: {usage.get('input_tokens', 0)} input / {usage.get('output_tokens', 0)} output")
                    
                    print(f"\nTotal content blocks: {len(response.get('content', []))}")
                    thinking_blocks = [b for b in response.get('content', []) if b.get('type') == 'thinking']
                    tool_blocks = [b for b in response.get('content', []) if b.get('type') == 'tool_use']
                    text_blocks = [b for b in response.get('content', []) if b.get('type') == 'text']
                    
                    print(f"Thinking blocks: {len(thinking_blocks)}")
                    print(f"Tool use blocks: {len(tool_blocks)}")
                    print(f"Text blocks: {len(text_blocks)}")
                    
                    print("\n=== RESPONSE CONTENT ===")
                    for i, content_item in enumerate(response.get("content", [])):
                        if content_item.get("type") == "text":
                            print(f"\n[TEXT BLOCK {i+1}]\n{content_item.get('text')}")
                        elif content_item.get("type") == "thinking":
                            print(f"\n[THINKING BLOCK {i+1}]")
                            thinking_content = content_item.get("thinking", "")
                            print(f"{thinking_content[:200]}..." if len(thinking_content) > 200 else thinking_content)
                            if "signature" in content_item:
                                print(f"Signature present: Yes (begins with {content_item['signature'][:20]}...)")
                            else:
                                print("Signature present: No")
                        elif content_item.get("type") == "tool_use":
                            print(f"\n[TOOL USE BLOCK {i+1}]")
                            print(f"Tool: {content_item.get('name')}")
                            print(f"Input: {json.dumps(content_item.get('input'), indent=2)}")
                            print(f"ID: {content_item.get('id')}")
                    print("\n=== END RESPONSE CONTENT ===")
            else:
                # STREAMING MODE TEST
                print("\n=== TESTING STREAMING MODE ===")
                print("\nSending LLM request with system message and thinking mode enabled (STREAMING)...")
                
                # Define a streaming callback to process events in real-time
                async def stream_callback(event):
                    """Process streaming events in real-time."""
                    # Use nonlocal to track the current block across callback invocations
                    nonlocal current_block, current_block_type
                    
                    if event.type == "content_block_start":
                        # Start collecting a new block
                        current_block_type = event.content_block.type
                        current_block = ""
                        
                        # Print differently based on block type
                        if current_block_type == "thinking":
                            print(f"\n\033[90m--> Starting thinking block...\033[0m")
                        elif current_block_type == "tool_use":
                            print(f"\n\033[94m--> Preparing tool parameters ...\033[0m")
                    
                    elif event.type == "content_block_delta":
                        # Add to the current block content
                        if event.delta.type == "thinking_delta":
                            current_block += event.delta.thinking
                            # Print thinking content in gray/dim
                            print(f"\033[90m{event.delta.thinking}\033[0m", end="", flush=True)
                        elif event.delta.type == "text_delta":
                            current_block += event.delta.text
                            # Print text content in normal color
                            print(f"\033[0m{event.delta.text}\033[0m", end="", flush=True)
                        elif event.delta.type == "input_json_delta":
                            # For tool use deltas, collect and print in blue
                            current_block += event.delta.partial_json
                            print(f"\033[94m{event.delta.partial_json}\033[0m", end="", flush=True)
                            
                    elif event.type == "content_block_stop":
                        # Block is complete, process it based on its type
                        if current_block_type == "thinking":
                            print(f"\n\033[90m--> Thinking block complete.\033[0m")
                        
                        # Reset tracking
                        current_block = ""
                        current_block_type = None
                
                # Initialize variables to track blocks across callbacks
                current_block = ""
                current_block_type = None
                
                # Call streaming version with callback
                response = await llm_integration.process_messages_stream(
                    messages, 
                    callback=stream_callback
                )
                
                # After streaming completes, show summary info
                if "error" in response:
                    print(f"\nError in streaming response: {response.get('error')}")
                else:
                    print(f"\n=== STREAMING RESPONSE SUMMARY ===")
                    print(f"Response ID: {response.get('id')}")
                    print(f"Model used: {response.get('model')}")
                    print(f"Stop reason: {response.get('stop_reason')}")
                    print(f"Total content blocks: {len(response.get('content', []))}")
                    print(f"Thinking blocks: {len([b for b in response.get('content', []) if b.get('type') == 'thinking'])}")
                    print(f"Tool use blocks: {len([b for b in response.get('content', []) if b.get('type') == 'tool_use'])}")
                    print(f"Text blocks: {len([b for b in response.get('content', []) if b.get('type') == 'text'])}")
                    print("\n=== END STREAMING RESPONSE SUMMARY ===")
                
        else:
            print("\nSkipping Anthropic API integration: No API key found in environment variables.")
            print("Set ANTHROPIC_API_KEY or LLM_API_KEY environment variable to use this feature.")
    
    except FileNotFoundError:
        print(f"Error: Configuration file '{config_path}' exists but cannot be opened.")
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in '{config_path}'. Please check the file format.")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    finally:
        # Close all connections
        if 'mcp_client' in locals():
            print("\nClosing MCP server connections...")
            await mcp_client.close()
            print("Done.")

if __name__ == "__main__":
    asyncio.run(main())
