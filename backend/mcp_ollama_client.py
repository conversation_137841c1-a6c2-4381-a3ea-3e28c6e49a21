#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""MCP Client adapted for Ollama API integration with <think> tag support."""

import asyncio
import json
import logging
import os
import argparse
import time
import re # Added for parsing <think> tags
from functools import wraps
from typing import Dict, List, Any, Optional, Tuple, Callable, Union, Awaitable

import aiohttp # Use aiohttp for Ollama requests

from mcp import ClientSession
from mcp.client.sse import sse_client

# Export public classes for easier imports
__all__ = [
    "MCPClient",
    "MCPServerConnection",
    "MCPToolDefinition",
    "MCPLLMIntegrationOllama", # Renamed class
    "RateLimiter",
    "retry_with_exponential_backoff",
    "configure_logging"
]

# Rate limiter class (remains the same)
class RateLimiter:
    def __init__(self, max_requests=50, time_window=60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []

    def can_make_request(self):
        now = time.time()
        self.requests = [req_time for req_time in self.requests if now - req_time < self.time_window]
        if len(self.requests) < self.max_requests:
            self.requests.append(now)
            return True
        return False

    async def wait_for_next_window(self):
        if self.requests:
            oldest_request = min(self.requests)
            sleep_time = self.time_window - (time.time() - oldest_request)
            if sleep_time > 0:
                logger.info(f"Rate limit reached, waiting {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)

# Global rate limiter instance (consider if needed for local Ollama)
rate_limiter = RateLimiter()

# Configure logging function (remains the same)
def configure_logging(log_to_file=True, log_to_console=False, log_level=logging.INFO, log_file_path=None):
    handlers = []
    if log_to_file:
        if log_file_path is None:
            log_directory = os.path.join(os.path.dirname(os.path.abspath(__file__)), "log")
            os.makedirs(log_directory, exist_ok=True)
            log_file_path = os.path.join(log_directory, "mcp_ollama_client.log")
        else:
            log_directory = os.path.dirname(os.path.abspath(log_file_path))
            os.makedirs(log_directory, exist_ok=True)
        handlers.append(logging.FileHandler(log_file_path, encoding="utf-8"))
    if log_to_console:
        handlers.append(logging.StreamHandler())
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        handlers=handlers,
        force=True
    )
    return log_file_path

# Set up default logging
log_file_path = configure_logging(log_to_file=True, log_to_console=False)
logger = logging.getLogger("mcp_client_ollama") # Changed logger name

# Retry decorator (adjust exception types if needed for aiohttp/Ollama)
def retry_with_exponential_backoff(max_retries=3, initial_wait=5):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retries = 0
            last_error = None
            mcp_client = None
            if args and hasattr(args[0], 'mcp_client'):
                mcp_client = args[0].mcp_client
            elif args and isinstance(args[0], MCPClient):
                mcp_client = args[0]

            while retries <= max_retries:
                # Rate limiting might be less critical for local Ollama, but kept for structure
                # if not rate_limiter.can_make_request():
                #     logger.info("Rate limit reached, waiting for next window")
                #     await rate_limiter.wait_for_next_window()

                try:
                    return await func(*args, **kwargs)
                # Catch aiohttp specific errors + general exceptions
                except (aiohttp.ClientConnectionError, aiohttp.ClientResponseError, asyncio.TimeoutError) as e:
                    retries += 1
                    last_error = e
                    logger.warning(f"Ollama connection/response error: {str(e)} (attempt {retries}/{max_retries+1})")
                    is_connection_error = True # Treat these as connection issues
                except Exception as e:
                    retries += 1
                    last_error = e
                    logger.warning(f"Error during Ollama request: {str(e)} (attempt {retries}/{max_retries+1})")
                    # Basic check if it might be a connection issue
                    is_connection_error = any(err in str(e).lower() for err in ["connection", "timeout", "network", "closed", "eof"])

                if retries <= max_retries:
                    wait_time = initial_wait * (2 ** (retries - 1))
                    logger.info(f"Retrying in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)

                    # Reconnect MCP servers if it was a connection error and client exists
                    if is_connection_error and mcp_client:
                        logger.info("Reconnecting to MCP servers before retry...")
                        await refresh_connections(mcp_client)
                else:
                    logger.error(f"Max retries reached for Ollama request.")
                    raise last_error

            raise last_error if last_error else RuntimeError("Unknown error during retry")
        return wrapper
    return decorator

# Refresh MCP connections helper (remains the same)
async def refresh_connections(mcp_client):
    reconnected_servers = []
    try:
        all_servers = list(mcp_client.servers.items())
        for server_id, server in all_servers:
            if server.is_connected and server.session:
                try:
                    is_healthy = await server.session.ping()
                    if not is_healthy:
                        logger.info(f"Server {server_id} connection is not healthy")
                        await server.disconnect()
                        reconnected_servers.append(server_id)
                except Exception as e:
                    logger.warning(f"Health check failed for server {server_id}: {str(e)}")
                    await server.disconnect()
                    reconnected_servers.append(server_id)
            elif not server.is_connected:
                reconnected_servers.append(server_id)

        for server_id in reconnected_servers:
            server = mcp_client.servers.get(server_id)
            if server:
                logger.info(f"Attempting to reconnect to server {server_id}")
                try:
                    await asyncio.sleep(0.5)
                    success = await server.connect()
                    if success:
                        logger.info(f"Successfully reconnected to server {server_id}")
                    else:
                        logger.warning(f"Failed to reconnect to server {server_id}")
                except Exception as e:
                    logger.warning(f"Error reconnecting to server {server_id}: {str(e)}")

        if reconnected_servers:
            logger.info(f"Refreshing tools cache after reconnecting {len(reconnected_servers)} servers")
            try:
                await mcp_client.list_all_tools()
            except Exception as e:
                logger.warning(f"Error refreshing tools after reconnection: {str(e)}")

        return len(reconnected_servers)

    except Exception as e:
        logger.warning(f"Error refreshing MCP connections: {str(e)}")
        return 0

# MCP Tool Definition (remains the same)
class MCPToolDefinition:
    def __init__(self, server_id: str, name: str, description: str, input_schema: Dict[str, Any]):
        self.server_id = server_id
        self.name = name
        self.description = description
        self.input_schema = input_schema

    def __repr__(self) -> str:
        return f"Tool(server={self.server_id}, name={self.name})"

    def format_for_llm(self) -> Dict[str, Any]:
        # --- ADAPTED FOR OLLAMA --- (closer to OpenAI format)
        # Assumes input_schema is already in JSON Schema format
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.input_schema # Directly use the schema
            }
        }

# MCP Server Connection (remains the same)
class MCPServerConnection:
    def __init__(self, server_id: str, server_url: str, keep_alive_interval: int = 30):
        self.server_id = server_id
        self.server_url = server_url
        self.session: Optional[ClientSession] = None
        self.tools_cache: List[MCPToolDefinition] = []
        self.is_connected = False
        self._connection_task = None
        self._keep_alive_task = None
        self._last_successful_operation = 0
        self.keep_alive_interval = keep_alive_interval

    async def _connection_handler(self):
        try:
            logger.info(f"Starting connection to MCP server {self.server_id} at {self.server_url}")
            async with sse_client(self.server_url) as streams:
                read_stream, write_stream = streams
                async with ClientSession(read_stream, write_stream) as session:
                    session.ping = lambda: self._ping_session(session)
                    self.session = session
                    self.is_connected = True
                    self._last_successful_operation = time.time()
                    await session.initialize()
                    logger.info(f"Successfully connected to MCP server {self.server_id}")
                    await self._refresh_tools_cache_internal(session)
                    self._start_keep_alive_task()
                    while self.is_connected:
                        try:
                            message = await asyncio.wait_for(anext(session.incoming_messages.__aiter__()), 1.0)
                            if isinstance(message, Exception):
                                logger.error(f"Error from server {self.server_id}: {message}")
                            else:
                                logger.debug(f"Message from server {self.server_id}: {message}")
                                self._last_successful_operation = time.time()
                        except asyncio.TimeoutError:
                            pass
                        except StopAsyncIteration:
                            logger.warning(f"Message stream ended for server {self.server_id}")
                            self.is_connected = False
                            break
                        except Exception as e:
                            logger.error(f"Error processing messages for {self.server_id}: {str(e)}")
                            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                                self.is_connected = False
        except Exception as e:
            logger.error(f"Connection error for server {self.server_id}: {str(e)}")
        finally:
            self._stop_keep_alive_task()
            self.session = None
            self.is_connected = False
            logger.info(f"Disconnected from MCP server {self.server_id}")

    def _start_keep_alive_task(self):
        if self._keep_alive_task is None or self._keep_alive_task.done():
            self._keep_alive_task = asyncio.create_task(self._keep_alive_loop())
            logger.info(f"Started keep-alive task for server {self.server_id}")

    def _stop_keep_alive_task(self):
        if self._keep_alive_task and not self._keep_alive_task.done():
            self._keep_alive_task.cancel()
            logger.info(f"Stopped keep-alive task for server {self.server_id}")

    async def _keep_alive_loop(self):
        try:
            logger.info(f"Keep-alive loop started for server {self.server_id} (interval: {self.keep_alive_interval}s)")
            while self.is_connected and self.session:
                try:
                    await asyncio.sleep(self.keep_alive_interval)
                    if not self.is_connected or not self.session:
                        break
                    current_time = time.time()
                    time_since_last_op = current_time - self._last_successful_operation
                    if time_since_last_op >= self.keep_alive_interval:
                        logger.debug(f"Sending keep-alive ping to server {self.server_id}")
                        is_healthy = await self.session.ping()
                        if is_healthy:
                            logger.debug(f"Keep-alive ping successful for server {self.server_id}")
                            self._last_successful_operation = current_time
                        else:
                            logger.warning(f"Keep-alive ping failed for server {self.server_id}")
                    else:
                        logger.debug(f"Skipping keep-alive ping for {self.server_id} (recent activity {time_since_last_op:.1f}s ago)")
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logger.warning(f"Error in keep-alive ping for server {self.server_id}: {str(e)}")
        except asyncio.CancelledError:
            logger.debug(f"Keep-alive loop cancelled for server {self.server_id}")
        except Exception as e:
            logger.error(f"Error in keep-alive loop for server {self.server_id}: {str(e)}")

    async def _ping_session(self, session) -> bool:
        if (time.time() - self._last_successful_operation) < 5:
            return True
        try:
            await session.list_tools()
            self._last_successful_operation = time.time()
            return True
        except Exception as e:
            logger.warning(f"Ping failed for server {self.server_id}: {str(e)}")
            return False

    async def connect(self) -> bool:
        if self.is_connected and self.session:
            try:
                await self.session.ping()
                return True
            except Exception as e:
                logger.warning(f"Existing connection to {self.server_id} seems broken: {str(e)}")
                await self.disconnect()

        if self._connection_task and not self._connection_task.done():
            try:
                self._connection_task.cancel()
                await asyncio.wait_for(self._connection_task, timeout=2.0)
            except (asyncio.TimeoutError, asyncio.CancelledError):
                pass
            except Exception as e:
                logger.warning(f"Error cancelling previous connection task for {self.server_id}: {str(e)}")

        self.is_connected = False
        self.session = None
        try:
            self._connection_task = asyncio.create_task(self._connection_handler())
            for _ in range(15):
                if self.is_connected and self.session:
                    return True
                await asyncio.sleep(1.0)
            if self._connection_task and not self._connection_task.done():
                self._connection_task.cancel()
            return False
        except Exception as e:
            logger.error(f"Error initiating connection to {self.server_id}: {str(e)}")
            return False

    async def _refresh_tools_cache_internal(self, session) -> List[MCPToolDefinition]:
        try:
            tools_response = await session.list_tools()
            self.tools_cache = [
                MCPToolDefinition(
                    server_id=self.server_id,
                    name=tool.name,
                    description=tool.description or f"Tool from {self.server_id}",
                    input_schema=tool.inputSchema
                )
                for tool in tools_response.tools
            ]
            logger.info(f"Retrieved {len(self.tools_cache)} tools from server {self.server_id}")
            return self.tools_cache
        except Exception as e:
            logger.error(f"Failed to list tools for server {self.server_id}: {str(e)}")
            return []

    async def refresh_tools_cache(self) -> List[MCPToolDefinition]:
        if not self.is_connected or not self.session:
            logger.warning(f"Cannot refresh tools for disconnected server {self.server_id}")
            return self.tools_cache
        try:
            return await self._refresh_tools_cache_internal(self.session)
        except Exception as e:
            logger.error(f"Failed to list tools for server {self.server_id}: {str(e)}")
            if "connection" in str(e).lower() or "timeout" in str(e).lower():
                self.is_connected = False
            return self.tools_cache

    # Note: The retry decorator is now defined globally and adapted for aiohttp
    # @retry_with_exponential_backoff(max_retries=3, initial_wait=20) # Applied globally now
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        if not self.is_connected or not self.session:
            try:
                await self.connect()
            except Exception as e:
                return {"success": False, "server_id": self.server_id, "tool": tool_name, "error": f"Cannot connect to server: {str(e)}"}
            if not self.is_connected or not self.session:
                return {"success": False, "server_id": self.server_id, "tool": tool_name, "error": "Failed to establish connection to server"}

        try:
            logger.info(f"Executing tool {tool_name} on server {self.server_id}")
            result = await self.session.call_tool(tool_name, params)
            all_text_content = []
            if hasattr(result, 'content') and len(result.content) > 0:
                for content_item in result.content:
                    if hasattr(content_item, 'text'):
                        all_text_content.append(content_item.text)
                    else:
                        all_text_content.append(str(content_item))
                text_content = "\n\n---\n\n".join(all_text_content)
            else:
                text_content = str(result)
            return {"success": True, "server_id": self.server_id, "tool": tool_name, "content": text_content, "raw_result": result}
        except Exception as e:
            if "connection" in str(e).lower() or "timeout" in str(e).lower() or "429" in str(e):
                logger.warning(f"Connection issue detected for server {self.server_id}: {str(e)}")
                self.is_connected = False
                try:
                    await self.disconnect()
                    await self.connect()
                except Exception as reconnect_error:
                    logger.warning(f"Reconnection attempt failed: {str(reconnect_error)}")
            raise # Let the global retry decorator handle it

    async def disconnect(self) -> None:
        if not self.is_connected:
            return
        logger.info(f"Disconnecting from server {self.server_id}")
        self._stop_keep_alive_task()
        self.is_connected = False
        if self._connection_task:
            try:
                await asyncio.wait_for(self._connection_task, timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(f"Timeout waiting for connection handler to stop for {self.server_id}")
                self._connection_task.cancel()
            except Exception as e:
                logger.warning(f"Error waiting for connection handler: {str(e)}")
            self._connection_task = None
        logger.info(f"Disconnected from MCP server {self.server_id}")

# MCP Client (remains mostly the same, uses adapted ToolDefinition)
class MCPClient:
    def __init__(self, config_path: str = "mcp.json", keep_alive_interval: int = 30):
        self.config_path = config_path
        self.servers: Dict[str, MCPServerConnection] = {}
        self.config: Dict[str, Any] = {}
        self.keep_alive_interval = keep_alive_interval

    @classmethod
    async def create(cls, config_path: str = "mcp.json", keep_alive_interval: int = 30) -> 'MCPClient':
        client = cls(config_path, keep_alive_interval)
        await client.initialize()
        return client

    def load_config(self) -> Dict[str, Any]:
        """Load the MCP configuration from file.
        
        Returns:
            Dict: The loaded configuration
        
        Raises:
            FileNotFoundError: If the configuration file doesn't exist
            json.JSONDecodeError: If the configuration file is invalid JSON
        """
        try:
            with open(self.config_path, 'r', encoding="utf-8") as f:
                self.config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_path}")
                return self.config
        except FileNotFoundError:
            logger.error(f"Configuration file not found: {self.config_path}")
            self.config = {"servers": {}}
            raise
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON in configuration file: {self.config_path}")
            self.config = {"servers": {}}
            raise
    
    async def initialize(self) -> None:
        """Initialize connections to all configured MCP servers."""
        # Load configuration if not already loaded
        if not self.config:
            self.load_config()
        
        # Get server configurations
        server_configs = self.config.get("servers", {})
        
        if not server_configs:
            logger.warning("No MCP servers configured")
            return
        
        # Initialize each server connection
        for server_id, server_config in server_configs.items():
            if "url" not in server_config:
                logger.warning(f"Skipping server {server_id}: Missing URL")
                continue
                
            server_url = server_config["url"]
            # Pass the keep_alive_interval when creating connections
            self.servers[server_id] = MCPServerConnection(server_id, server_url, self.keep_alive_interval)
        
        # Connect to all servers concurrently
        connection_tasks = [
            self.servers[server_id].connect() 
            for server_id in self.servers
        ]
        
        # Wait for all connections (or failures)
        await asyncio.gather(*connection_tasks, return_exceptions=True)
        
        # Log connection summary
        connected_servers = sum(1 for server in self.servers.values() if server.is_connected)
        logger.info(f"Connected to {connected_servers}/{len(self.servers)} MCP servers")

    async def list_all_tools(self) -> List[MCPToolDefinition]:
        all_tools = []
        refresh_tasks = [server.refresh_tools_cache() for server in self.servers.values() if server.is_connected]
        await asyncio.gather(*refresh_tasks, return_exceptions=True)
        for server in self.servers.values():
            if server.is_connected:
                all_tools.extend(server.tools_cache)
        return all_tools

    async def pretty_tool_list(self) -> str:
        tools = await self.list_all_tools()
        tools_by_server = {}
        for tool in tools:
            if tool.server_id not in tools_by_server:
                tools_by_server[tool.server_id] = []
            tools_by_server[tool.server_id].append(tool.name)
        markdown_output = "# Available MCP Tools\n\n"
        for server_id in sorted(tools_by_server.keys()):
            markdown_output += f"## {server_id}\n\n"
            for tool_name in sorted(tools_by_server[server_id]):
                markdown_output += f"- `{tool_name}`\n"
            markdown_output += "\n"
        return markdown_output

    async def find_tool(self, tool_name: str) -> Tuple[Optional[str], Optional[MCPToolDefinition]]:
        await self.list_all_tools() # Ensure cache is fresh
        for server_id, server in self.servers.items():
            if not server.is_connected:
                continue
            for tool in server.tools_cache:
                if tool.name == tool_name:
                    return server_id, tool
        return None, None

    # Note: The retry decorator is now defined globally and adapted for aiohttp
    # @retry_with_exponential_backoff(max_retries=3, initial_wait=20) # Applied globally now
    async def call_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        server_id, _ = await self.find_tool(tool_name)
        if not server_id:
            error_msg = f"Tool '{tool_name}' not found on any connected server"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        server = self.servers.get(server_id)
        if not server or not server.is_connected:
            error_msg = f"Server {server_id} is not connected"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
        try:
            # Use the server's call_tool method, which might internally use retry
            return await server.call_tool(tool_name, params)
        except Exception as e:
            logger.error(f"Error calling tool {tool_name} via MCPClient: {str(e)}")
            # Re-raise for the global retry decorator if applicable, or handle here
            raise

    async def format_tools_for_llm(self) -> List[Dict[str, Any]]:
        """Formats tools for Ollama (OpenAI compatible structure)."""
        all_tools = await self.list_all_tools()
        # Uses the adapted format_for_llm in MCPToolDefinition
        return [tool.format_for_llm() for tool in all_tools]

    async def close(self) -> None:
        logger.info("Closing all MCP server connections")
        close_exceptions = []
        for server_id, server in self.servers.items():
            try:
                await server.disconnect()
            except Exception as e:
                error_msg = f"Error closing connection to server {server_id}: {str(e)}"
                logger.error(error_msg)
                close_exceptions.append(error_msg)
        if close_exceptions:
            logger.warning(f"Encountered {len(close_exceptions)} errors while closing connections")
        else:
            logger.info("Successfully closed all MCP server connections")

    def create_llm_integration(self, ollama_base_url: str = "http://localhost:11434", model: str = "llama3.1", max_iterations: int = 10) -> 'MCPLLMIntegrationOllama':
        """Create an Ollama LLM integration with this client."""
        return MCPLLMIntegrationOllama(self, ollama_base_url=ollama_base_url, model=model, max_iterations=max_iterations)

# --- Helper function to parse Ollama content for <think> tags --- #
def parse_ollama_content(content_str: str) -> List[Dict[str, Any]]:
    """Parses Ollama content string into text and thinking blocks."""
    if not content_str:
        return []

    blocks = []
    # Regex to find <think>...</think> blocks, handling potential whitespace
    # It captures the text before, the thinking content, and the text after
    pattern = re.compile(r"(.*?)(?:<think>(.*?)</think>|$)", re.DOTALL | re.IGNORECASE)
    last_end = 0

    for match in pattern.finditer(content_str):
        start, end = match.span()
        if start > last_end:
            # Capture text between the previous match and this one (shouldn't happen with this regex but good practice)
            text_before = content_str[last_end:start].strip()
            if text_before:
                blocks.append({"type": "text", "text": text_before})

        # Text before the <think> tag in this match
        text_part = match.group(1).strip()
        if text_part:
            blocks.append({"type": "text", "text": text_part})

        # Thinking content inside the <think> tag
        thinking_part = match.group(2)
        if thinking_part is not None: # If <think> tag was found
            thinking_part = thinking_part.strip()
            if thinking_part:
                blocks.append({"type": "thinking", "thinking": thinking_part})
                logger.debug(f"Extracted thinking block: {thinking_part[:100]}...")

        last_end = end

    # Capture any remaining text after the last match
    if last_end < len(content_str):
        remaining_text = content_str[last_end:].strip()
        if remaining_text:
            blocks.append({"type": "text", "text": remaining_text})

    # If no tags were found, return the original content as a single text block
    if not blocks and content_str:
        blocks.append({"type": "text", "text": content_str.strip()})

    return blocks

# --- MCPLLMIntegration adapted for Ollama --- #

class MCPLLMIntegrationOllama:
    """Integrates MCP tools with Ollama's API."""

    def __init__(self, mcp_client: MCPClient, ollama_base_url: str = "http://localhost:11434", model: str = "llama3.1", max_iterations: int = 10):
        self.mcp_client = mcp_client
        self.ollama_base_url = ollama_base_url.rstrip('/')
        self.chat_endpoint = f"{self.ollama_base_url}/api/chat"
        self.model = model
        self.max_iterations = max_iterations
        self.tool_call_history = []
        self.tool_call_callback = None
        # Create a persistent aiohttp session
        self._session: Optional[aiohttp.ClientSession] = None

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create the aiohttp ClientSession."""
        if self._session is None or self._session.closed:
            # You might want to configure timeouts here
            timeout = aiohttp.ClientTimeout(total=300) # 5 minutes total timeout
            self._session = aiohttp.ClientSession(timeout=timeout)
        return self._session

    async def close_session(self):
        """Close the aiohttp session if it exists."""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
            logger.info("Closed Ollama HTTP session.")

    def register_tool_call_callback(self, callback: Callable[[Dict[str, Any]], Union[None, Awaitable[None]]]):
        self.tool_call_callback = callback

    def get_tool_call_history(self) -> List[Dict[str, Any]]:
        return self.tool_call_history

    def clear_tool_call_history(self):
        self.tool_call_history = []

    async def _handle_tool_call_result(self, tool_name: str, tool_input: Dict[str, Any], tool_id: str, tool_result: Dict[str, Any]):
        tool_call_record = {
            "timestamp": asyncio.get_event_loop().time(),
            "tool_name": tool_name,
            "tool_input": tool_input,
            "tool_id": tool_id,
            "success": tool_result.get("success", False),
            "content": tool_result.get("content", ""),
            "error": tool_result.get("error", "") if not tool_result.get("success", False) else "",
            "raw_result": tool_result
        }
        self.tool_call_history.append(tool_call_record)
        if self.tool_call_callback:
            if asyncio.iscoroutinefunction(self.tool_call_callback):
                await self.tool_call_callback(tool_call_record)
            else:
                self.tool_call_callback(tool_call_record)

    # --- Ollama Specific Methods --- #

    @retry_with_exponential_backoff(max_retries=3, initial_wait=5)
    async def _send_to_ollama(self, messages: List[Dict[str, Any]],
                             tools: List[Dict[str, Any]],
                             max_tokens: Optional[int] = None, # Ollama doesn't have a strict max_tokens for chat
                             options: Optional[Dict[str, Any]] = None, # For temperature, etc.
                             format_json: bool = False # To enable JSON mode
                             ) -> Dict[str, Any]:
        """Send a non-streaming request to Ollama's /api/chat endpoint."""
        session = await self._get_session()
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": False,
        }
        if tools:
            payload["tools"] = tools
        if options:
            payload["options"] = options
        if format_json:
             payload["format"] = "json"
             # Crucial: Instruct the model to use JSON in the prompt itself
             if messages and messages[-1]["role"] == "user":
                 if "Respond using JSON" not in messages[-1]["content"]:
                     logger.warning("Using format='json' but the prompt doesn't explicitly ask for JSON. This might lead to unexpected results.")

        logger.info(f"Sending request to Ollama: {self.chat_endpoint}")
        # logger.debug(f"Ollama Payload: {json.dumps(payload, indent=2)}")

        try:
            async with session.post(self.chat_endpoint, json=payload) as response:
                response.raise_for_status() # Raise exception for 4xx/5xx errors
                result = await response.json()
                # logger.debug(f"Ollama Response: {json.dumps(result, indent=2)}")
                logger.info("Received response from Ollama")

                # --- ADDED: Parse content for <think> tags --- #
                if result.get("message") and isinstance(result["message"].get("content"), str):
                    raw_content = result["message"]["content"]
                    parsed_blocks = parse_ollama_content(raw_content)
                    result["message"]["content"] = parsed_blocks # Replace string with list of blocks
                    logger.info(f"Parsed Ollama content into {len(parsed_blocks)} blocks (text/thinking).")
                # --- END ADDED --- #

                return result
        except aiohttp.ClientResponseError as e:
            logger.error(f"Ollama API error: {e.status} {e.message}")
            try:
                error_details = await e.text()
                logger.error(f"Ollama error details: {error_details}")
                return {"error": f"Ollama API error: {e.status} {e.message}", "details": error_details}
            except Exception as read_err:
                logger.error(f"Could not read error details: {read_err}")
                return {"error": f"Ollama API error: {e.status} {e.message}"}
        except aiohttp.ClientConnectionError as e:
            logger.error(f"Ollama connection error: {e}")
            raise # Let retry handle connection errors
        except asyncio.TimeoutError as e:
            logger.error(f"Ollama request timed out: {e}")
            raise # Let retry handle timeouts
        except Exception as e:
            logger.error(f"Error sending request to Ollama: {e}")
            raise # Let retry handle other errors

    @retry_with_exponential_backoff(max_retries=3, initial_wait=5)
    async def _send_to_ollama_stream(self, messages: List[Dict[str, Any]],
                                    tools: List[Dict[str, Any]],
                                    max_tokens: Optional[int] = None,
                                    options: Optional[Dict[str, Any]] = None,
                                    format_json: bool = False,
                                    callback: Optional[Callable[[Dict[str, Any]], Union[None, Awaitable[None]]]] = None
                                    ) -> Dict[str, Any]:
        """Send a streaming request to Ollama's /api/chat endpoint."""
        session = await self._get_session()
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": True,
        }
        if tools:
            payload["tools"] = tools
        if options:
            payload["options"] = options
        if format_json:
             payload["format"] = "json"
             if messages and messages[-1]["role"] == "user":
                 if "Respond using JSON" not in messages[-1]["content"]:
                     logger.warning("Using format='json' but the prompt doesn't explicitly ask for JSON. This might lead to unexpected results.")

        logger.info(f"Sending streaming request to Ollama: {self.chat_endpoint}")
        # logger.debug(f"Ollama Payload (stream): {json.dumps(payload, indent=2)}")

        # Initialize full_response structure
        full_response = {
            "message": {"role": "assistant", "content": [], "tool_calls": []}, # content is now a list
            "done": False
        }
        accumulated_content_str = "" # Accumulate the raw string first
        accumulated_tool_calls = []

        try:
            async with session.post(self.chat_endpoint, json=payload) as response:
                response.raise_for_status()
                async for line in response.content:
                    if line:
                        try:
                            chunk_str = line.decode('utf-8').strip()
                            if not chunk_str:
                                continue

                            chunk = json.loads(chunk_str)
                            # logger.debug(f"Ollama Stream Chunk: {chunk}")

                            # Call callback if provided (pass raw chunk)
                            if callback:
                                # We might want a different callback format if we parse thinking mid-stream
                                if asyncio.iscoroutinefunction(callback):
                                    await callback(chunk)
                                else:
                                    callback(chunk)

                            # Accumulate raw content delta string
                            if chunk.get("message") and "content" in chunk["message"]:
                                delta = chunk["message"]["content"]
                                if delta:
                                    accumulated_content_str += delta
                                    # Note: We parse the full string at the end for simplicity
                                    # Real-time parsing of <think> tags in stream is complex

                            # Accumulate tool calls (Ollama might send these only at the end)
                            if chunk.get("message") and "tool_calls" in chunk["message"]:
                                if chunk["message"]["tool_calls"]:
                                    accumulated_tool_calls = chunk["message"]["tool_calls"]
                                    # Update immediately in case they arrive mid-stream (though unlikely)
                                    full_response["message"]["tool_calls"] = accumulated_tool_calls

                            # Check if done and capture final metadata
                            if chunk.get("done", False):
                                full_response["done"] = True
                                # Capture other final fields like usage stats if needed
                                for key, value in chunk.items():
                                    if key not in ["message", "done"]:
                                        full_response[key] = value
                                logger.info("Ollama stream finished.")
                                break # Exit loop once done

                        except json.JSONDecodeError:
                            logger.warning(f"Failed to decode JSON chunk: {line.decode('utf-8', errors='ignore')}")
                        except Exception as e:
                            logger.error(f"Error processing stream chunk: {e}")
                            # Decide if we should break or continue
                            break

            # --- ADDED: Parse final accumulated content string for <think> tags --- #
            parsed_blocks = parse_ollama_content(accumulated_content_str)
            full_response["message"]["content"] = parsed_blocks # Set the final content as list of blocks
            logger.info(f"Parsed final Ollama stream content into {len(parsed_blocks)} blocks (text/thinking).")
            # --- END ADDED --- #

            # Ensure tool calls are correctly set in the final response
            full_response["message"]["tool_calls"] = accumulated_tool_calls
            # logger.debug(f"Ollama Final Assembled Response (Stream): {json.dumps(full_response, indent=2)}")
            return full_response

        except aiohttp.ClientResponseError as e:
            logger.error(f"Ollama API stream error: {e.status} {e.message}")
            try:
                error_details = await e.text()
                logger.error(f"Ollama error details: {error_details}")
                return {"error": f"Ollama API stream error: {e.status} {e.message}", "details": error_details}
            except Exception as read_err:
                logger.error(f"Could not read error details: {read_err}")
                return {"error": f"Ollama API stream error: {e.status} {e.message}"}
        except aiohttp.ClientConnectionError as e:
            logger.error(f"Ollama stream connection error: {e}")
            raise
        except asyncio.TimeoutError as e:
            logger.error(f"Ollama stream request timed out: {e}")
            raise
        except Exception as e:
            logger.error(f"Error sending streaming request to Ollama: {e}")
            raise

    # --- Main Processing Logic --- #

    async def process_messages(self, messages: List[Dict[str, Any]],
                              max_tokens: Optional[int] = None, # Not directly used by Ollama chat
                              options: Optional[Dict[str, Any]] = None,
                              format_json: bool = False
                              ) -> Dict[str, Any]:
        """Process messages using Ollama, handling tool calls (non-streaming)."""
        tool_definitions = await self.mcp_client.format_tools_for_llm()
        conversation_history = messages.copy()
        iteration = 0

        while iteration < self.max_iterations:
            iteration += 1
            logger.info(f"Ollama processing iteration {iteration}")

            response = await self._send_to_ollama(
                conversation_history,
                tool_definitions,
                max_tokens, # Pass along, though Ollama might ignore for /chat
                options,
                format_json
            )

            if response.get("error"):
                logger.error(f"Ollama API returned an error: {response['error']}")
                return response # Return error response directly

            assistant_message = response.get("message", {})
            if not assistant_message:
                 logger.error("Ollama response missing 'message' field.")
                 return {"error": "Invalid Ollama response: missing 'message' field", "raw_response": response}

            # --- UPDATED: Add assistant's response (now content is list of blocks) --- #
            # Ensure content list is not empty or tool_calls exist before adding
            if assistant_message.get("content") or assistant_message.get("tool_calls"):
                 conversation_history.append(assistant_message)
            else:
                 logger.warning("Assistant message has neither content blocks nor tool_calls, not adding to history.")
            # --- END UPDATED --- #

            tool_calls = assistant_message.get("tool_calls")

            if not tool_calls:
                logger.info("No tool calls requested by Ollama. Finishing.")
                # Return the final response from Ollama (which now includes parsed content blocks)
                return response

            logger.info(f"Ollama requested {len(tool_calls)} tool calls.")

            # Process tool calls (logic remains the same)
            tool_results = []
            for tool_call in tool_calls:
                tool_func = tool_call.get("function", {})
                tool_name = tool_func.get("name")
                tool_id = tool_call.get("id") # Ollama uses id directly in the tool_call object
                tool_args_str = tool_func.get("arguments", "{}")

                if not tool_name or not tool_id:
                    logger.warning(f"Skipping invalid tool call object: {tool_call}")
                    continue

                try:
                    tool_input = json.loads(tool_args_str)
                except json.JSONDecodeError:
                    logger.error(f"Failed to decode tool arguments for {tool_name}: {tool_args_str}")
                    # Provide an error message back to the model
                    tool_output_content = f"Error: Invalid JSON arguments provided: {tool_args_str}"
                    tool_results.append({
                        "role": "tool",
                        "tool_call_id": tool_id,
                        "content": tool_output_content # Result content must be a string for Ollama
                    })
                    # Log this specific failure but continue processing other tools
                    await self._handle_tool_call_result(tool_name, {"arguments_string": tool_args_str}, tool_id, {"success": False, "error": "Invalid JSON arguments"})
                    continue

                logger.info(f"Executing tool: {tool_name}")
                print(f"\n\033[94m--> Executing tool: [{tool_name}] with input: {tool_input}\033[0m")

                # Execute the tool via MCPClient
                mcp_tool_result = await self.mcp_client.call_tool(tool_name, tool_input)

                # Log/Callback for the tool result
                await self._handle_tool_call_result(tool_name, tool_input, tool_id, mcp_tool_result)

                # Format result for Ollama API (content must be a string)
                if mcp_tool_result.get("success", False):
                    # Serialize complex results (like dicts/lists) into a JSON string
                    raw_content = mcp_tool_result.get("content", "")
                    if isinstance(raw_content, (dict, list)):
                        tool_output_content = json.dumps(raw_content)
                    else:
                        tool_output_content = str(raw_content)
                else:
                    tool_output_content = f"Error executing tool {tool_name}: {mcp_tool_result.get('error', 'Unknown error')}"

                # print(f"\n\033[94m--> Tool result for Ollama: {tool_output_content}\033[0m")

                tool_results.append({
                    "role": "tool",
                    "tool_call_id": tool_id,
                    "content": tool_output_content # Result content must be a string
                })

            # Add all tool results to conversation history for the next turn
            conversation_history.extend(tool_results)

        # If loop finishes due to max_iterations
        logger.warning(f"Reached max tool call iterations ({self.max_iterations}). Returning last response.")
        # Return the last response received from Ollama before hitting the limit
        return response

    async def process_messages_stream(self, messages: List[Dict[str, Any]],
                                     max_tokens: Optional[int] = None,
                                     options: Optional[Dict[str, Any]] = None,
                                     format_json: bool = False,
                                     callback: Optional[Callable[[Dict[str, Any]], Union[None, Awaitable[None]]]] = None
                                     ) -> Dict[str, Any]:
        """Process messages using Ollama, handling tool calls (streaming)."""
        tool_definitions = await self.mcp_client.format_tools_for_llm()
        conversation_history = messages.copy()
        iteration = 0

        while iteration < self.max_iterations:
            iteration += 1
            logger.info(f"Ollama streaming processing iteration {iteration}")

            # Make the streaming call
            response = await self._send_to_ollama_stream(
                conversation_history,
                tool_definitions,
                max_tokens,
                options,
                format_json,
                callback # Pass the callback down
            )

            if response.get("error"):
                logger.error(f"Ollama API returned an error during stream: {response['error']}")
                return response

            assistant_message = response.get("message", {})
            if not assistant_message:
                 logger.error("Ollama stream response missing 'message' field.")
                 return {"error": "Invalid Ollama stream response: missing 'message' field", "raw_response": response}

            # --- UPDATED: Add assistant's assembled response (content is list of blocks) --- #
            # Ensure content list is not empty or tool_calls exist before adding
            if assistant_message.get("content") or assistant_message.get("tool_calls"):
                 conversation_history.append(assistant_message)
            else:
                 logger.warning("Streamed assistant message has neither content blocks nor tool_calls, not adding to history.")
            # --- END UPDATED --- #

            tool_calls = assistant_message.get("tool_calls")

            if not tool_calls:
                logger.info("No tool calls requested by Ollama in stream. Finishing.")
                # Return the final assembled response from the stream (which now includes parsed content blocks)
                return response

            logger.info(f"Ollama requested {len(tool_calls)} tool calls (detected after stream)." )

            # Process tool calls (same logic as non-streaming)
            tool_results = []
            for tool_call in tool_calls:
                tool_func = tool_call.get("function", {})
                tool_name = tool_func.get("name")
                tool_id = tool_call.get("id")
                tool_args_str = tool_func.get("arguments", "{}")

                if not tool_name or not tool_id:
                    logger.warning(f"Skipping invalid tool call object: {tool_call}")
                    continue

                try:
                    tool_input = json.loads(tool_args_str)
                except json.JSONDecodeError:
                    logger.error(f"Failed to decode tool arguments for {tool_name}: {tool_args_str}")
                    tool_output_content = f"Error: Invalid JSON arguments provided: {tool_args_str}"
                    tool_results.append({"role": "tool", "tool_call_id": tool_id, "content": tool_output_content})
                    await self._handle_tool_call_result(tool_name, {"arguments_string": tool_args_str}, tool_id, {"success": False, "error": "Invalid JSON arguments"})
                    continue

                logger.info(f"Executing tool: {tool_name}")
                print(f"\n\033[94m--> Executing tool: [{tool_name}] with input: {tool_input}\033[0m")
                mcp_tool_result = await self.mcp_client.call_tool(tool_name, tool_input)
                await self._handle_tool_call_result(tool_name, tool_input, tool_id, mcp_tool_result)

                if mcp_tool_result.get("success", False):
                    raw_content = mcp_tool_result.get("content", "")
                    if isinstance(raw_content, (dict, list)):
                        tool_output_content = json.dumps(raw_content)
                    else:
                        tool_output_content = str(raw_content)
                else:
                    tool_output_content = f"Error executing tool {tool_name}: {mcp_tool_result.get('error', 'Unknown error')}"

                # print(f"\n\033[94m--> Tool result for Ollama: {tool_output_content}\033[0m")
                tool_results.append({"role": "tool", "tool_call_id": tool_id, "content": tool_output_content})

            conversation_history.extend(tool_results)

        logger.warning(f"Reached max tool call iterations ({self.max_iterations}) during stream. Returning last assembled response.")
        return response

# Example Usage (Async context needed)
async def main():
    # Configure logging (optional)
    configure_logging(log_to_console=True, log_level=logging.INFO)

    # --- Setup MCP Client (Requires mcp.json) --- #
    # Create a dummy mcp.json if needed for testing structure
    if not os.path.exists("mcp.json"):
        logger.warning("mcp.json not found. Creating a dummy file. MCP tool calls will fail.")
        with open("mcp.json", "w") as f:
            json.dump({"servers": {"dummy_server": {"url": "http://localhost:9999"}}}, f)

    mcp_client = await MCPClient.create("mcp.json")

    # --- Setup Ollama Integration --- #
    # Ensure Ollama is running and the model (e.g., llama3.1) is pulled
    ollama_llm = mcp_client.create_llm_integration(
        ollama_base_url="http://localhost:11434", # Adjust if Ollama runs elsewhere
        model="llama3.1" # Use a model known to support tool calls
    )

    # Define a callback for stream events (optional)
    async def stream_callback(chunk):
        # This callback receives raw chunks. Parsing thinking mid-stream is complex.
        # For simplicity, we print the content delta here.
        if chunk.get("message") and chunk["message"].get("content"):
            print(chunk["message"]["content"], end="", flush=True)
        # You could add logic here to detect start/end of <think> tags across chunks if needed.

    # Define a callback for tool calls (optional)
    async def tool_callback(tool_call_info):
        print(f"\n\033[92mTool Callback: {tool_call_info['tool_name']} executed. Success: {tool_call_info['success']}\033[0m")
        if not tool_call_info['success']:
            print(f"\033[91m   Error: {tool_call_info['error']}\033[0m")
        # else:
        #     print(f"\033[92m   Result: {tool_call_info['content'][:100]}...\033[0m")

    ollama_llm.register_tool_call_callback(tool_callback)

    # --- Example Conversation --- #
    messages = [
        {"role": "system", "content": "You are a helpful assistant. Use tools when necessary. Wrap your reasoning process in <think></think> tags. Today is Apr 29 2025."}, # Added instruction for <think> tags
        {"role": "user", "content": "What is the weather like in London right now? Use a tool."}
        # {"role": "user", "content": "Summarize the main points of the article at https://example.com/article about AI trends."} # Example requiring a browse tool
    ]

    try:
        print("--- Running Non-Streaming Example (with <think> parsing) ---")
        # Note: MCP dummy server/tools will cause tool calls to fail here unless a real MCP server is running
        response = await ollama_llm.process_messages(messages, options={"temperature": 0.7})
        print("\n--- Final Non-Streaming Response ---")
        # Extract and print thinking blocks separately for clarity
        thinking_blocks = [block["thinking"] for block in response.get("message", {}).get("content", []) if block.get("type") == "thinking"]
        text_blocks = [block["text"] for block in response.get("message", {}).get("content", []) if block.get("type") == "text"]
        if thinking_blocks:
            print("\033[93mThinking:" + "\n".join(thinking_blocks) + "\033[0m")
        if text_blocks:
            print("\033[96mText Response:" + "\n".join(text_blocks) + "\033[0m")
        # Print full structure for debugging
        # print(json.dumps(response, indent=2))
        print("-------------------------------------")

        print("\n\n--- Running Streaming Example (with <think> parsing) ---")
        ollama_llm.clear_tool_call_history() # Clear history for the second run
        # Note: MCP dummy server/tools will cause tool calls to fail here
        stream_response = await ollama_llm.process_messages_stream(messages, options={"temperature": 0.7}, callback=stream_callback)
        print("\n--- Final Streaming Response (Assembled) ---")
        # Extract and print thinking blocks separately for clarity
        thinking_blocks_stream = [block["thinking"] for block in stream_response.get("message", {}).get("content", []) if block.get("type") == "thinking"]
        text_blocks_stream = [block["text"] for block in stream_response.get("message", {}).get("content", []) if block.get("type") == "text"]
        if thinking_blocks_stream:
            print("\033[93mThinking:" + "\n".join(thinking_blocks_stream) + "\033[0m")
        if text_blocks_stream:
            # Text was already printed by the callback, maybe just print a separator
            print("\033[96m(End of Text Response)\033[0m")
        # Print full structure for debugging
        # print(json.dumps(stream_response, indent=2))
        print("-------------------------------------------")

    except Exception as e:
        logger.exception(f"An error occurred during the example run: {e}")
    finally:
        # Clean up MCP connections and Ollama session
        await mcp_client.close()
        await ollama_llm.close_session()

if __name__ == "__main__":
    # Setup basic logging if run directly
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
    # Run the async main function
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nExiting...")

