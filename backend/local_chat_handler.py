import json
import os
from datetime import datetime
from pathlib import Path
import requests
from fastapi import WebSocket, WebSocketDisconnect
from os import remove
import base64
from PIL import Image
import io
import re
from dotenv import load_dotenv
import openai

class LocalChatHandler:
    def __init__(self):
        self.chat_history_dir = Path("chathist")
        self.chat_history_dir.mkdir(exist_ok=True)
        load_dotenv()
        self.small_model = os.getenv('SMALL_OLLAMA_MODEL', 'qwen2.5:latest')  # fallback to qwen2.5 if not set

    def convert_to_png_base64(self, image_data: str) -> str:
        try:
            # Decode base64 image
            img_bytes = base64.b64decode(image_data)
            img = Image.open(io.BytesIO(img_bytes))
            
            # Convert to PNG
            png_buffer = io.BytesIO()
            img.save(png_buffer, format='PNG')
            png_base64 = base64.b64encode(png_buffer.getvalue()).decode('utf-8')
            
            return png_base64
        except Exception as e:
            print(f"Error converting image: {e}")
            return None

    async def handle_message(self, websocket: WebSocket, data: dict):
        message_type = data.get("type")

        if message_type == "get_histories":
            await self.send_chat_histories(websocket)
        
        elif message_type == "message":
            content = data.get("content")
            history_id = data.get("historyId")
            config = data.get("config", {})
            images = data.get("images", [])
            
            # Convert images to PNG if present
            processed_images = []
            for img in images:
                png_base64 = self.convert_to_png_base64(img)
                if png_base64:
                    processed_images.append(png_base64)
            
            if not history_id:
                history_id = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save user message with images
            self.save_message(history_id, "user", content, processed_images)
            
            try:
                # Get chat history and build context
                history_file = self.chat_history_dir / f"{history_id}.json"
                messages = []
                if history_file.exists():
                    with open(history_file, "r", encoding="utf-8") as f:
                        history = json.load(f)
                        # Build messages array for API
                        for msg in history.get("messages", []):
                            # If the message has images, format them according to OpenAI's format for vision models
                            if msg.get("images"):
                                content_parts = []
                                # Add text content
                                if msg["content"]:
                                    content_parts.append({
                                        "type": "text",
                                        "text": msg["content"]
                                    })
                                
                                # Add each image as image_url with base64 format
                                for img_base64 in msg["images"]:
                                    content_parts.append({
                                        "type": "image_url",
                                        "image_url": {
                                            "url": f"data:image/png;base64,{img_base64}"
                                        }
                                    })
                                
                                message_obj = {
                                    "role": msg["role"],
                                    "content": content_parts
                                }
                            else:
                                # For text-only messages, use the standard format
                                message_obj = {
                                    "role": msg["role"],
                                    "content": msg["content"]
                                }
                            messages.append(message_obj)
                
                # Use OpenAI-compatible API for Ollama
                ollama_client = openai.OpenAI(
                    base_url=f"{config.get('ollamaUrl')}/v1",
                    api_key="ollama"  # required but unused
                )
                
                # Send message to Ollama using OpenAI compatibility
                stream = ollama_client.chat.completions.create(
                    model=config.get('ollamaModel'),
                    messages=messages,
                    stream=True,
                    max_tokens=4096  # Set a reasonable max_tokens value to ensure complete responses
                )
                
                accumulated_response = ""
                for chunk in stream:
                    if chunk.choices[0].delta.content:
                        content_chunk = chunk.choices[0].delta.content
                        accumulated_response += content_chunk
                        await websocket.send_json({
                            "type": "stream",
                            "content": content_chunk
                        })
                
                # Save assistant message
                self.save_message(history_id, "assistant", accumulated_response)

                # Generate title if this is a new chat
                if not data.get("historyId"):
                    title = await self.generate_title(content, config)
                    self.update_chat_title(history_id, title)
                
                # Send end message
                await websocket.send_json({
                    "type": "end",
                    "content": accumulated_response,
                    "historyId": history_id
                })
                
                # Send updated histories after title generation
                await self.send_chat_histories(websocket)
                
            except Exception as e:
                print(f"Error processing message: {str(e)}")
                await websocket.send_json({
                    "type": "error",
                    "content": str(e)
                })
        
        elif message_type == "load_history":
            history_id = data.get("historyId")
            await self.load_chat_history(websocket, history_id)
        
        elif message_type == "delete_history":
            history_id = data.get("historyId")
            success = self.delete_history(history_id)
            await websocket.send_json({
                "type": "delete_response",
                "success": success,
                "historyId": history_id
            })
            if success:
                # Resend updated histories list
                await self.send_chat_histories(websocket)

    async def generate_title(self, first_message: str, config: dict) -> str:
        try:
            prompt = """Please generate a concise title (maximum 30 characters) based on the following first message from a conversation. The title should capture the main topic or intent.
            
            Message: {message}
            
            Generate only the title without any explanation or additional text. and put into <title></title> tags."""
            
            # Use OpenAI-compatible API for Ollama
            ollama_client = openai.OpenAI(
                base_url=f"{config.get('ollamaUrl')}/v1",
                api_key="ollama"  # required but unused
            )
            
            # Use chat completions API
            response = ollama_client.chat.completions.create(
                model=self.small_model,
                messages=[
                    {"role": "user", "content": prompt.format(message=first_message)}
                ],
                stream=False
            )
            
            title = response.choices[0].message.content.strip()
            # Extract title from tags if present
            tag_match = re.search(r'<title>(.*?)</title>', title)
            if tag_match:
                title = tag_match.group(1).strip()
            # Ensure title is not too long and add timestamp
            current_time = datetime.now().strftime('%H:%M')
            title = f"{title[:20]} [{current_time}]" if len(title) > 20 else f"{title} [{current_time}]"
            return title
            
        except Exception as e:
            print(f"Error generating title: {e}")
            return f"Chat [{datetime.now().strftime('%H:%M')}]"

    def update_chat_title(self, history_id: str, title: str):
        history_file = self.chat_history_dir / f"{history_id}.json"
        if history_file.exists():
            with open(history_file, "r", encoding="utf-8") as f:
                history = json.load(f)
            
            history["title"] = title
            
            with open(history_file, "w", encoding="utf-8") as f:
                json.dump(history, f, indent=2, ensure_ascii=False)

    def save_message(self, history_id: str, role: str, content: str, images: list = None):
        history_file = self.chat_history_dir / f"{history_id}.json"
        
        if history_file.exists():
            with open(history_file, "r", encoding="utf-8") as f:
                history = json.load(f)
        else:
            current_time = datetime.now().strftime('%H:%M')
            history = {
                "id": history_id,
                "title": f"Chat [{current_time}]",
                "timestamp": datetime.now().isoformat()
            }
        
        if "messages" not in history:
            history["messages"] = []
            
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        
        if images:
            message["images"] = images
            
        history["messages"].append(message)
        
        with open(history_file, "w", encoding="utf-8") as f:
            json.dump(history, f, indent=2, ensure_ascii=False)

    async def send_chat_histories(self, websocket: WebSocket):
        histories = []
        for history_file in self.chat_history_dir.glob("*.json"):
            with open(history_file, "r", encoding="utf-8") as f:
                history = json.load(f)
                histories.append({
                    "id": history["id"],
                    "title": history["title"],
                    "timestamp": history["timestamp"]
                })
        
        await websocket.send_json({
            "type": "history",
            "histories": sorted(histories, key=lambda x: x["timestamp"], reverse=True)
        })

    async def load_chat_history(self, websocket: WebSocket, history_id: str):
        history_file = self.chat_history_dir / f"{history_id}.json"
        if history_file.exists():
            try:
                with open(history_file, "r", encoding="utf-8") as f:
                    history = json.load(f)
                    
                    # Send each message with all its data including images
                    for message in history.get("messages", []):
                        message_data = {
                            "type": "message",
                            "role": message["role"],
                            "content": message["content"],
                            "timestamp": message["timestamp"]
                        }
                        # Only include images if they exist
                        if "images" in message:
                            message_data["images"] = message["images"]
                        
                        await websocket.send_json(message_data)
            except Exception as e:
                print(f"Error loading chat history: {e}")
                await websocket.send_json({
                    "type": "error",
                    "content": f"Failed to load chat history: {str(e)}"
                })

    def delete_history(self, history_id: str) -> bool:
        try:
            history_file = self.chat_history_dir / f"{history_id}.json"
            if history_file.exists():
                remove(history_file)
                return True
            return False
        except Exception as e:
            print(f"Error deleting history file: {e}")
            return False