import os
import base64
import io
from pathlib import Path
from PIL import Image
from byaldi import RAGMultiModalModel
from together import Together
import openai
import gc
import torch
from dotenv import load_dotenv

load_dotenv()


class ColpaliProcessor:
    def __init__(self, model_path="vidore/colqwen2-v0.1"):
        self.model_path = model_path
        self.model = None
        self.together_client = Together(api_key=os.getenv("TOGETHER_API_KEY"))
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Parent of backend folder
        self.cost_budget = os.getenv('COST_BUDGET', 'high')
                    

    def create_index(self, pdf_path, index_name):
        self.cleanup_memory()
        self.model = RAGMultiModalModel.from_pretrained(self.model_path, verbose=1, index_root=os.path.join(self.base_dir, ".byaldi"))
        self.model.index(
            input_path=Path(pdf_path),
            index_name=index_name,
            store_collection_with_index=True,
            overwrite=True            
        )
        self.cleanup_memory()

    def load_index(self, index_name):       
        # Force garbage collection to reclaim memory
        self.cleanup_memory()
        # self.model = RAGMultiModalModel.from_pretrained(self.model_path, verbose=1)
        # Load the new index
        self.model = RAGMultiModalModel.from_index(index_name, verbose=0, index_root=os.path.join(self.base_dir, ".byaldi"))
        

    def search_and_merge_pages(self, query, k=5, max_image_size=(800, 800), padding=5):
        if not self.model:
            raise ValueError("No index loaded. Please create or load an index first.")

        results = self.model.search(query, k=k)
        
        images = []
        result_info = f"Search results for '{query}':\n"
        
        for i, result in enumerate(results, 1):
            result_info += f"Result {i}:\n"
            result_info += f"  Doc ID: {result.doc_id}\n"
            result_info += f"  Page: {result.page_num}\n"
            result_info += f"  Score: {result.score}\n\n"
            
            image_bytes = base64.b64decode(result.base64)
            img = Image.open(io.BytesIO(image_bytes))
            img = img.convert('L')  # Convert to grayscale
            img.thumbnail(max_image_size, Image.LANCZOS)
            images.append(img)
        
        total_width = sum(img.width for img in images) + padding * (len(images) - 1)
        max_height = max(img.height for img in images)
        
        merged_image = Image.new('L', (total_width, max_height), 255)
        
        x_offset = 0
        for img in images:
            merged_image.paste(img, (x_offset, (max_height - img.height) // 2))
            x_offset += img.width + padding
        
        return merged_image, result_info

    def process_query(self, query, k=5, max_image_size=(1024, 1024)):
        merged_image, result_info = self.search_and_merge_pages(query, k, max_image_size)
        
        buffered = io.BytesIO()
        merged_image.save(buffered, format="PNG", optimize=True, quality=85)
        merged_image_base64 = base64.b64encode(buffered.getvalue()).decode()

        prompt = f"""You are a helpful assistant that can answer questions about the provided image. 
        The user is asking a question about the content of the book. Answer the question based on the content of the image. 
        If you cannot find the answer in the image, say so. If you can find the answer, provide a detailed and long answer.
        pls use the language of the book to answer the question.
        \n\nQuestion: {query}"""
        
        if self.cost_budget == 'low':
            response = self.together_client.chat.completions.create(
                model="meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{merged_image_base64}",
                                },
                            },
                        ],
                    }
                ],
                max_tokens=1024,
            )
        else:
            if self.cost_budget == 'medium':
                model = "gpt-4o-mini"
            else:
                model = "gpt-4o"
                
            response = self.openai_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt},
                    {   
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{merged_image_base64}",
                                },
                            },
                        ],
                    },
                ],
                max_tokens=1024,
            )

        llm_response = response.choices[0].message.content

        return {
            "merged_image": merged_image_base64,
            "result_info": result_info,
            "llm_response": llm_response
        }

    @staticmethod
    def get_indexes():
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Parent of backend folder
        byaldi_dir = os.path.join(base_dir, ".byaldi")
        if not os.path.exists(byaldi_dir):
            os.makedirs(byaldi_dir)
        return [f.name for f in os.scandir(byaldi_dir) if f.is_dir()]

    def cleanup_memory(self):
        """
        Clean up memory after query completion.
        This function forces garbage collection and empties CUDA cache if available.
        """
        if self.model is not None:
            del self.model
            self.model = None

        # Force garbage collection to reclaim memory
        gc.collect()
        
        # If using CUDA, empty the cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()        
            
        print("Memory cleanup completed.")
