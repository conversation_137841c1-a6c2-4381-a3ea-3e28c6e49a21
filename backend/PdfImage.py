from anthropic import Anthropic
from PyPDF2 import PdfReader
import PyPDF2
import os
import fitz
import base64
from PIL import Image
import io
import tempfile
import shutil
from PyPDF2.generic import IndirectObject
from openai import OpenAI

class PDFProcessor:
    def __init__(self, api_key=None, provider="openrouter", model_name=None):
        if api_key is None:
            if provider == "anthropic":
                api_key = os.getenv("ANTHROPIC_API_KEY")
            elif provider == "openrouter":
                api_key = os.getenv("OPENROUTER_API_KEY")        

        if model_name is None:
            if provider == "anthropic":
                model_name = "claude-3-opus-20240229"
            elif provider == "openrouter":
                model_name = "meta-llama/llama-3.2-90b-vision-instruct"

        if provider == "anthropic":            
            self.client = Anthropic(api_key=api_key,
                                    base_url="https://anthropic.helicone.ai",
                                    default_headers={"Helicone-Auth": f"Bearer {os.getenv('HELICONE_API_KEY')}"}
                                    )
        elif provider == "openrouter":
            self.client = OpenAI(
                base_url="https://openrouter.ai/api/v1",
                api_key=api_key,
            )
        else:
            raise ValueError(f"Unsupported provider: {provider}")
        
        self.model_name = model_name
        self.provider = provider
        self.haiku_prompt = """
        You will be extracting information from images in a PDF file and converting it into a well-organized and coherent JSON format. 
        Finally, double-check your work for accuracy and completeness. Make sure that you have captured all the important information 
        from each image and that the text accurately reflects the content of the tables and graphs. When extracting table information, 
        please ensure that the output JSON includes the relationship of nested subtables. 
        Please provide your final output inside a code block and Must use Japanese. Dont output explanation or comments. 
        Output only the JSON format."""
        self.temp_dir = tempfile.mkdtemp()

    def __del__(self):
        self.cleanup()

    def cleanup(self):
        if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def _pdf_to_base64_pngs(self, pdf_path, page_num, quality=75, max_size=(1024, 1024)):
        doc = fitz.open(pdf_path)
        page = doc.load_page(page_num)
        pix = page.get_pixmap(matrix=fitz.Matrix(300/72, 300/72))
        image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        
        if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
        
        image_path = os.path.join(self.temp_dir, f"{os.path.basename(pdf_path)}_{page_num}.png")
        image.save(image_path, format='PNG', optimize=True, quality=quality)
        
        image_data = io.BytesIO()
        image.save(image_data, format='PNG', optimize=True, quality=quality)
        image_data.seek(0)
        base64_encoded = base64.b64encode(image_data.getvalue()).decode('utf-8')
        
        doc.close()
        
        return base64_encoded, page_num

    def _extract_info(self, pdf_path, page_num):
        base64_encoded_png, page_num = self._pdf_to_base64_pngs(pdf_path, page_num)
        
        if self.provider == "anthropic":
            messages = [
                {
                    "role": "user",
                    "content": [
                            {"type": "image", "source": {"type": "base64", "media_type": "image/png", "data": base64_encoded_png}},
                            {"type": "text", "text": self.haiku_prompt}
                        ]
                    }
                ]
        
        elif self.provider == "openrouter":
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_encoded_png}"
                            }          
                        },
                        {"type": "text", "text": self.haiku_prompt}
                    ]
                }
            ]
                        
        if self.provider == "anthropic":
            response = self.client.messages.create(
                model=self.model_name,
                max_tokens=4096,
                messages=messages
            )
            return response.content[0].text, page_num
        elif self.provider == "openrouter":
            response = self.client.chat.completions.create(
                model=self.model_name,
                temperature=0.0,
                max_tokens=8192,
                messages=messages
            )
            return response.choices[0].message.content, page_num        
        

    def _extract_text_from_pdf(self, pdf_path):
        with open(pdf_path, 'rb') as pdf_file:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            text = ''
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text()
        return text

    def _create_output_file(self, pdf_path, suffix=""):
        pdf_filename = os.path.basename(pdf_path)
        output_file = os.path.join(self.temp_dir, f"{os.path.splitext(pdf_filename)[0]}{suffix}.txt")
        return output_file

    def _save_extracted_info(self, output_file, extracted_info):
        with open(output_file, "w", encoding="utf-8") as file:
            file.write(extracted_info)
        print(f"Extracted text saved to: {output_file}")

    def process_pdf(self, pdf_path, max_pages=-1):
        pdf_reader = PdfReader(pdf_path)
        extracted_info = ''
        
        if max_pages == -1:
            max_pages = len(pdf_reader.pages)
        
        for page_num in range(max_pages):
            page = pdf_reader.pages[page_num]
            try:
                resources = page.get('/Resources', {})
                if isinstance(resources, IndirectObject):
                    resources = resources.get_object()
                
                if '/XObject' in resources:
                    info, _ = self._extract_info(pdf_path, page_num)
                    extracted_info += f"Page {page_num + 1}:\n{info}\n\n"
                    # We'll keep this temporary file creation for individual pages
                    output_file = self._create_output_file(pdf_path, f"_page{page_num + 1}")
                    self._save_extracted_info(output_file, info)
                else:
                    text = page.extract_text()
                    extracted_info += f"Page {page_num + 1}:\n{text}\n\n"
            except Exception as e:
                print(f"Warning: Unable to process page {page_num + 1}. Error: {str(e)}. Skipping...")
                extracted_info += f"Page {page_num + 1}: Unable to process. Error: {str(e)}\n\n"
        
        self.cleanup()
        return extracted_info

    def process_pdf_range(self, pdf_path, start_page, end_page=-1):
        pdf_reader = PdfReader(pdf_path)
        extracted_info = ''

        if end_page == -1:
            end_page = len(pdf_reader.pages)

        for page_num in range(start_page - 1, end_page):
            page = pdf_reader.pages[page_num]
            try:
                resources = page.get('/Resources', {})
                if isinstance(resources, IndirectObject):
                    resources = resources.get_object()
                
                if '/XObject' in resources:
                    info, _ = self._extract_info(pdf_path, page_num)
                    extracted_info += f"Page {page_num + 1}:\n{info}\n\n"
                    # We'll keep this temporary file creation for individual pages
                    output_file = self._create_output_file(pdf_path, f"_page{page_num + 1}")
                    self._save_extracted_info(output_file, info)
                else:
                    text = page.extract_text()
                    extracted_info += f"Page {page_num + 1}:\n{text}\n\n"
            except Exception as e:
                print(f"Warning: Unable to process page {page_num + 1}. Error: {str(e)}. Skipping...")
                extracted_info += f"Page {page_num + 1}: Unable to process. Error: {str(e)}\n\n"
        self.cleanup()
        return extracted_info

# 使用示例
if __name__ == "__main__":
    api_key = "sk-or-v1-xxx"
    pdf_processor = PDFProcessor(api_key)
    
    pdf_path = "/opt/workspace/app/cursor/jina/backend/pdfs/late-Chunking2409.04701v1.pdf"
    # pdf_path = "/opt/workspace/app/cursor/jina/backend/pdfs/DiT2212.09748v2.pdf"
    
    try:
        # 处理前31页
        result = pdf_processor.process_pdf(pdf_path, -1)
        print(result)  # Or save result to a file here if needed
        
        # 处理第30页到第38页
        # result = pdf_processor.process_pdf_range(pdf_path, 30, 38)
        # print(result)  # Or save result to a file here if needed
    finally:
        # 清理临时文件
        pdf_processor.cleanup()