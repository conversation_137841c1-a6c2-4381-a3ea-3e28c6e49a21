import json
import os
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from fastapi import WebSocket, WebSocketDisconnect
import uuid
from dotenv import load_dotenv
from mcp_client import MCPClient, MCPLLMIntegration
from anthropic import AsyncAnthropic

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MCPChatHandler:
    def __init__(self):
        self.chat_history_dir = Path("chathist/mcp")
        self.chat_history_dir.mkdir(exist_ok=True, parents=True)
        load_dotenv()
        
        # Default MCP configurations
        self.default_model = os.getenv('DEFAULT_MCP_MODEL', 'claude-3-7-sonnet-20250219')
        self.anthropic_api_key = os.getenv('ANTHROPIC_API_KEY', '')
        
        # System prompts configuration
        self.prompts_file = Path("prompts.json")
        if not self.prompts_file.exists():
            self.prompts_file = Path("backend/prompts.json")
            
        # Create default prompts file if it doesn't exist
        if not self.prompts_file.exists():
            with open(self.prompts_file, "w", encoding="utf-8") as f:
                json.dump({"system_prompt": ""}, f, indent=4)
                
        # Active client sessions
        self.mcp_clients = {}
        self.llm_integrations = {}

    async def initialize_mcp_client(self, session_id, config=None):
        """Initialize a new MCP client for a session"""
        try:
            if session_id in self.mcp_clients:
                # Close existing client if exists
                await self.mcp_clients[session_id].close()
            
            # Create new MCP client instance
            mcp_client = await MCPClient.create()
            await mcp_client.initialize()
            
            # Store in active clients
            self.mcp_clients[session_id] = mcp_client
            
            # Create LLM integration
            model = config.get('model', self.default_model) if config else self.default_model
            api_key = config.get('api_key', self.anthropic_api_key) if config else self.anthropic_api_key
            max_iterations = config.get('max_iterations', 10) if config else 10
            
            llm_integration = mcp_client.create_llm_integration(
                api_key=api_key, 
                model=model,
                max_iterations=max_iterations
            )
            self.llm_integrations[session_id] = llm_integration
            
            # Log available tools for debugging
            all_tools = await mcp_client.list_all_tools()
            logger.info(f"Initialized MCP client with {len(all_tools)} tools")
            
            if all_tools:
                logger.info(f"Tool types: {[type(t).__name__ for t in all_tools[:3]]}")
                logger.info(f"First tool attributes: {dir(all_tools[0])}")
            
            return True
        except Exception as e:
            logger.error(f"Failed to initialize MCP client: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def handle_message(self, websocket: WebSocket, data: dict):
        message_type = data.get("type")
        
        if message_type == "get_histories":
            await self.send_chat_histories(websocket)
        
        elif message_type == "initialize":
            session_id = data.get("sessionId", str(uuid.uuid4()))
            config = data.get("config", {})
            success = await self.initialize_mcp_client(session_id, config)
            
            # Return available tools after initialization
            tools = []
            if success and session_id in self.mcp_clients:
                tools = await self.mcp_clients[session_id].format_tools_for_llm()
            
            # Load system prompt
            system_prompt = self.load_system_prompt()
            
            # Get max_iterations value from the LLM integration
            max_iterations = 10  # Default
            if session_id in self.llm_integrations:
                llm_integration = self.llm_integrations[session_id]
                if hasattr(llm_integration, 'max_iterations'):
                    max_iterations = llm_integration.max_iterations
            
            await websocket.send_json({
                "type": "initialize_response",
                "success": success,
                "sessionId": session_id,
                "tools": tools,
                "system_prompt": system_prompt,
                "max_iterations": max_iterations
            })
        
        elif message_type == "save_system_prompt":
            prompt = data.get("prompt", "")
            success = self.save_system_prompt(prompt)
            
            await websocket.send_json({
                "type": "system_prompt_saved",
                "success": success
            })
        
        elif message_type == "status_request":
            await self.handle_status_request(websocket, data)
        
        elif message_type == "get_mcp_config":
            await self.handle_get_mcp_config(websocket)
        
        elif message_type == "update_mcp_config":
            await self.handle_update_mcp_config(websocket, data)
        
        elif message_type == "message":
            content = data.get("content")
            session_id = data.get("sessionId")
            history_id = data.get("historyId")
            system_prompt = data.get("systemPrompt")  # Get system prompt if provided
            
            logger.info(f"Processing message with historyId: {history_id if history_id else 'NEW CONVERSATION'}")
            
            # If system prompt is provided, save it
            if system_prompt is not None:
                self.save_system_prompt(system_prompt)
            else:
                # If not provided, try to load from file
                system_prompt = self.load_system_prompt()
            
            if not session_id or session_id not in self.mcp_clients:
                await websocket.send_json({
                    "type": "error",
                    "content": "MCP client not initialized. Please initialize first."
                })
                return
            
            # If no history_id provided, this is a new conversation
            is_new_conversation = not history_id
            if is_new_conversation:
                history_id = datetime.now().strftime("%Y%m%d_%H%M%S")
                logger.info(f"Created new history_id: {history_id}")
            else:
                logger.info(f"Using existing history_id: {history_id}")
                
                # Verify that the history file exists
                history_file = self.chat_history_dir / f"{history_id}.json"
                if not history_file.exists():
                    logger.warning(f"History file {history_id}.json not found, but historyId was provided. Creating new file.")
                    # We'll let the save_message create a new file
            
            # Save user message
            self.save_message(history_id, "user", content)
            
            try:
                # Get chat history and build messages array
                messages = []
                
                # Add system prompt as the first message if provided
                if system_prompt:
                    logger.info("Adding system prompt to conversation")
                    messages.append({
                        "role": "system",
                        "content": system_prompt
                    })
                
                history_file = self.chat_history_dir / f"{history_id}.json"
                if history_file.exists():
                    with open(history_file, "r", encoding="utf-8") as f:
                        history = json.load(f)
                        for msg in history.get("messages", []):
                            messages.append({
                                "role": msg["role"],
                                "content": msg["content"]
                            })
                
                # Process messages with MCP client
                llm_integration = self.llm_integrations[session_id]
                
                # First, signal processing is starting
                await websocket.send_json({
                    "type": "processing_started"
                })
                
                # Track the accumulated response for saving later
                accumulated_response = ""
                
                # Define the callback function for handling the stream events
                async def handle_stream_event(event):
                    nonlocal accumulated_response
                    
                    # Debug output
                    logger.info(f"Stream event: {event.type}")
                    
                    try:
                        # Different handling based on event type
                        event_type = event.type
                        
                        # Create a corresponding client event
                        client_event = {
                            "type": "llm_update",
                            "update_type": event_type
                        }
                        
                        # For thinking deltas, we want to ensure they're processed immediately
                        # and not batched with other events
                        if event_type == "content_block_delta" and event.delta.type == "thinking_delta":
                            thinking_text = event.delta.thinking
                            logger.info(f"Thinking delta received: {thinking_text[:50]}...")
                            
                            # Create specific thinking delta event
                            thinking_event = {
                                "type": "llm_update",
                                "update_type": "content_block_delta",
                                "delta_type": "thinking_delta",
                                "thinking": thinking_text
                            }
                            
                            # Send thinking delta immediately
                            try:
                                await websocket.send_json(thinking_event)
                                logger.info(f"Thinking delta sent successfully ({len(thinking_text)} chars)")
                            except Exception as thinking_error:
                                logger.error(f"Error sending thinking delta: {str(thinking_error)}")
                            
                            # Return early to avoid sending the same event twice
                            return
                        
                        # Regular event processing for other event types
                        if event_type == "content_block_start":
                            # Debug
                            logger.info(f"Content block start: {event.content_block.type}")
                            
                            client_event["content_block_type"] = event.content_block.type
                            
                            if event.content_block.type == "tool_use":
                                # Add tool information
                                client_event["name"] = event.content_block.name
                                client_event["input"] = event.content_block.input
                                client_event["tool_id"] = event.content_block.id
                            
                        elif event_type == "content_block_delta":
                            # Debug based on delta type
                            delta_type = event.delta.type
                            client_event["delta_type"] = delta_type
                            
                            if delta_type == "text_delta":
                                text = event.delta.text
                                logger.info(f"Text delta: {text[:20]}...")
                                accumulated_response += text
                                client_event["text"] = text
                                
                            elif delta_type == "input_json_delta":
                                json_text = event.delta.partial_json
                                logger.info(f"JSON delta: {json_text[:20]}...")
                                client_event["json"] = json_text
                            
                        elif event_type == "content_block_stop":
                            logger.info(f"Content block stop: {event.content_block.type}")
                            client_event["content_block_type"] = event.content_block.type
                            
                            # Include the complete content block for tool_use
                            if event.content_block.type == "tool_use":
                                client_event["tool_name"] = event.content_block.name
                                client_event["tool_input"] = event.content_block.input
                                client_event["tool_id"] = event.content_block.id
                        
                        # Log the client event before sending
                        logger.debug(f"Sending client event: {client_event}")
                        
                        # Send the event to the client
                        await websocket.send_json(client_event)
                        
                    except Exception as e:
                        logger.error(f"Error handling stream event: {str(e)}")
                        import traceback
                        logger.error(traceback.format_exc())
                
                # Define the tool call callback
                async def tool_call_handler(tool_call_record):
                    try:
                        logger.info(f"Tool called: {tool_call_record['tool_name']}")
                        
                        # Create a tool result message
                        tool_result = {
                            "type": "tool_call_result",
                            "tool_id": tool_call_record['tool_id'],
                            "name": tool_call_record['tool_name'],
                            "input": tool_call_record['tool_input'],
                            "success": bool(tool_call_record['success']),  # Ensure this is a boolean
                            "content": str(tool_call_record['content']) if tool_call_record['success'] else '',
                            "error": str(tool_call_record['error']) if not tool_call_record['success'] else ''
                        }
                        
                        # Log what we're about to send
                        logger.info(f"Sending tool result for: {tool_result['name']}")
                        logger.debug(f"Tool result data: {json.dumps(tool_result)}")
                        
                        # Send the tool result
                        await websocket.send_json(tool_result)
                        
                        # Wait a short time to ensure tool result is processed before continuing
                        # This helps prevent race conditions in the UI
                        await asyncio.sleep(0.1)
                        
                        logger.info(f"Successfully sent tool result for: {tool_result['name']}")
                    except Exception as e:
                        logger.error(f"Error handling tool call: {str(e)}")
                        import traceback
                        logger.error(traceback.format_exc())
                        
                        # Try to send an error response
                        try:
                            error_result = {
                                "type": "tool_call_result",
                                "tool_id": tool_call_record.get('tool_id', 'unknown'),
                                "name": tool_call_record.get('tool_name', 'Unknown Tool'),
                                "input": tool_call_record.get('tool_input', {}),
                                "success": False,
                                "error": f"Failed to process tool result: {str(e)}"
                            }
                            await websocket.send_json(error_result)
                        except Exception as inner_e:
                            logger.error(f"Critical error sending fallback tool result: {str(inner_e)}")
                
                # Register the tool call callback
                llm_integration.register_tool_call_callback(tool_call_handler)
                
                # Process the messages with streaming
                response = await llm_integration.process_messages_stream(
                    messages=messages,
                    max_tokens=16384,
                    thinking_budget=3000,  # Increased thinking budget to ensure we get thinking content
                    callback=handle_stream_event
                )
                
                # Save assistant message
                self.save_message(history_id, "assistant", accumulated_response)
                
                # Generate title if this is a new chat - only do this once per conversation
                if is_new_conversation:
                    title = await self.generate_title(content, session_id, llm_integration)
                    self.update_chat_title(history_id, title)
                
                # Send processing complete message
                await websocket.send_json({
                    "type": "processing_complete",
                    "content": accumulated_response,
                    "historyId": history_id,
                    "messageCount": len(history.get("messages", [])) if history_file.exists() and history else 0
                })
                
                logger.info(f"Sent processing_complete with historyId: {history_id}")
                
                # Send updated histories
                await self.send_chat_histories(websocket)
                
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                await websocket.send_json({
                    "type": "error",
                    "content": str(e)
                })
        
        elif message_type == "load_history":
            history_id = data.get("historyId")
            await self.load_chat_history(websocket, history_id)
        
        elif message_type == "delete_history":
            history_id = data.get("historyId")
            logger.info(f"Received delete request for history: {history_id}")
            
            success = self.delete_history(history_id)
            
            # Send delete response
            await websocket.send_json({
                "type": "delete_response",
                "success": success,
                "historyId": history_id
            })
            
            # Always send updated histories list regardless of success
            # This ensures the client has the latest state
            await self.send_chat_histories(websocket)
            
            if success:
                logger.info(f"Successfully deleted history: {history_id}")
            else:
                logger.warning(f"Failed to delete history: {history_id}")
        
        elif message_type == "get_tools":
            session_id = data.get("sessionId")
            if not session_id or session_id not in self.mcp_clients:
                await websocket.send_json({
                    "type": "error",
                    "content": "MCP client not initialized. Please initialize first."
                })
                return
            
            try:
                mcp_client = self.mcp_clients[session_id]
                tools = await mcp_client.format_tools_for_llm()
                
                await websocket.send_json({
                    "type": "tools_response",
                    "tools": tools
                })
            except Exception as e:
                logger.error(f"Error getting tools: {str(e)}")
                await websocket.send_json({
                    "type": "error",
                    "content": f"Failed to get tools: {str(e)}"
                })

    async def generate_title(self, first_message: str, session_id: str, llm_integration: MCPLLMIntegration) -> str:
        try:
            # Get API key from environment or config
            api_key = os.environ.get("ANTHROPIC_API_KEY", "")
            if not api_key:
                api_key = self.anthropic_api_key
            
            if not api_key:
                logger.error("No Anthropic API key found for title generation")
                return f"MCP Chat [{datetime.now().strftime('%H:%M')}]"
            
            # Create Anthropic client
            client = AsyncAnthropic(api_key=api_key)
            
            prompt = """Please generate a concise title (maximum 30 characters) based on the following first message from a conversation. The title should capture the main topic or intent.
            
            Message: {message}
            
            Generate only the title without any explanation or additional text."""
            
            # Direct API call to Claude
            response = await client.messages.create(
                model=self.default_model,
                max_tokens=100,
                messages=[
                    {"role": "user", "content": prompt.format(message=first_message)}
                ]
            )
            
            title = response.content[0].text.strip()
            
            # Ensure title is not too long and add timestamp
            current_time = datetime.now().strftime('%H:%M')
            title = f"{title[:20]} [{current_time}]" if len(title) > 20 else f"{title} [{current_time}]"
            
            return title
            
        except Exception as e:
            logger.error(f"Error generating title with Claude API: {e}")
            return f"MCP Chat [{datetime.now().strftime('%H:%M')}]"

    def update_chat_title(self, history_id: str, title: str):
        history_file = self.chat_history_dir / f"{history_id}.json"
        if history_file.exists():
            with open(history_file, "r", encoding="utf-8") as f:
                history = json.load(f)
            
            history["title"] = title
            
            with open(history_file, "w", encoding="utf-8") as f:
                json.dump(history, f, indent=2, ensure_ascii=False)

    def save_message(self, history_id: str, role: str, content: str):
        history_file = self.chat_history_dir / f"{history_id}.json"
        
        logger.info(f"Saving {role} message to history: {history_id}")
        
        if history_file.exists():
            with open(history_file, "r", encoding="utf-8") as f:
                history = json.load(f)
            # Make sure history has a valid title - this handles legacy files 
            # that might not have a proper title
            if not history.get("title") or history.get("title") == f"MCP Chat [{datetime.now().strftime('%H:%M')}]":
                logger.info(f"Existing history {history_id} has default title, keeping as is")
        else:
            # This is a new conversation
            current_time = datetime.now().strftime('%H:%M')
            logger.info(f"Creating new history file for ID: {history_id}")
            history = {
                "id": history_id,
                "title": f"MCP Chat [{current_time}]",  # Default title, will be updated later
                "timestamp": datetime.now().isoformat()
            }
        
        if "messages" not in history:
            history["messages"] = []
            
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
            
        history["messages"].append(message)
        
        # Store the latest timestamp as the conversation timestamp
        history["timestamp"] = datetime.now().isoformat()
        
        with open(history_file, "w", encoding="utf-8") as f:
            json.dump(history, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Successfully saved message to {history_id}, message count: {len(history['messages'])}")

    async def send_chat_histories(self, websocket: WebSocket):
        try:
            histories = []
            
            # Make sure the directory exists
            if not self.chat_history_dir.exists():
                logger.warning(f"Chat history directory does not exist: {self.chat_history_dir}")
                self.chat_history_dir.mkdir(exist_ok=True, parents=True)
            
            # Get a fresh list of files - don't rely on cached state
            history_files = list(self.chat_history_dir.glob("*.json"))
            logger.info(f"Found {len(history_files)} history files")
            
            for history_file in history_files:
                try:
                    with open(history_file, "r", encoding="utf-8") as f:
                        history = json.load(f)
                        histories.append({
                            "id": history["id"],
                            "title": history["title"],
                            "timestamp": history["timestamp"]
                        })
                except Exception as e:
                    logger.error(f"Error reading history file {history_file}: {str(e)}")
                    # Skip corrupted files
                    continue
            
            # Sort by timestamp in descending order (newest first)
            histories = sorted(histories, key=lambda x: x["timestamp"], reverse=True)
            
            await websocket.send_json({
                "type": "history",
                "histories": histories
            })
            logger.info(f"Sent {len(histories)} chat histories to client")
        except Exception as e:
            logger.error(f"Error sending chat histories: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # Try to send an empty list as fallback
            try:
                await websocket.send_json({
                    "type": "history",
                    "histories": []
                })
            except Exception:
                logger.error("Failed to send fallback empty histories list")

    async def load_chat_history(self, websocket: WebSocket, history_id: str):
        history_file = self.chat_history_dir / f"{history_id}.json"
        if history_file.exists():
            try:
                with open(history_file, "r", encoding="utf-8") as f:
                    history = json.load(f)
                    
                    # First send the chat title and history ID for client context
                    await websocket.send_json({
                        "type": "history_metadata",
                        "historyId": history["id"],
                        "title": history.get("title", f"MCP Chat [{datetime.now().strftime('%H:%M')}]")
                    })
                    
                    # Send each message in order
                    for message in history.get("messages", []):
                        await websocket.send_json({
                            "type": "message",
                            "role": message["role"],
                            "content": message["content"],
                            "timestamp": message["timestamp"],
                            "historyId": history_id  # Include historyId in each message
                        })
                    
                    logger.info(f"Loaded chat history with {len(history.get('messages', []))} messages for ID: {history_id}")
            except Exception as e:
                logger.error(f"Error loading chat history: {e}")
                import traceback
                logger.error(traceback.format_exc())
                await websocket.send_json({
                    "type": "error",
                    "content": f"Failed to load chat history: {str(e)}"
                })
        else:
            logger.warning(f"History file not found: {history_id}")
            await websocket.send_json({
                "type": "error",
                "content": f"Chat history not found: {history_id}"
            })

    def delete_history(self, history_id: str) -> bool:
        try:
            history_file = self.chat_history_dir / f"{history_id}.json"
            logger.info(f"Attempting to delete history file: {history_file}")
            
            if history_file.exists():
                # Check if file is accessible and not locked
                if os.access(history_file, os.W_OK):
                    # Use pathlib's unlink which is more reliable than os.remove
                    history_file.unlink()
                    
                    # Verify the file was actually deleted
                    if not history_file.exists():
                        logger.info(f"Successfully deleted history file: {history_file}")
                        return True
                    else:
                        logger.error(f"Failed to delete history file: {history_file} (file still exists after deletion)")
                        return False
                else:
                    logger.error(f"Cannot delete history file: {history_file} (no write permission)")
                    return False
            else:
                logger.warning(f"History file not found: {history_file}")
                return False
        except Exception as e:
            logger.error(f"Error deleting history file: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    async def handle_status_request(self, websocket: WebSocket, data: dict):
        """Handle a request for MCP status and tools categorized by server"""
        session_id = data.get("sessionId")
        
        if not session_id and hasattr(self, 'mcp_clients') and self.mcp_clients:
            # Use the first available client if no session ID provided
            session_id = list(self.mcp_clients.keys())[0]
            logger.info(f"No session ID provided, using first available: {session_id}")
        
        try:
            if not session_id or session_id not in self.mcp_clients:
                logger.error(f"MCP client not initialized or session not found: {session_id}")
                await websocket.send_json({
                    "type": "status_update",
                    "status": "error",
                    "message": "MCP client not initialized or session not found"
                })
                return
                
            mcp_client = self.mcp_clients[session_id]
            logger.info(f"Processing status request for session {session_id}")
            
            # Get all tools from the client
            all_tools = await mcp_client.list_all_tools()
            logger.info(f"Found {len(all_tools)} total tools")
            
            # Log tool properties for debugging
            if all_tools:
                sample_tool = all_tools[0]
                logger.info(f"Sample tool properties: {dir(sample_tool)}")
                logger.info(f"Sample tool dict: {sample_tool.__dict__ if hasattr(sample_tool, '__dict__') else 'No __dict__'}")
            
            # Organize tools by server and count
            tools_by_server = {}
            server_tool_counts = {}
            
            for tool in all_tools:
                server_id = tool.server_id
                if server_id not in tools_by_server:
                    tools_by_server[server_id] = []
                    server_tool_counts[server_id] = 0
                
                server_tool_counts[server_id] += 1
                
                tool_data = {
                    "name": tool.name,
                    "description": tool.description,
                }
                
                # Safely add parameters if available
                if hasattr(tool, 'parameters'):
                    tool_data["parameters"] = tool.parameters
                
                tools_by_server[server_id].append(tool_data)
            
            # Verify all tools were properly added to their servers
            for server_id, tools in tools_by_server.items():
                actual_count = len(tools)
                expected_count = server_tool_counts[server_id]
                logger.info(f"Server {server_id}: Added {actual_count}/{expected_count} tools")
                
                if actual_count != expected_count:
                    logger.warning(f"Tool count mismatch for server {server_id}: expected {expected_count}, got {actual_count}")
            
            logger.info(f"Organized tools by server: {', '.join([f'{s}:{len(t)}' for s, t in tools_by_server.items()])}")
            
            # Get server status
            servers_status = {}
            for server_id, server in mcp_client.servers.items():
                servers_status[server_id] = {
                    "connected": server.is_connected,
                    "url": server.server_url,
                    "tools_count": len([t for t in all_tools if t.server_id == server_id])
                }
                logger.info(f"Server {server_id}: connected={server.is_connected}, tools={servers_status[server_id]['tools_count']}")
            
            # Send status update
            response = {
                "type": "status_update",
                "status": "ok",
                "servers": servers_status,
                "tools_by_server": tools_by_server,
                "total_tools": len(all_tools)
            }
            
            # Log response size for debugging
            response_size = len(str(response))
            logger.info(f"Sending status update response (size: ~{response_size/1024:.1f}KB)")
            
            await websocket.send_json(response)
            logger.info("Status update response sent successfully")
            
        except Exception as e:
            logger.error(f"Error getting MCP status: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            await websocket.send_json({
                "type": "status_update",
                "status": "error",
                "message": str(e)
            })

    async def handle_get_mcp_config(self, websocket: WebSocket):
        """Handle a request to get the MCP server configuration"""
        try:
            # Read the mcp.json file
            config_file = Path("mcp.json")
            if not config_file.exists():
                config_file = Path("backend/mcp.json")
            
            if not config_file.exists():
                await websocket.send_json({
                    "type": "mcp_config_response",
                    "status": "error",
                    "message": "MCP configuration file not found"
                })
                return
                
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)
                
            await websocket.send_json({
                "type": "mcp_config_response",
                "status": "ok",
                "config": config
            })
            
        except Exception as e:
            logger.error(f"Error getting MCP config: {str(e)}")
            await websocket.send_json({
                "type": "mcp_config_response",
                "status": "error",
                "message": str(e)
            })
            
    async def handle_update_mcp_config(self, websocket: WebSocket, data: dict):
        """Handle a request to update the MCP server configuration"""
        try:
            new_config = data.get("config")
            if not new_config:
                await websocket.send_json({
                    "type": "mcp_config_update_response",
                    "status": "error",
                    "message": "No configuration provided"
                })
                return
                
            # Determine the config file path
            config_file = Path("mcp.json")
            if not config_file.exists():
                config_file = Path("backend/mcp.json")
                
            if not config_file.exists():
                await websocket.send_json({
                    "type": "mcp_config_update_response",
                    "status": "error",
                    "message": "MCP configuration file not found"
                })
                return
                
            # Create a backup of the original file
            backup_file = config_file.with_suffix(".json.bak")
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    original_config = f.read()
                with open(backup_file, "w", encoding="utf-8") as f:
                    f.write(original_config)
                    logger.info(f"Created backup of MCP config at {backup_file}")
            except Exception as e:
                logger.warning(f"Failed to create backup: {str(e)}")
                
            # Write the new configuration
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(new_config, f, indent=4, ensure_ascii=False)
                
            # Restart MCP clients for all active sessions
            restarted_sessions = []
            for session_id in list(self.mcp_clients.keys()):
                try:
                    old_client = self.mcp_clients[session_id]
                    await old_client.close()
                    
                    # Create new client with updated config
                    new_client = await MCPClient.create()
                    await new_client.initialize()
                    self.mcp_clients[session_id] = new_client
                    
                    # Update LLM integration
                    if session_id in self.llm_integrations:
                        old_integration = self.llm_integrations[session_id]
                        model = old_integration.model if hasattr(old_integration, 'model') else self.default_model
                        api_key = old_integration.api_key if hasattr(old_integration, 'api_key') else self.anthropic_api_key
                        
                        new_integration = new_client.create_llm_integration(api_key=api_key, model=model)
                        self.llm_integrations[session_id] = new_integration
                        
                    restarted_sessions.append(session_id)
                    logger.info(f"Restarted MCP client for session {session_id}")
                except Exception as e:
                    logger.error(f"Failed to restart MCP client for session {session_id}: {str(e)}")
            
            # Send success response
            await websocket.send_json({
                "type": "mcp_config_update_response",
                "status": "ok",
                "message": f"Configuration updated and {len(restarted_sessions)} sessions restarted",
                "restarted_sessions": restarted_sessions
            })
            
        except Exception as e:
            logger.error(f"Error updating MCP config: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            await websocket.send_json({
                "type": "mcp_config_update_response",
                "status": "error",
                "message": str(e)
            })

    def load_system_prompt(self):
        """Load system prompt from prompts.json file"""
        try:
            if self.prompts_file.exists():
                with open(self.prompts_file, "r", encoding="utf-8") as f:
                    prompts = json.load(f)
                    return prompts.get("system_prompt", "")
            return ""
        except Exception as e:
            logger.error(f"Error loading system prompt: {str(e)}")
            return ""
            
    def save_system_prompt(self, prompt):
        """Save system prompt to prompts.json file"""
        try:
            prompts = {"system_prompt": prompt}
            
            # Create backup
            if self.prompts_file.exists():
                backup_file = self.prompts_file.with_suffix(".json.bak")
                try:
                    with open(self.prompts_file, "r", encoding="utf-8") as f:
                        original_content = f.read()
                    with open(backup_file, "w", encoding="utf-8") as f:
                        f.write(original_content)
                except Exception as e:
                    logger.warning(f"Failed to create backup of prompts file: {str(e)}")
            
            # Save new content
            with open(self.prompts_file, "w", encoding="utf-8") as f:
                json.dump(prompts, f, indent=4, ensure_ascii=False)
                
            logger.info(f"Saved system prompt ({len(prompt)} chars)")
            return True
        except Exception as e:
            logger.error(f"Error saving system prompt: {str(e)}")
            return False 