from fastapi import WebSocket, WebSocketDisconnect
import asyncio
import logging
from typing import List, Dict, Any
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path
import io
from PIL import Image
import base64

# Set up logging
log_dir = Path(__file__).parent / "log"
log_dir.mkdir(exist_ok=True)
log_file = log_dir / "websocket.log"

# Configure the logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

# Create a TimedRotatingFileHandler
file_handler = TimedRotatingFileHandler(
    filename=log_file,
    when="midnight",
    interval=1,
    backupCount=7,
    encoding="utf-8",
)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# Store active WebSocket connections
active_connections: List[WebSocket] = []

class WebSocketHandler:
    def __init__(self, llm_clients: Dict[str, Any]):
        self.llm_clients = llm_clients
        self.active_tasks = set()
        self.is_connected = True
        self.response_buffers = {}
        self.converted_histories = {
            'openai': [],
            'anthropic': [],
            'openrouter': []
        }
        logger.info(f"WebSocketHandler initialized with clients: {list(llm_clients.keys())}")

    async def process_llm_stream(self, model_name: str, stream: Any, websocket: WebSocket):
        """Handle streaming responses asynchronously"""
        try:
            self.response_buffers[model_name] = ""
            
            if model_name == 'anthropic':
                for chunk in stream:
                    if not self.is_connected:
                        logger.warning(f"WebSocket disconnected while streaming {model_name}")
                        return

                    content = chunk.delta.text if chunk.type == 'content_block_delta' else None
                    if content:
                        self.response_buffers[model_name] += content
                        try:
                            await websocket.send_json({
                                "type": "stream",
                                "modelName": model_name,
                                "content": content
                            })
                            await asyncio.sleep(0.01) # Add a small delay to increase parallelism
                        except Exception as e:
                            logger.error(f"Error sending stream for {model_name}: {str(e)}")
                            self.is_connected = False
                            return

            else:  # openai or openrouter
                for chunk in stream:
                    if not self.is_connected:
                        logger.warning(f"WebSocket disconnected while streaming {model_name}")
                        return

                    if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        self.response_buffers[model_name] += content
                        try:
                            await websocket.send_json({
                                "type": "stream",
                                "modelName": model_name,
                                "content": content
                            })
                            await asyncio.sleep(0.01) # Add a small delay to increase parallelism
                        except Exception as e:
                            logger.error(f"Error sending stream for {model_name}: {str(e)}")
                            self.is_connected = False
                            return

            # Send completion message
            if self.is_connected:
                try:    
                    await websocket.send_json({
                        "type": "end",
                        "modelName": model_name,
                        "content": self.response_buffers[model_name]
                    })
                    logger.info(f"Completed streaming for {model_name}")
                    
                    # Add assistant's response to converted history
                    self.converted_histories[model_name].append({
                        "role": "assistant",
                        "content": self.response_buffers[model_name]
                    })
                except Exception as e:
                    logger.error(f"Error sending completion for {model_name}: {str(e)}")
                    self.is_connected = False
                    
                    
            # Clean up
            del self.response_buffers[model_name]

        except Exception as e:
            logger.error(f"Error streaming from {model_name}: {str(e)}")
            if self.is_connected:
                try:
                    await websocket.send_json({
                        "type": "error",
                        "modelName": model_name,
                        "error": f"Error streaming from {model_name}: {str(e)}"
                    })
                except Exception:
                    self.is_connected = False

    async def process_llm(self, model_name: str, client: Any, websocket: WebSocket, user_message: str, image_data: dict, models: Dict[str, str]):
        task_id = id(asyncio.current_task())
        self.active_tasks.add(task_id)
        logger.info(f"Starting process_llm for {model_name} (task {task_id})")
        
        try:
            if client is None:
                logger.error(f"Client for {model_name} is None")
                await websocket.send_json({
                    "type": "error",
                    "modelName": model_name,
                    "error": f"Error: {model_name} client is not initialized"
                })
                return

            max_retries = 3 if model_name == 'anthropic' else 1
            retry_count = 0
            retry_delay = 10

            while retry_count < max_retries:
                try:
                    if model_name == 'anthropic':
                        try:
                            # Use cached converted history if available
                            messages = self.converted_histories.get(model_name, []).copy()

                            # Prepare current message content
                            content = []
                            if user_message:
                                content.append({"type": "text", "text": user_message})
                            if image_data:
                                base64_url = image_data["image_url"]["url"]
                                header_parts = base64_url.split(',', 1)
                                if len(header_parts) == 2:
                                    header = header_parts[0]
                                    base64_data = header_parts[1]
                                    media_type = header.split(':')[1].split(';')[0]
                                    
                                    # Convert JPEG to PNG if necessary
                                    if media_type.lower() in ['image/jpeg', 'image/jpg']:
                                        image_bytes = base64.b64decode(base64_data)
                                        image = Image.open(io.BytesIO(image_bytes))
                                        png_buffer = io.BytesIO()
                                        image.save(png_buffer, format='PNG')
                                        base64_data = base64.b64encode(png_buffer.getvalue()).decode('utf-8')
                                        media_type = 'image/png'
                                    
                                    content.append({
                                        "type": "image",
                                        "source": {
                                            "type": "base64",
                                            "media_type": media_type,
                                            "data": base64_data
                                        }
                                    })

                            # Add current message and save to history
                            current_message = {
                                "role": "user",
                                "content": content
                            }
                            messages.append(current_message)
                            self.converted_histories[model_name].append(current_message)

                            stream = client.messages.create(
                                model=models.get('anthropic', "claude-3-5-sonnet-20241022"),
                                max_tokens=8192,
                                messages=messages,
                                stream=True
                            )
                        except Exception as e:
                            logger.error(f"Error processing image data: {str(e)}")
                            await websocket.send_json({
                                "type": "error",
                                "modelName": model_name,
                                "error": f"Error: Invalid image format - {str(e)}"
                            })
                            return
                    
                    elif model_name in ['openai', 'openrouter']:
                        # Use cached converted history for OpenAI/OpenRouter
                        messages = [{"role": "system", "content": "You are a helpful assistant."}]
                        messages.extend(self.converted_histories.get(model_name, []))
                        
                        content = []
                        if user_message:
                            content.append({"type": "text", "text": user_message})
                        if image_data:
                            content.append(image_data)
                        
                        current_message = {"role": "user", "content": content}
                        messages.append(current_message)
                        self.converted_histories[model_name].append(current_message)
                        
                        stream = client.chat.completions.create(
                            model=models.get(model_name),
                            messages=messages,
                            max_tokens=8192,
                            stream=True
                        )
                    
                    await self.process_llm_stream(model_name, stream, websocket)
                    break
                    
                except Exception as e:
                    error_dict = getattr(e, '__dict__', {})
                    if (model_name == 'anthropic' and 
                        isinstance(error_dict.get('error', {}), dict) and 
                        error_dict['error'].get('type') == 'overloaded_error'):
                        
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.warning(f"Claude overloaded, attempt {retry_count}/{max_retries}. Retrying in {retry_delay} seconds...")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 2
                            continue
                    
                    logger.error(f"Error with {model_name}: {str(e)}")
                    if self.is_connected:
                        try:
                            await websocket.send_json({
                                "type": "error",
                                "modelName": model_name,
                                "error": f"Error: Unable to get response from {model_name}"
                            })
                        except Exception:
                            pass
                    break

        finally:
            self.active_tasks.remove(task_id)
            logger.info(f"Completed process_llm for {model_name} (task {task_id})")

    async def handle_websocket(self, websocket: WebSocket):
        logger.info("New WebSocket connection")
        await websocket.accept()
        active_connections.append(websocket)
        self.is_connected = True
        
        try:
            while True:
                try:
                    if not self.is_connected:
                        break

                    data = await websocket.receive_json()
                    user_message = data.get('message', '')
                    models = data.get('models', {})
                    image_data = data.get('imageData')
                    
                    logger.info(f"Received message: {user_message}")
                    
                    tasks = []
                    for model_name, client in self.llm_clients.items():
                        if client is not None:
                            task = asyncio.create_task(
                                self.process_llm(
                                    model_name=model_name,
                                    client=client,
                                    websocket=websocket,
                                    user_message=user_message,
                                    image_data=image_data,
                                    models=models
                                )
                            )
                            tasks.append(task)
                            logger.info(f"Created task for {model_name}")
                    
                    if tasks:
                        logger.info(f"Starting {len(tasks)} parallel tasks")
                        await asyncio.gather(*tasks, return_exceptions=True)
                        logger.info("All tasks completed")
                    else:
                        logger.warning("No tasks created - no active LLM clients")

                except WebSocketDisconnect:
                    logger.info("WebSocket disconnected")
                    self.is_connected = False
                    break
                except Exception as e:
                    logger.error(f"Error processing message: {str(e)}")
                    self.is_connected = False
                    break

        finally:
            if websocket in active_connections:
                active_connections.remove(websocket)
            logger.info("WebSocket connection closed") 