{
    "configurations": [       
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "Attach to Edge",
            "port": 9222,
            "request": "attach",
            "type": "msedge",
            "webRoot": "${workspaceFolder}"
        },
        {
            "name": "Next.js Debug Ray",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/frontend/node_modules/next/dist/bin/next",
            "args": [
                "dev",
                "-p",
                "8505"
            ],
            "cwd": "${workspaceFolder}/frontend",
            "console": "integratedTerminal",
            "env": {
                "NODE_OPTIONS": "--inspect"
            },
            "resolveSourceMapLocations": [
                "${workspaceFolder}/**",
                "!**/node_modules/**"
            ]
        },
        {
            "type": "node-terminal",
            "name": "<PERSON> Script: start",
            "request": "launch",
            "command": "npm run start",
            "cwd": "${workspaceFolder}/frontend"
        },
        {
            "name": "Next.js: Debug Full Stack",
            "type": "node-terminal",
            "request": "launch",
            "command": "npm run dev",
            "cwd": "${workspaceFolder}/frontend",
            "serverReadyAction": {
                "pattern": "- Local:.+(https?://.+)",
                "uriFormat": "%s",
                "action": "debugWithChrome"
            }
        },
        {
            "name": "Next.js: Debug Server-Side",
            "type": "node-terminal",
            "request": "launch",
            "command": "npm run dev",
            "cwd": "${workspaceFolder}/frontend"
        },
        {
            "name": "Next.js: Debug Client-Side",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:8505",
            "webRoot": "${workspaceFolder}/frontend",
            "sourceMaps": true,
            "sourceMapPathOverrides": {
                "webpack://_N_E/*": "${webRoot}/*"
            }
        },
    ]
}