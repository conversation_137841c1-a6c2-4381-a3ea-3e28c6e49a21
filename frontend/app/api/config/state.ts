export interface CachedConfig {
  backendUrl: string;
  llmModels: Record<string, string>;
  localLLMModels: string;
  chatApiUrl: string;
  apiKey: string;
}

// Module state
export const state = {
  cachedConfig: null as CachedConfig | null,
  currentBackendUrl: null as string | null,
  lastRead: 0,
  CACHE_DURATION: 5000  // Cache invalidation time (5 seconds)
};

// State management functions
export const resetState = () => {
  state.cachedConfig = null;
  state.currentBackendUrl = null;
  state.lastRead = 0;
}; 