import { NextRequest } from 'next/server';
import * as fs from 'fs';
import { state, resetState } from '../state';

// Mock fs module
jest.mock('fs', () => ({
  existsSync: jest.fn(),
  readFileSync: jest.fn(),
}));

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch as jest.Mock;

// Fixed timestamp for tests
const FIXED_TIMESTAMP = 1733294360167;

// Import after mocks
import { GET } from '../route';

// Expected fetch options for health checks
const expectedFetchOptions = {
  method: 'GET',
  cache: 'no-store',
  headers: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  },
  signal: expect.any(Object)
};

describe('Config Route Tests', () => {
  beforeAll(() => {
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  beforeEach(() => {
    // Reset all mocks and state
    jest.clearAllMocks();
    mockFetch.mockReset();
    resetState();
    
    // Setup Date.now() mock
    jest.spyOn(Date, 'now').mockImplementation(() => FIXED_TIMESTAMP);
    
    // Reset environment variables
    process.env = {
      NODE_ENV: 'test',
      NEXT_PUBLIC_PRIMARY_BACKEND_URL: 'http://primary:3201',
      NEXT_PUBLIC_SECONDARY_BACKEND_URL: 'http://secondary:3201',
      NEXT_PUBLIC_BACKEND_API_URL: 'http://legacy:3201',
      NEXT_PUBLIC_LLM_MODELS: '{"test": "model"}',
      DEBUG_CONFIG: 'true'
    } as NodeJS.ProcessEnv;

    // Mock .env file exists and content
    (fs.existsSync as jest.Mock).mockReturnValue(true);
    (fs.readFileSync as jest.Mock).mockReturnValue(`
      NEXT_PUBLIC_PRIMARY_BACKEND_URL=http://primary:3201
      NEXT_PUBLIC_SECONDARY_BACKEND_URL=http://secondary:3201
      NEXT_PUBLIC_BACKEND_API_URL=http://legacy:3201
    `);
  });

  afterEach(() => {
    // Clear any pending timers
    jest.clearAllTimers();
  });

  it('should try primary URL first', async () => {
    mockFetch.mockResolvedValueOnce({ ok: true });

    const response = await GET(new NextRequest(new Request('http://localhost')));
    const data = await response.json();

    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockFetch).toHaveBeenCalledWith(
      `http://primary:3201/health?t=${FIXED_TIMESTAMP}`,
      expectedFetchOptions
    );
    expect(data.backendUrl).toBe('http://primary:3201');
  });

  it('should failover to secondary URL when primary fails', async () => {
    mockFetch
      .mockRejectedValueOnce(new Error('Primary failed'))
      .mockResolvedValueOnce({ ok: true });

    const response = await GET(new NextRequest(new Request('http://localhost')));
    const data = await response.json();

    expect(mockFetch).toHaveBeenCalledTimes(2);
    expect(mockFetch).toHaveBeenNthCalledWith(
      1,
      `http://primary:3201/health?t=${FIXED_TIMESTAMP}`,
      expectedFetchOptions
    );
    expect(mockFetch).toHaveBeenNthCalledWith(
      2,
      `http://secondary:3201/health?t=${FIXED_TIMESTAMP}`,
      expectedFetchOptions
    );
    expect(data.backendUrl).toBe('http://secondary:3201');
  });

  it('should failover to legacy URL when both primary and secondary fail', async () => {
    mockFetch
      .mockRejectedValueOnce(new Error('Primary failed'))
      .mockRejectedValueOnce(new Error('Secondary failed'));

    const response = await GET(new NextRequest(new Request('http://localhost')));
    const data = await response.json();

    expect(mockFetch).toHaveBeenCalledTimes(2);
    expect(data.backendUrl).toBe('http://legacy:3201');
  });

  it('should handle timeout correctly', async () => {
    mockFetch
      .mockImplementationOnce(() => 
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Timeout')), 3100);
        })
      )
      .mockResolvedValueOnce({ ok: true });

    const responsePromise = GET(new NextRequest(new Request('http://localhost')));
    jest.runAllTimers();
    const response = await responsePromise;
    const data = await response.json();

    expect(mockFetch).toHaveBeenCalledTimes(2);
    expect(data.backendUrl).toBe('http://secondary:3201');
  });

  it('should cache the working backend URL', async () => {
    // Override CACHE_DURATION for this test
    const originalCacheDuration = state.CACHE_DURATION;
    state.CACHE_DURATION = 10000; // 10 seconds

    try {
      // First call - primary fails, secondary succeeds
      mockFetch
        .mockRejectedValueOnce(new Error('Primary failed'))
        .mockResolvedValueOnce({ ok: true });

      // First call
      let response = await GET(new NextRequest(new Request('http://localhost')));
      let data = await response.json();
      expect(data.backendUrl).toBe('http://secondary:3201');

      // Second call should use cached URL and not check health
      response = await GET(new NextRequest(new Request('http://localhost')));
      data = await response.json();
      
      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(data.backendUrl).toBe('http://secondary:3201');
    } finally {
      // Restore original cache duration
      state.CACHE_DURATION = originalCacheDuration;
    }
  });

  it('should use secondary URL for external IP', async () => {
    mockFetch.mockResolvedValueOnce({ ok: true });

    const request = new NextRequest(new Request('http://localhost'), {
      headers: {
        'x-forwarded-for': '*******'  // External IP
      }
    });
    const response = await GET(request);
    const data = await response.json();

    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockFetch).toHaveBeenCalledWith(
      `http://secondary:3201/health?t=${FIXED_TIMESTAMP}`,
      expectedFetchOptions
    );
    expect(data.backendUrl).toBe('http://secondary:3201');
  });
}); 