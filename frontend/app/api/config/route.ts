import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { state, resetState } from './state';

// Mark this route as dynamic
export const dynamic = 'force-dynamic';

// Add type definitions at the top
interface EnvConfig {
  NEXT_PUBLIC_PRIMARY_BACKEND_URL?: string;
  NEXT_PUBLIC_SECONDARY_BACKEND_URL?: string;
  NEXT_PUBLIC_BACKEND_API_URL?: string;
  NEXT_PUBLIC_LLM_MODELS?: string;
  NEXT_LOCAL_LLM_MODELS?: string;
  NEXT_PUBLIC_CHAT_API_URL?: string;
  NEXT_PUBLIC_API_KEY?: string;
  [key: string]: string | undefined;  // Add index signature for string keys
}

// Debug logging helper
const debug = (message: string, data?: any) => {
  if (process.env.DEBUG_CONFIG === 'true') {
    if (data) {
      console.log(`[CONFIG] ${message}`, data);
    } else {
      console.log(`[CONFIG] ${message}`);
    }
  }
};

// Helper function to check if an IP is internal
function isInternalIP(ip: string): boolean {
  try {
    // Remove port if present
    ip = ip.split(':')[0];
    
    // Handle IPv6-mapped IPv4 addresses (::ffff:************)
    if (ip.startsWith('::ffff:')) {
      ip = ip.substring(7);
    }
    
    debug('Checking IP:', ip);
    
    // Check localhost
    if (ip === 'localhost' || ip === '127.0.0.1') {
      debug('IP is localhost');
      return true;
    }
    
    // Check private IP ranges
    const parts = ip.split('.');
    if (parts.length !== 4) {
      debug('Invalid IP format');
      return false;
    }
    
    const firstOctet = parseInt(parts[0]);
    const secondOctet = parseInt(parts[1]);
    
    debug('IP octets:', { first: firstOctet, second: secondOctet });
    
    // Only treat ***********/16 as internal network
    if (firstOctet === 192 && secondOctet === 168) {
      debug('IP is in ***********/16 range (internal)');
      return true;
    }
    
    debug('IP is external');
    return false;
  } catch (error) {
    console.error('[API] Error checking IP:', error);
    return false;
  }
}

async function checkBackendAvailability(url: string): Promise<boolean> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 3000);
  
  try {
    const response = await fetch(`${url}/health?t=${Date.now()}`, {
      signal: controller.signal,
      method: 'GET',
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
    
    return response.ok;
  } catch (error) {
    debug(`Backend ${url} is not available:`, error);
    return false;
  } finally {
    clearTimeout(timeoutId);
  }
}

async function determineBackendUrl(envConfig: EnvConfig, clientIP?: string): Promise<string> {
  const primaryUrl = envConfig.NEXT_PUBLIC_PRIMARY_BACKEND_URL;
  const secondaryUrl = envConfig.NEXT_PUBLIC_SECONDARY_BACKEND_URL;
  const fallbackUrl = 'http://localhost:3201';

  debug('Determining backend URL:', {
    primaryUrl,
    secondaryUrl,
    clientIP,
    isInternal: clientIP ? isInternalIP(clientIP) : 'no-ip'
  });

  // If client IP is provided and it's external, try secondary URL first
  if (clientIP && !isInternalIP(clientIP) && secondaryUrl && await checkBackendAvailability(secondaryUrl)) {
    state.currentBackendUrl = secondaryUrl;
    debug('Using secondary URL for external client:', { clientIP, url: secondaryUrl });
    return secondaryUrl;
  }

  // Otherwise follow normal order: primary -> secondary -> fallback
  if (primaryUrl && await checkBackendAvailability(primaryUrl)) {
    state.currentBackendUrl = primaryUrl;
    debug('Using primary URL:', primaryUrl);
    return primaryUrl;
  }

  if (secondaryUrl && await checkBackendAvailability(secondaryUrl)) {
    state.currentBackendUrl = secondaryUrl;
    debug('Primary not available, using secondary URL:', secondaryUrl);
    return secondaryUrl;
  }

  // Fallback to legacy URL if specified
  if (envConfig.NEXT_PUBLIC_BACKEND_API_URL) {
    state.currentBackendUrl = envConfig.NEXT_PUBLIC_BACKEND_API_URL;
    debug('Using legacy URL:', envConfig.NEXT_PUBLIC_BACKEND_API_URL);
    return envConfig.NEXT_PUBLIC_BACKEND_API_URL;
  }

  // Last resort fallback
  state.currentBackendUrl = fallbackUrl;
  debug('Using fallback URL:', fallbackUrl);
  return fallbackUrl;
}

// Helper function to reload environment variables
function reloadEnv(envPath: string): EnvConfig | null {
  try {
    const fileContent = fs.readFileSync(envPath, 'utf-8');
    const envConfig = dotenv.parse(fileContent) as EnvConfig;
    // Update process.env
    for (const k in envConfig) {
      process.env[k] = envConfig[k];
    }
    return envConfig;
  } catch (err) {
    console.error('[CONFIG] Error reloading env:', err);
    return null;
  }
}

export async function GET(request: Request) {
  const headers = {
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
  };

  try {
    const now = Date.now();
    debug(`Request received at ${new Date().toISOString()}`);
    
    // Get client IP from request headers and clean it
    let clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
                   request.headers.get('x-real-ip') || 
                   request.headers.get('x-client-ip') || 
                   '127.0.0.1';
                   
    // Clean IPv6-mapped IPv4 addresses
    if (clientIP.startsWith('::ffff:')) {
      clientIP = clientIP.substring(7);
    }
    
    debug('Request headers:', {
      'x-forwarded-for': request.headers.get('x-forwarded-for'),
      'x-real-ip': request.headers.get('x-real-ip'),
      'x-client-ip': request.headers.get('x-client-ip'),
      'resolved-ip': clientIP
    });
    
    // Check if we need to refresh the cache
    if (!state.cachedConfig || (now - state.lastRead) > state.CACHE_DURATION) {
      const rootDir = process.cwd();
      const envPath = path.join(rootDir, '.env');
      
      let envConfig;
      if (fs.existsSync(envPath)) {
        envConfig = reloadEnv(envPath);
        if (!envConfig) {
          throw new Error('Failed to reload env config');
        }
        state.lastRead = now;
      } else {
        envConfig = {
          NEXT_PUBLIC_PRIMARY_BACKEND_URL: process.env.NEXT_PUBLIC_PRIMARY_BACKEND_URL,
          NEXT_PUBLIC_SECONDARY_BACKEND_URL: process.env.NEXT_PUBLIC_SECONDARY_BACKEND_URL,
          NEXT_PUBLIC_BACKEND_API_URL: process.env.NEXT_PUBLIC_BACKEND_API_URL,
          NEXT_PUBLIC_LLM_MODELS: process.env.NEXT_PUBLIC_LLM_MODELS,
          NEXT_LOCAL_LLM_MODELS: process.env.NEXT_LOCAL_LLM_MODELS,
          NEXT_PUBLIC_CHAT_API_URL: process.env.NEXT_PUBLIC_CHAT_API_URL,
          NEXT_PUBLIC_API_KEY: process.env.NEXT_PUBLIC_API_KEY
        };
      }

      // Determine the best backend URL based on client IP
      const backendUrl = await determineBackendUrl(envConfig, clientIP);

      // Update cache with new values
      state.cachedConfig = {
        backendUrl,
        llmModels: JSON.parse(envConfig.NEXT_PUBLIC_LLM_MODELS || '{}'),
        localLLMModels: envConfig.NEXT_LOCAL_LLM_MODELS || '{}',
        chatApiUrl: envConfig.NEXT_PUBLIC_CHAT_API_URL || '',
        apiKey: envConfig.NEXT_PUBLIC_API_KEY || ''
      };

      debug('New config:', {
        backendUrl: state.cachedConfig.backendUrl,
        modelsAvailable: Object.keys(state.cachedConfig.llmModels),
        localLLMConfig: state.cachedConfig.localLLMModels,
        chatApiUrl: state.cachedConfig.chatApiUrl,
        apiKey: '***' + state.cachedConfig.apiKey.slice(-4),
        clientIP
      });
    }

    return new NextResponse(JSON.stringify(state.cachedConfig), { 
      status: 200,
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('[CONFIG] Critical error:', error);
    
    // Fallback config
    const fallbackConfig = {
      backendUrl: state.currentBackendUrl || process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3201',
      llmModels: JSON.parse(process.env.NEXT_PUBLIC_LLM_MODELS || '{}')
    };

    return new NextResponse(JSON.stringify(fallbackConfig), { 
      status: 200,
      headers: {
        ...headers,
        'Content-Type': 'application/json'
      }
    });
  }
} 