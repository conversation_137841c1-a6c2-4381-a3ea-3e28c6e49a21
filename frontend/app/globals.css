@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import 'katex/dist/katex.min.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  font-family: 'Inter', sans-serif;
  @apply bg-gray-50 text-gray-900;
}

h1, h2, h3, h4, h5, h6 {
  @apply font-bold;
}

.gradient-text {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600;
}

.input-focus {
  @apply focus:ring-2 focus:ring-blue-300 focus:border-blue-300;
}

.button-hover {
  @apply transition duration-300 ease-in-out transform hover:-translate-y-1 hover:scale-105;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.enlarge-textarea {
  transition: transform 0.3s ease-in-out;
}

.enlarge-textarea:hover {
  transform: scale(1.1);
}

.react-resizable {
  position: relative;
}

.react-resizable-handle {
  position: absolute;
  width: 100%;
  height: 10px;
  bottom: 0;
  cursor: ns-resize;
}

.custom-handle {
  width: 100%;
  height: 10px;
  background: rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 0;
  cursor: ns-resize;
  display: flex;
  justify-content: center;
  align-items: center;
}

.custom-handle::after {
  content: '';
  width: 40px;
  height: 3px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

/* LaTeX 样式 */
.math-content {
  overflow-x: auto;
}

.math-display {
  overflow-x: auto;
  padding: 1em 0;
  text-align: center;
}

.math-inline {
  padding: 0 0.2em;
}

/* 确保矩阵正确显示 */
.MathJax_Display {
  overflow-x: auto;
  overflow-y: hidden;
}

/* Custom accordion classes */
.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

/* Add styles for thinking bubble and tool results */
@keyframes thinkingPulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.thinking-pulse {
  animation: thinkingPulse 1.5s infinite ease-in-out;
}

.tool-result-container, .tool-error-container {
  margin-top: 0.5rem;
  border-radius: 0.375rem;
  overflow: hidden;
}

.tool-result-container {
  border: 1px solid rgba(0, 200, 83, 0.2);
}

.tool-error-container {
  border: 1px solid rgba(255, 59, 48, 0.2);
}

.tool-result-header, .tool-error-header {
  padding: 0.5rem 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.tool-result-header {
  background-color: rgba(0, 200, 83, 0.1);
}

.tool-error-header {
  background-color: rgba(255, 59, 48, 0.1);
}

.tool-result-title {
  font-weight: 500;
  color: rgb(0, 150, 62);
}

.tool-error-title {
  font-weight: 500;
  color: rgb(200, 30, 30);
}

.tool-result-content {
  padding: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  font-family: monospace;
  font-size: 0.875rem;
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 16rem;
  overflow-y: auto;
}

.toggle-icon {
  transition: transform 0.2s ease;
}

.toggle-icon.open {
  transform: rotate(180deg);
}

/* Styles for tool use bubbles */
.chat-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.compact-tool-bubble {
  padding: 0.75rem;
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
}

.tool-name {
  font-weight: 600;
  color: rgb(37, 99, 235);
  margin-bottom: 0.25rem;
}

.tool-content {
  font-family: monospace;
  font-size: 0.75rem;
  overflow-x: auto;
  white-space: pre;
  padding: 0.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 0.25rem;
}
