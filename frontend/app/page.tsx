'use client'

import { useState, useEffect } from 'react'
import { AiProductPortal } from '@/components/ai-product-portal'
import ChatInterface from '@/components/ChatInterface'
import FileUpload from '@/components/FileUpload'
import ColpaliInterface from '@/components/ColpaliInterface'
import { Progress } from '@/components/ui/progress'
import { fetchEnvConfig } from '@/lib/api'
import { useTheme } from "next-themes"
import { Waves } from "@/components/ui/waves-background"
import { ChevronUp, ChevronDown } from 'lucide-react'
import HelpChatbot from '@/components/HelpChatbot'
import MCPChat from '@/components/MCPChat'

export default function Home() {
  const { theme } = useTheme()
  const [filesUploaded, setFilesUploaded] = useState(false)
  const [searchResults, setSearchResults] = useState('')
  const [contextualEmbedding, setContextualEmbedding] = useState(true)
  const [selectedLLM, setSelectedLLM] = useState('claude')
  const [selectedSearchEngine, setSelectedSearchEngine] = useState('bing')
  const [topK, setTopK] = useState(20)
  const [relevanceThreshold, setRelevanceThreshold] = useState(3)
  const [fullDocsSearch, setFullDocsSearch] = useState(true)
  const [rerankMethod, setRerankMethod] = useState('jina')
  const [indexes, setIndexes] = useState<string[]>([])
  const [selectedIndex, setSelectedIndex] = useState('')
  const [query, setQuery] = useState('')
  const [result, setResult] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('upload')
  const [uploadProgress, setUploadProgress] = useState(0)
  const [processingProgress, setProcessingProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)
  const [backendUrl, setBackendUrl] = useState<string>('http://localhost:3201');

  useEffect(() => {
    const loadConfig = async () => {
      const config = await fetchEnvConfig();
      setBackendUrl(config.backendUrl);
    };
    loadConfig();
  }, []);

  useEffect(() => {
    let eventSource: EventSource | null = null;

    const setupEventSource = async () => {
      if (isUploading && uploadProgress >= 50) {
        const config = await fetchEnvConfig();
        eventSource = new EventSource(`${config.backendUrl}/progress`);
        
        eventSource.onmessage = (event) => {
          const progress = parseInt(event.data, 10);
          setProcessingProgress(progress);
        };

        eventSource.addEventListener('complete', () => {
          setProcessingProgress(100);
          setIsUploading(false);
          eventSource?.close();
        });

        eventSource.onerror = () => {
          eventSource?.close();
          setIsUploading(false);
        };
      }
    };

    setupEventSource();

    return () => {
      eventSource?.close();
    };
  }, [isUploading, uploadProgress]);

  const calculateTotalProgress = () => {
    if (uploadProgress < 50) {
      return uploadProgress;
    } else {
      return 50 + (processingProgress / 2);
    }
  };

  const handleUploadComplete = async (fileExisted: boolean) => {
    setFilesUploaded(true);
    if (fileExisted) {
      setIsUploading(false);
      setUploadProgress(100);
      setProcessingProgress(100);
    }
    
    const config = await fetchEnvConfig();
    setBackendUrl(config.backendUrl);
  };

  const handleUploadStart = () => {
    setIsUploading(true);
    setUploadProgress(0);
    setProcessingProgress(0);
  };

  const handleUploadProgressChange = (progress: number) => {
    setUploadProgress(progress);
  };

  const handleScrollUp = () => {
    // Dispatch custom scroll event for ResultDisplay
    const customScrollEvent = new CustomEvent('customScroll', {
      detail: { scrollAmount: -300 }
    });
    window.dispatchEvent(customScrollEvent);

    // Default scroll behavior for main window
    window.scrollBy({
      top: -300,
      behavior: 'smooth'
    });
  };

  const handleScrollDown = () => {
    // Dispatch custom scroll event for ResultDisplay
    const customScrollEvent = new CustomEvent('customScroll', {
      detail: { scrollAmount: 300 }
    });
    window.dispatchEvent(customScrollEvent);

    // Default scroll behavior for main window
    window.scrollBy({
      top: 300,
      behavior: 'smooth'
    });
  };

  return (
    <main className="min-h-screen relative">
      <HelpChatbot />
      {/* Floating scroll buttons - only visible on mobile */}
      <div className="lg:hidden fixed right-4 top-1/2 -translate-y-1/2 z-[10000] flex flex-col gap-2">
        <button
          onClick={handleScrollUp}
          className="p-2 rounded-full bg-cyan-500/80 text-white backdrop-blur-sm hover:bg-cyan-600/80 transition-colors shadow-lg"
          aria-label="Scroll up"
        >
          <ChevronUp className="h-6 w-6" />
        </button>
        <button
          onClick={handleScrollDown}
          className="p-2 rounded-full bg-cyan-500/80 text-white backdrop-blur-sm hover:bg-cyan-600/80 transition-colors shadow-lg"
          aria-label="Scroll down"
        >
          <ChevronDown className="h-6 w-6" />
        </button>
      </div>
      
      <div className="absolute inset-0">
        <Waves
          lineColor={theme === "dark" ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.3)"}
          backgroundColor="transparent"
          waveSpeedX={0.02}
          waveSpeedY={0.01}
          waveAmpX={40}
          waveAmpY={20}
          friction={0.9}
          tension={0.01}
          maxCursorMove={120}
          xGap={12}
          yGap={36}
        />
      </div>
      <div className="relative z-10">
        <AiProductPortal>
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-center mb-4 text-white">Knowledge Base Search</h2>
            <div className="w-full">
              <div className="flex w-full rounded-t-lg overflow-hidden">
                <div className="flex-1">
                  <button 
                    onClick={() => setActiveTab('upload')} 
                    className={`w-full py-2 px-4 transition-colors ${
                      activeTab === 'upload' 
                        ? 'bg-blue-400/90 text-white' 
                        : 'bg-blue-300/70 text-white hover:bg-blue-400/80'
                    }`}
                  >
                    Upload
                  </button>
                </div>
                <div className="flex-1">
                  <button 
                    onClick={() => setActiveTab('search')} 
                    className={`w-full py-2 px-4 transition-colors ${
                      activeTab === 'search' 
                        ? 'bg-blue-400/90 text-white' 
                        : 'bg-blue-300/70 text-white hover:bg-blue-400/80'
                    }`}
                  >
                    Search
                  </button>
                </div>
                <div className="flex-1">
                  <button 
                    onClick={() => setActiveTab('pdf')} 
                    className={`w-full py-2 px-4 transition-colors ${
                      activeTab === 'pdf' 
                        ? 'bg-blue-400/90 text-white' 
                        : 'bg-blue-300/70 text-white hover:bg-blue-400/80'
                    }`}
                  >
                    PDF Scanner
                  </button>
                </div>
              </div>
              <div className="mt-4">
                {(isUploading || uploadProgress > 0 || processingProgress > 0) && (
                  <div className="mb-4 space-y-2 bg-white/90 p-4 rounded-lg">
                    <Progress 
                      value={calculateTotalProgress()} 
                      className="bg-gray-200"
                    />
                    <p className="text-sm text-gray-700">
                      {uploadProgress < 50 ? 'Uploading: ' : 'Processing: '}
                      {calculateTotalProgress().toFixed(0)}%
                    </p>
                    {uploadProgress < 50 && (
                      <p className="text-xs text-gray-600">
                        Uploading file... ({uploadProgress.toFixed(0)}%)
                      </p>
                    )}
                    {uploadProgress >= 50 && processingProgress < 100 && (
                      <p className="text-xs text-gray-600">
                        Processing file... ({processingProgress}%)
                      </p>
                    )}
                    {uploadProgress >= 50 && processingProgress >= 100 && (
                      <p className="text-sm text-green-600 font-medium">Processing complete!</p>
                    )}
                  </div>
                )}

                {activeTab === 'upload' && (
                  <FileUpload
                    onUploadComplete={handleUploadComplete}
                    onUploadStart={handleUploadStart}
                    onUploadProgressChange={handleUploadProgressChange}
                    isUploading={isUploading}
                    contextualEmbedding={contextualEmbedding}
                    onContextualEmbeddingChange={setContextualEmbedding}
                  />
                )}
                {activeTab === 'search' && (
                  <ChatInterface
                    filesUploaded={filesUploaded}
                    contextualEmbedding={contextualEmbedding}
                    onContextualEmbeddingChange={setContextualEmbedding}
                    searchResults={searchResults}
                    onSearchResults={setSearchResults}
                    selectedLLM={selectedLLM}
                    onLLMChange={setSelectedLLM}
                    selectedSearchEngine={selectedSearchEngine}
                    onSearchEngineChange={setSelectedSearchEngine}
                    topK={topK}
                    onTopKChange={setTopK}
                    relevanceThreshold={relevanceThreshold}
                    onRelevanceThresholdChange={setRelevanceThreshold}
                    fullDocsSearch={fullDocsSearch}
                    onFullDocsSearchChange={setFullDocsSearch}
                    rerankMethod={rerankMethod}
                    onRerankMethodChange={setRerankMethod}
                  />
                )}
                {activeTab === 'pdf' && (
                  <ColpaliInterface
                    indexes={indexes}
                    setIndexes={setIndexes}
                    selectedIndex={selectedIndex}
                    onSelectedIndexChange={setSelectedIndex}
                    query={query}
                    onQueryChange={setQuery}
                    result={result}
                    onResultChange={setResult}
                  />
                )}
              </div>
            </div>
          </div>
        </AiProductPortal>
      </div>
    </main>
  )
}
