"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface DialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: React.ReactNode
}

const Dialog = ({ open, onOpenChange, children }: DialogProps) => {
  if (!open) return null

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && onOpenChange) {
        onOpenChange(false)
      }
    }

    document.addEventListener("keydown", handleEscape)
    
    return () => {
      document.removeEventListener("keydown", handleEscape)
    }
  }, [onOpenChange])

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div 
        className="fixed inset-0 bg-black/80" 
        onClick={() => onOpenChange && onOpenChange(false)}
      />
      <div 
        className="relative z-50 bg-background/100 border-2 border-gray-200 rounded-lg shadow-xl max-w-lg w-full mx-4 overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>
  )
}

const DialogContent = ({ 
  className,
  children,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div 
      className={cn("p-6 max-h-[90vh] overflow-auto bg-white", className)} 
      {...props}
    >
      {children}
    </div>
  )
}

const DialogHeader = ({ 
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div 
      className={cn("flex flex-col space-y-1.5 text-center sm:text-left mb-4", className)} 
      {...props}
    />
  )
}

const DialogTitle = ({ 
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) => {
  return (
    <h3 
      className={cn("text-xl font-bold leading-none tracking-tight text-gray-900", className)} 
      {...props}
    />
  )
}

const DialogDescription = ({ 
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) => {
  return (
    <p 
      className={cn("text-sm text-muted-foreground", className)} 
      {...props}
    />
  )
}

export { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } 