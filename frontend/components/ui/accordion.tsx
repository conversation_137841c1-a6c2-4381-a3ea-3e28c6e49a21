import * as React from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface AccordionProps {
  type?: "single" | "multiple"
  collapsible?: boolean
  defaultValue?: string
  className?: string
  children?: React.ReactNode
}

const AccordionContext = React.createContext<{
  value: string | null
  setValue: (value: string | null) => void
}>({
  value: null,
  setValue: () => {}
})

const Accordion = React.forwardRef<
  HTMLDivElement,
  AccordionProps
>(({ className, type = "single", collapsible = false, defaultValue, children, ...props }, ref) => {
  const [value, setValue] = React.useState<string | null>(defaultValue || null)

  return (
    <AccordionContext.Provider value={{ value, setValue }}>
      <div
        ref={ref}
        className={cn("space-y-1", className)}
        {...props}
      >
        {children}
      </div>
    </AccordionContext.Provider>
  )
})
Accordion.displayName = "Accordion"

interface AccordionItemProps {
  value: string
  className?: string
  children?: React.ReactNode
}

const AccordionItem = React.forwardRef<
  HTMLDivElement,
  AccordionItemProps
>(({ className, value, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("border-b", className)}
      {...props}
    >
      {children}
    </div>
  )
})
AccordionItem.displayName = "AccordionItem"

interface AccordionTriggerProps {
  className?: string
  children?: React.ReactNode
}

const AccordionTrigger = React.forwardRef<
  HTMLButtonElement,
  AccordionTriggerProps
>(({ className, children, ...props }, ref) => {
  const { value, setValue } = React.useContext(AccordionContext)
  const itemValue = React.useContext(AccordionItemContext)
  
  const isOpen = value === itemValue

  const handleClick = () => {
    setValue(isOpen ? null : itemValue)
  }

  return (
    <button
      ref={ref}
      type="button"
      onClick={handleClick}
      className={cn(
        "flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline",
        className
      )}
      {...props}
    >
      {children}
      <ChevronDown 
        className={cn(
          "h-4 w-4 shrink-0 transition-transform duration-200",
          isOpen && "rotate-180"
        )} 
      />
    </button>
  )
})
AccordionTrigger.displayName = "AccordionTrigger"

interface AccordionContentProps {
  className?: string
  children?: React.ReactNode
}

const AccordionItemContext = React.createContext<string>("")

const AccordionContent = React.forwardRef<
  HTMLDivElement,
  AccordionContentProps
>(({ className, children, ...props }, ref) => {
  const { value } = React.useContext(AccordionContext)
  const itemValue = React.useContext(AccordionItemContext)
  
  const isOpen = value === itemValue

  return (
    <div
      ref={ref}
      className={cn(
        "overflow-hidden text-sm",
        isOpen ? "animate-accordion-down" : "animate-accordion-up",
        !isOpen && "hidden",
        className
      )}
      {...props}
    >
      <div className={cn("pb-4 pt-0", className)}>{children}</div>
    </div>
  )
})
AccordionContent.displayName = "AccordionContent"

// Wrap AccordionItem to provide context
const AccordionItemWithContext = ({ value, className, children, ...props }: AccordionItemProps) => {
  return (
    <AccordionItemContext.Provider value={value}>
      <AccordionItem value={value} className={className} {...props}>
        {children}
      </AccordionItem>
    </AccordionItemContext.Provider>
  )
}

export { 
  Accordion, 
  AccordionItemWithContext as AccordionItem, 
  AccordionTrigger, 
  AccordionContent 
} 