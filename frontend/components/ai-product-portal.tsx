'use client'

import { useState, ReactNode, useEffect, useRef } from 'react'
import { FileText, ImageIcon, MessageSquare, Network, Users, ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from "@/lib/utils"
import Image from 'next/image'
import MultiModelChat from './MultiModelChat'
import FluxplorerInterface from './FluxplorerInterface'
import MindmapInterface from './MindmapInterface'
import LocalChat from './LocalChat'
import MCPChat from './MCPChat'
import { SparklesCore } from "@/components/ui/sparkles"

interface AIProduct {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  component?: React.ReactNode
}

interface AiProductPortalProps {
  children: ReactNode
}

export function AiProductPortal({ children }: AiProductPortalProps) {
  const [selectedProduct, setSelectedProduct] = useState<string>('1')
  const navRef = useRef<HTMLDivElement>(null)

  const handleScrollLeft = () => {
    if (navRef.current) {
      navRef.current.scrollBy({
        left: -200,
        behavior: 'smooth'
      });
    }
  };

  const handleScrollRight = () => {
    if (navRef.current) {
      navRef.current.scrollBy({
        left: 200,
        behavior: 'smooth'
      });
    }
  };

  const products: AIProduct[] = [
    {
      id: '1',
      name: 'Knowledge Base',
      description: 'AI-powered document search',
      icon: <FileText className="h-5 w-5" />,
    },
    {
      id: '2',
      name: 'Multi-Model Chat',
      description: 'Intelligent conversational AI',
      icon: <Users className="h-5 w-5" />,
      component: <MultiModelChat />
    },
    {
      id: '3',
      name: 'Fluxplorer',
      description: 'AI image generation',
      icon: <ImageIcon className="h-5 w-5" />,
      component: <FluxplorerInterface />
    },
    {
      id: '4',
      name: 'Mindmap Generator',
      description: 'AI-powered mindmap generation',
      icon: <Network className="h-5 w-5" />,
      component: <MindmapInterface />
    },
    {
      id: '5',
      name: 'Local AI Chat',
      description: 'Chat with local AI models',
      icon: <MessageSquare className="h-5 w-5 scale-x-[-1]" />,
      component: <LocalChat />
    },
    {
      id: '6',
      name: 'Agentic AI',
      description: 'Advanced AI with external tools',
      icon: <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>,
      component: <MCPChat />
    },
  ]

  return (
    <div className="min-h-screen w-full">
      {/* Mobile header - visible only on small screens */}
      <header className="lg:hidden relative border-b border-white/10 bg-black from-gray-900 via-slate-900 to-black backdrop-blur-sm overflow-hidden">
        <div className="container mx-auto py-3 relative z-10">
          <h1 className="text-2xl font-bold text-center text-white flex flex-col items-center justify-center gap-2">
            <div className="flex items-center gap-2">
              <Image 
                src="/favicon.ico" 
                alt="AI Portal Logo" 
                width={32} 
                height={32} 
                className="w-8 h-8"
              />
              AI Product Portal
              <Image 
                src="/favicon.ico" 
                alt="AI Portal Logo" 
                width={32} 
                height={32} 
                className="w-8 h-8"
              />
            </div>
            <div className="relative w-[400px] h-5">
              <div className="absolute inset-x-20 top-0 bg-gradient-to-r from-transparent via-indigo-500 to-transparent h-[2px] w-3/4 blur-sm" />
              <div className="absolute inset-x-20 top-0 bg-gradient-to-r from-transparent via-indigo-500 to-transparent h-px w-3/4" />
              <div className="absolute inset-x-40 top-0 bg-gradient-to-r from-transparent via-sky-500 to-transparent h-[5px] w-1/3 blur-sm" />
              <div className="absolute inset-x-40 top-0 bg-gradient-to-r from-transparent via-sky-500 to-transparent h-px w-1/3" />
              <SparklesCore
                background="transparent"
                minSize={0.4}
                maxSize={1}
                particleDensity={1200}
                className="w-full h-full"
                particleColor="#FFFFFF"
              />
              <div className="absolute inset-0 w-full h-full bg-black [mask-image:radial-gradient(350px_200px_at_top,transparent_20%,white)]"></div>
            </div>
          </h1>
        </div>
      </header>

      {/* Desktop layout */}
      <div className="flex flex-col lg:flex-row">
        {/* Sidebar with header on top for desktop */}
        <div className="hidden lg:flex lg:flex-col lg:w-64 lg:min-h-screen bg-gray-900">
          {/* Desktop header */}
          <div className="border-b border-white/10 bg-black from-gray-900 via-slate-900 to-black backdrop-blur-sm overflow-hidden">
            <div className="py-3 relative z-10">
              <h1 className="text-2xl font-bold text-center text-white flex flex-col items-center justify-center gap-2">
                <div className="flex items-center gap-2">
                  <Image 
                    src="/favicon.ico" 
                    alt="AI Portal Logo" 
                    width={32} 
                    height={32} 
                    className="w-8 h-8"
                  />
                  AI Portal
                  <Image 
                    src="/favicon.ico" 
                    alt="AI Portal Logo" 
                    width={32} 
                    height={32} 
                    className="w-8 h-8"
                  />
                </div>
                <div className="relative w-full h-5">
                  <div className="absolute inset-x-4 top-0 bg-gradient-to-r from-transparent via-indigo-500 to-transparent h-[2px] w-3/4 blur-sm" />
                  <div className="absolute inset-x-4 top-0 bg-gradient-to-r from-transparent via-indigo-500 to-transparent h-px w-3/4" />
                  <div className="absolute inset-x-8 top-0 bg-gradient-to-r from-transparent via-sky-500 to-transparent h-[5px] w-1/3 blur-sm" />
                  <div className="absolute inset-x-8 top-0 bg-gradient-to-r from-transparent via-sky-500 to-transparent h-px w-1/3" />
                  <SparklesCore
                    background="transparent"
                    minSize={0.4}
                    maxSize={1}
                    particleDensity={1200}
                    className="w-full h-full"
                    particleColor="#FFFFFF"
                  />
                  <div className="absolute inset-0 w-full h-full bg-black [mask-image:radial-gradient(350px_200px_at_top,transparent_20%,white)]"></div>
                </div>
              </h1>
            </div>
          </div>
          {/* Sidebar navigation */}
          <div className="flex-1 p-4">
            <div className="space-y-1">
              {products.map((product) => (
                <button
                  key={product.id}
                  onClick={() => setSelectedProduct(product.id)}
                  className={cn(
                    "flex items-start gap-3 w-full px-4 py-3 text-left text-sm transition-colors rounded-lg",
                    selectedProduct === product.id
                      ? "bg-cyan-500 text-white"
                      : "text-white hover:bg-white/10"
                  )}
                >
                  <span className="mt-0.5">{product.icon}</span>
                  <div>
                    <div className="font-medium">{product.name}</div>
                    <div className="text-xs opacity-70">{product.description}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Mobile navigation */}
        <div className="lg:hidden w-full relative">
          {/* Left scroll button */}
          <button
            onClick={handleScrollLeft}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 p-1.5 rounded-r-lg bg-cyan-500/80 text-white backdrop-blur-sm hover:bg-cyan-600/80 transition-colors"
            aria-label="Scroll left"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>

          {/* Right scroll button */}
          <button
            onClick={handleScrollRight}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 p-1.5 rounded-l-lg bg-cyan-500/80 text-white backdrop-blur-sm hover:bg-cyan-600/80 transition-colors"
            aria-label="Scroll right"
          >
            <ChevronRight className="h-5 w-5" />
          </button>

          <div ref={navRef} className="flex p-2 space-x-2 bg-black/30 backdrop-blur-sm overflow-x-auto scrollbar-hide">
            {products.map((product) => (
              <button
                key={product.id}
                onClick={() => setSelectedProduct(product.id)}
                className={cn(
                  "flex flex-col items-center p-2 rounded min-w-[80px] text-center transition-colors",
                  selectedProduct === product.id
                    ? "bg-cyan-500 text-white"
                    : "bg-white/10 text-white hover:bg-white/20"
                )}
              >
                <span>{product.icon}</span>
                <div className="text-xs mt-1 font-medium">{product.name}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 lg:ml-0 p-3 md:p-6">
          <div className="rounded-xl border border-white/10 bg-black/50 p-3 md:p-6 backdrop-blur-xl">
            {selectedProduct === '1' ? children : products.find((p) => p.id === selectedProduct)?.component}
          </div>
        </div>
      </div>
    </div>
  )
}