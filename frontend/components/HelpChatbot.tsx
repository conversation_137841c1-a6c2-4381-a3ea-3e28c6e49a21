import { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, RotateCw, Send } from 'lucide-react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface Message {
  content: string;
  isAi: boolean;
  timestamp: Date;
}

export default function HelpChatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [sessionId, setSessionId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [apiKey, setApiKey] = useState<string>('');
  const [chatApiUrl, setChatApiUrl] = useState<string>('');
  const chatRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Fetch config to get chat API URL and API key
    const fetchConfig = async () => {
      try {
        const response = await fetch('/api/config');
        const config = await response.json();
        setChatApiUrl(config.chatApiUrl);
        setApiKey(config.apiKey);
      } catch (error) {
        console.error('Error fetching config:', error);
        // Fallback to default URL
        setChatApiUrl('https://yrqd369zd7.execute-api.ap-northeast-1.amazonaws.com/test/chat');
      }
    };

    fetchConfig();
  }, []);

  const scrollToBottom = () => {
    if (chatRef.current) {
      chatRef.current.scrollTop = chatRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 500); // 500ms delay before closing
  };

  const handleNewSession = async () => {
    if (!apiKey || !chatApiUrl) {
      console.error('API key or chat URL not available');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(chatApiUrl, {
        method: 'POST',
        mode: 'cors',
        credentials: 'omit',
        headers: {
          'Content-Type': 'application/json',
          'Origin': window.location.origin,
          'x-api-key': apiKey,
        },
        body: JSON.stringify({
          message: "Hello, I need help!",
          sessionId: "",
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setSessionId(data.sessionId);
      setMessages([
        {
          content: data.response,
          isAi: true,
          timestamp: new Date(),
        },
      ]);
    } catch (error) {
      console.error('Error starting new session:', error);
      setMessages(prev => [...prev, {
        content: "Sorry, I'm having trouble connecting. Please try again later.",
        isAi: true,
        timestamp: new Date(),
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading || !apiKey || !chatApiUrl) return;

    const userMessage = {
      content: input,
      isAi: false,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const response = await fetch(chatApiUrl, {
        method: 'POST',
        mode: 'cors',
        credentials: 'omit',
        headers: {
          'Content-Type': 'application/json',
          'Origin': window.location.origin,
          'x-api-key': apiKey,
        },
        body: JSON.stringify({
          message: input,
          sessionId: sessionId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      setMessages(prev => [...prev, {
        content: data.response,
        isAi: true,
        timestamp: new Date(),
      }]);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages(prev => [...prev, {
        content: "Sorry, I'm having trouble responding. Please try again later.",
        isAi: true,
        timestamp: new Date(),
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div 
      className="fixed bottom-24 right-4 z-[10001]"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Chat Window */}
      <div className={cn(
        "absolute bottom-16 right-0 w-80 bg-gray-900 rounded-lg shadow-xl transition-all duration-300 ease-in-out",
        isOpen ? "opacity-100 scale-100" : "opacity-0 scale-95 pointer-events-none"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Help Chat</h3>
          <button
            onClick={handleNewSession}
            className="p-1 hover:bg-gray-700 rounded-full transition-colors"
            title="New Session"
          >
            <RotateCw className="w-5 h-5 text-gray-300" />
          </button>
        </div>

        {/* Chat Messages */}
        <div 
          ref={chatRef}
          className="h-96 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-800"
        >
          {messages.map((message, index) => (
            <div
              key={index}
              className={cn(
                "flex items-start gap-2.5",
                message.isAi ? "" : "flex-row-reverse"
              )}
            >
              <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
                {message.isAi ? (
                  <Image
                    src="/favicon.ico"
                    alt="AI"
                    width={20}
                    height={20}
                    className="rounded-full"
                  />
                ) : (
                  <div className="w-6 h-6 bg-cyan-500 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold text-white">U</span>
                  </div>
                )}
              </div>
              <div className={cn(
                "flex flex-col w-full max-w-[75%] leading-1.5 p-4 rounded-lg",
                message.isAi ? "bg-gray-700" : "bg-cyan-500"
              )}>
                <p className="text-sm text-white">{message.content}</p>
                <span className="text-xs text-gray-300 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </span>
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex items-center justify-center">
              <div className="animate-pulse text-gray-400">AI is typing...</div>
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Type your message..."
              className="flex-1 bg-gray-800 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-500"
            />
            <button
              onClick={handleSendMessage}
              disabled={isLoading || !input.trim()}
              className="p-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Floating Button */}
      <button
        className={cn(
          "p-3 bg-cyan-500 text-white rounded-full shadow-lg hover:bg-cyan-600 transition-colors",
          isOpen && "bg-cyan-600"
        )}
      >
        <MessageCircle className="w-6 h-6" />
      </button>
    </div>
  );
} 