import React, { useState, useRef, useEffect } from 'react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { ResizableBox } from 'react-resizable';
import confetti from 'canvas-confetti';
import 'react-resizable/css/styles.css';
import { BACKGROUND_IMAGE } from '../lib/constants';

interface EnhancedZoomableImageProps {
  src: string;
  alt: string;
  showConfetti?: boolean;
}

const EnhancedZoomableImage: React.FC<EnhancedZoomableImageProps> = ({ src, alt, showConfetti = false }) => {
  const [height, setHeight] = useState(400);
  const [width, setWidth] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      setWidth(containerRef.current.offsetWidth);
    }
    if (showConfetti) {
      createConfetti();
    }
  }, [showConfetti]);

  const createConfetti = () => {
    if (canvasRef.current) {
      const myConfetti = confetti.create(canvasRef.current, {
        resize: true,
        useWorker: true
      });

      myConfetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.1, y: 0.6 }
      });
      myConfetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.9, y: 0.6 }
      });
    }
  };

  const handleResize = (e: React.SyntheticEvent, { size }: { size: { width: number; height: number } }) => {
    setHeight(size.height);
    if (containerRef.current) {
      setWidth(containerRef.current.offsetWidth);
    }
  };

  return (
    <ResizableBox
      width={Infinity}
      height={height}
      minConstraints={[Infinity, 200]}
      maxConstraints={[Infinity, 800]}
      onResize={handleResize}
      resizeHandles={['s']}
      handle={<div className="custom-handle" />}
    >
      <div 
        ref={containerRef} 
        className="w-full h-full overflow-hidden bg-cover bg-center relative"
        style={{
          backgroundImage: `url(${BACKGROUND_IMAGE})`,
        }}
      >
        <canvas 
          ref={canvasRef}
          className="absolute inset-0 pointer-events-none z-50"
          style={{ width: '100%', height: '100%' }}
        />
        <TransformWrapper
          initialScale={1}
          initialPositionX={0}
          initialPositionY={0}
          minScale={0.1}
          maxScale={8}
          limitToBounds={false}
        >
          {({ zoomIn, zoomOut, resetTransform }) => (
            <React.Fragment>
              <div className="tools absolute top-2 left-2 z-10">
                <button onClick={() => zoomIn()} className="bg-white bg-opacity-50 text-black p-1 rounded mr-1 hover:bg-opacity-75 transition-all">+</button>
                <button onClick={() => zoomOut()} className="bg-white bg-opacity-50 text-black p-1 rounded mr-1 hover:bg-opacity-75 transition-all">-</button>
                <button onClick={() => resetTransform()} className="bg-white bg-opacity-50 text-black p-1 rounded hover:bg-opacity-75 transition-all">Reset</button>
              </div>
              <TransformComponent
                wrapperStyle={{
                  width: '100%',
                  height: '100%',
                }}
                contentStyle={{
                  width: '100%',
                  height: '100%',
                }}
              >
                <img 
                  src={src} 
                  alt={alt} 
                  style={{ 
                    width: '100%', 
                    height: '100%', 
                    objectFit: 'contain',
                    filter: 'contrast(1.1) brightness(1.1)',
                  }} 
                />
              </TransformComponent>
            </React.Fragment>
          )}
        </TransformWrapper>
        <div className="resize-handle absolute bottom-0 left-0 right-0 h-4 bg-gray-300 bg-opacity-50 cursor-ns-resize flex items-center justify-center">
          <div className="w-16 h-1 bg-gray-500 rounded-full"></div>
        </div>
      </div>
    </ResizableBox>
  );
};

export default EnhancedZoomableImage;
