"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { Switch } from './ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Label } from './ui/label'
import { Progress } from './ui/progress'
import { Card, CardContent } from './ui/card'
import { Upload, Globe, Settings } from 'lucide-react'
import axios from 'axios'
import { fetchEnvConfig } from '../lib/api'

interface FileUploadProps {
  onUploadComplete: (fileExisted: boolean) => void
  onUploadStart: () => void
  onUploadProgressChange: (progress: number) => void
  isUploading: boolean
  contextualEmbedding: boolean
  onContextualEmbeddingChange: (checked: boolean) => void
}

export default function FileUpload({ 
  onUploadComplete, 
  onUploadStart,
  onUploadProgressChange,
  isUploading,
  contextualEmbedding, 
  onContextualEmbeddingChange 
}: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null)
  const [language, setLanguage] = useState<string>('en')
  const [maxWorkers, setMaxWorkers] = useState(3)
  const [backendUrl, setBackendUrl] = useState<string>('http://localhost:3201');

  useEffect(() => {
    const loadConfig = async () => {
      const config = await fetchEnvConfig();
      setBackendUrl(config.backendUrl);
    };
    loadConfig();
  }, []);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setFile(event.target.files[0])
    }
  }

  const handleLanguageChange = (value: string) => {
    setLanguage(value)
  }

  const handleMaxWorkersChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value)
    if (!isNaN(value) && value >= 1 && value <= 10) {
      setMaxWorkers(value)
    }
  }

  const handleUpload = async () => {
    if (!file) {
      alert('Please select a file first')
      return
    }

    const allowedTypes = ['text/plain', 'application/pdf']
    const maxSize = 100 * 1024 * 1024 // 100MB

    if (!allowedTypes.includes(file.type)) {
      alert('Invalid file type. Please upload a txt or pdf file.')
      return
    }

    if (file.size > maxSize) {
      alert('File is too large. Maximum size is 100MB.')
      return
    }

    onUploadStart()

    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await axios.post(
        `${backendUrl}/upload?lang=${language}&max_workers=${maxWorkers}&contextual_embedding_query=${contextualEmbedding}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            const total = progressEvent.total ?? progressEvent.loaded
            const percentCompleted = Math.round((progressEvent.loaded * 50) / total)
            onUploadProgressChange(percentCompleted)
          },
        }
      )

      if (response.status === 200) {
        const fileExisted = response.data.message.includes("already exists")
        console.log(`Upload response: ${response.data.message}`)
        if (fileExisted) {
          onUploadComplete(true)
          alert(response.data.message)
        }
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.detail 
          ? Array.isArray(error.response.data.detail) 
            ? error.response.data.detail.join(', ') 
            : error.response.data.detail
          : error.message
        alert(`Error uploading file: ${errorMessage}`)
      } else {
        alert('An unexpected error occurred while uploading the file')
      }
      onUploadComplete(false)
    }
  }

  return (
    <Card>
      <CardContent className="space-y-6 p-6">
        <div className="space-y-2">
          <Label htmlFor="file-upload">Select File</Label>
          <div className="flex items-center space-x-2">
            <Input id="file-upload" type="file" onChange={handleFileChange} className="flex-grow" />
            <Button onClick={handleUpload} disabled={isUploading}>
              {isUploading ? (
                <span className="animate-spin">↻</span>
              ) : (
                <Upload className="w-4 h-4 mr-2" />
              )}
              Upload
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="language-select">Language</Label>
          <Select value={language} onValueChange={handleLanguageChange}>
            <SelectTrigger id="language-select">
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="zh">Chinese</SelectItem>
              <SelectItem value="ja">Japanese</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-4">
          <div className="flex-1 space-y-2">
            <Label htmlFor="max-workers">Max Workers</Label>
            <Input
              type="number"
              id="max-workers"
              value={maxWorkers}
              onChange={handleMaxWorkersChange}
              min={1}
              max={10}
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="contextual-embedding-upload"
              checked={contextualEmbedding}
              onCheckedChange={onContextualEmbeddingChange}
            />
            <Label htmlFor="contextual-embedding-upload">Contextual Embedding</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}