"use client"

import React, { useState, useEffect, KeyboardEvent } from 'react'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from './ui/select'
import { Switch } from "./ui/switch"
import { <PERSON><PERSON><PERSON> } from "./ui/slider"
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card"
import { Label } from "./ui/label"
import { Checkbox } from "./ui/checkbox"
import { AlertCircle, Send, Settings, Search } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from "./ui/alert"
import { fetchEnvConfig } from '../lib/api'
import ResultDisplay from './ResultDisplay'
import confetti from 'canvas-confetti'

interface ChatInterfaceProps {
  filesUploaded: boolean
  contextualEmbedding: boolean
  onContextualEmbeddingChange: (checked: boolean) => void
  searchResults: string
  onSearchResults: (results: string) => void
  selectedLLM: string
  onLLMChange: (value: string) => void
  selectedSearchEngine: string
  onSearchEngineChange: (value: string) => void
  topK: number
  onTopKChange: (value: number) => void
  relevanceThreshold: number
  onRelevanceThresholdChange: (value: number) => void
  fullDocsSearch: boolean
  onFullDocsSearchChange: (checked: boolean) => void
  rerankMethod: string
  onRerankMethodChange: (value: string) => void
}

export default function ChatInterface({ 
  filesUploaded, 
  contextualEmbedding , 
  onContextualEmbeddingChange,
  searchResults,
  onSearchResults,
  selectedLLM = "claude",  // Default to Claude
  onLLMChange,
  selectedSearchEngine = "bing",  // Default to Bing
  onSearchEngineChange,
  topK = 20,  // Default TopK
  onTopKChange,
  relevanceThreshold,
  onRelevanceThresholdChange,
  fullDocsSearch = true,  // Enable Full Docs search
  onFullDocsSearchChange,
  rerankMethod = "jina",  // Default Rerank Method
  onRerankMethodChange
}: ChatInterfaceProps) {
  const [query, setQuery] = useState('')
  const [files, setFiles] = useState<string[]>([])
  const [selectedFile, setSelectedFile] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false)
  const canvasRef = React.useRef<HTMLCanvasElement>(null);
  const [backendUrl, setBackendUrl] = useState<string>('http://localhost:3201');

  useEffect(() => {
    const loadConfig = async () => {
      const config = await fetchEnvConfig();
      setBackendUrl(config.backendUrl);
    };
    loadConfig();
  }, []);

  // Separate effect for loading files
  useEffect(() => {
    const loadFiles = async () => {
      try {
        const response = await fetch(`${backendUrl}/files`);
        if (response.ok) {
          const data = await response.json();
          setFiles(data.files);
          // Only set default file if no file is selected and no saved preference
          if (!selectedFile && data.files.length > 0) {
            const savedFile = localStorage.getItem('chat_selectedFile');
            if (!savedFile || !data.files.includes(savedFile)) {
              setSelectedFile(data.files[0]);
              localStorage.setItem('chat_selectedFile', data.files[0]);
            }
          }
        } else {
          console.error('Failed to fetch files');
        }
      } catch (error) {
        console.error('Error loading files:', error);
      }
    };

    if (backendUrl) {
      loadFiles();
    }
  }, [backendUrl, selectedFile]);

  // Separate effect for loading saved preferences
  useEffect(() => {
    try {
      const savedFile = localStorage.getItem('chat_selectedFile');
      const savedLLM = localStorage.getItem('chat_selectedLLM');
      const savedSearchEngine = localStorage.getItem('chat_selectedSearchEngine');
      const savedTopK = localStorage.getItem('chat_topK');
      const savedFullDocsSearch = localStorage.getItem('chat_fullDocsSearch');
      const savedRerankMethod = localStorage.getItem('chat_rerankMethod');
      const savedContextualEmbedding = localStorage.getItem('chat_contextualEmbedding');
      const savedShowAdvanced = localStorage.getItem('chat_showAdvancedSettings');

      if (savedFile && files.includes(savedFile)) setSelectedFile(savedFile);
      if (savedLLM) onLLMChange(savedLLM);
      if (savedSearchEngine) onSearchEngineChange(savedSearchEngine);
      if (savedTopK) onTopKChange(parseInt(savedTopK));
      if (savedFullDocsSearch) onFullDocsSearchChange(savedFullDocsSearch === 'true');
      if (savedRerankMethod) onRerankMethodChange(savedRerankMethod);
      if (savedContextualEmbedding) onContextualEmbeddingChange(savedContextualEmbedding === 'true');
      if (savedShowAdvanced) setShowAdvancedSettings(savedShowAdvanced === 'true');
    } catch (error) {
      console.error('Error loading saved preferences:', error);
    }
  }, [files]); // Only run when files list changes

  // Save preferences when they change
  useEffect(() => {
    if (!selectedFile) return; // Don't save if no file is selected
    try {
      localStorage.setItem('chat_selectedFile', selectedFile);
      localStorage.setItem('chat_selectedLLM', selectedLLM);
      localStorage.setItem('chat_selectedSearchEngine', selectedSearchEngine);
      localStorage.setItem('chat_topK', topK.toString());
      localStorage.setItem('chat_fullDocsSearch', fullDocsSearch.toString());
      localStorage.setItem('chat_rerankMethod', rerankMethod);
      localStorage.setItem('chat_contextualEmbedding', contextualEmbedding.toString());
      localStorage.setItem('chat_showAdvancedSettings', showAdvancedSettings.toString());
    } catch (error) {
      console.error('Error saving preferences:', error);
    }
  }, [selectedFile, selectedLLM, selectedSearchEngine, topK, fullDocsSearch, rerankMethod, contextualEmbedding, showAdvancedSettings]);

  const createConfetti = () => {
    if (canvasRef.current) {
      const myConfetti = confetti.create(canvasRef.current, {
        resize: true,
        useWorker: true
      });

      myConfetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.1, y: 0.6 }
      });
      myConfetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.9, y: 0.6 }
      });
    }
  }

  const handleQueryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(event.target.value)
    console.log(`Query changed: ${event.target.value}`)
  }

  const handleFileChange = (value: string) => {
    setSelectedFile(value);
    localStorage.setItem('chat_selectedFile', value);
    console.log(`Selected file changed: ${value}`);
  }

  const handleLLMChange = (value: string) => {
    onLLMChange(value)
    console.log(`Selected LLM changed: ${value}`)
  }

  const handleTopKChange = (value: number[]) => {
    onTopKChange(value[0])
    console.log(`Top k changed to: ${value[0]}`)
  }

  const handleRelevanceThresholdChange = (value: number[]) => {
    onRelevanceThresholdChange(value[0])
    console.log(`Relevance threshold changed to: ${value[0]}`)
  }

  const handleFullDocsSearchChange = (checked: boolean) => {
    onFullDocsSearchChange(checked)
    console.log(`Full Docs search changed to: ${checked}`)
  }

  const handleRerankMethodChange = (value: string) => {
    onRerankMethodChange(value)
    console.log(`Rerank method changed to: ${value}`)
  }

  const handleSearchEngineChange = (value: string) => {
    onSearchEngineChange(value)
    console.log(`Selected search engine changed: ${value}`)
  }

  const handleKeyPress = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleSubmit(event)
    }
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    if (!query || (!selectedFile && !fullDocsSearch) || !selectedLLM) return

    console.log(`Submitting query: ${query} ${fullDocsSearch ? 'for all files' : `for file: ${selectedFile}`} using LLM: ${selectedLLM}, search engine: ${selectedSearchEngine}, and rerank method: ${rerankMethod}`)
    setLoading(true)

    try {
      const response = await fetch(`${backendUrl}/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          query, 
          file_name: fullDocsSearch ? "full search" : selectedFile, 
          llm: selectedLLM, 
          k: topK, 
          threshold: relevanceThreshold,
          full_docs_search: fullDocsSearch,
          rerank_method: rerankMethod,
          contextual_embedding_query: contextualEmbedding,
          search_engine: selectedSearchEngine
        }),
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Query response:', data)
        onSearchResults(JSON.stringify(data))
        createConfetti()
      } else {
        const errorData = await response.json()
        console.error('Error response:', errorData)
        onSearchResults(`Error querying the system: ${JSON.stringify(errorData)}`)
      }
    } catch (error) {
      console.error('Error:', error)
      onSearchResults('Error querying the system')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full relative">
      <canvas 
        ref={canvasRef}
        className="absolute inset-0 pointer-events-none"
        style={{ width: '100%', height: '100%' }}
      />
      <CardHeader>
        <CardTitle>Knowledge Base Search</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {!filesUploaded && (
          <Alert variant="default">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Note</AlertTitle>
            <AlertDescription>
              No files have been uploaded yet. You can still query existing knowledge, but results may be limited.
            </AlertDescription>
          </Alert>
        )}
        
        <div className="flex space-x-4">
          <Select value={selectedFile} onValueChange={handleFileChange} disabled={fullDocsSearch}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a file" />
            </SelectTrigger>
            <SelectContent>
              {files.map((file) => (
                <SelectItem key={file} value={file}>
                  {file}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedLLM} onValueChange={handleLLMChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select an LLM" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="claude">Claude</SelectItem>
              <SelectItem value="openrouter">OpenRouter</SelectItem>
              <SelectItem value="ollama">Ollama</SelectItem>
              <SelectItem value="xunzi">古文大师</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedSearchEngine} onValueChange={handleSearchEngineChange}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a search engine" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="linkup">LinkUp</SelectItem>
              <SelectItem value="tavily">Tavily</SelectItem>
              <SelectItem value="serper">Serper</SelectItem>
              <SelectItem value="bing">Bing</SelectItem>
              <SelectItem value="google">Google</SelectItem>
              <SelectItem value="exa">Exa</SelectItem>
              <SelectItem value="txyz">TXYZ</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Input
            type="text"
            value={query}
            onChange={handleQueryChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter your query"
            className="flex-grow"
          />
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? <span className="animate-spin">↻</span> : <Send className="w-4 h-4" />}
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}>
            <Settings className="w-4 h-4 mr-2" />
            {showAdvancedSettings ? 'Hide' : 'Show'} Advanced Settings
          </Button>
        </div>

        {showAdvancedSettings && (
          <Card>
            <CardContent className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="topK">Top k: {topK}</Label>
                <Slider
                  id="topK"
                  min={1}
                  max={200}
                  step={1}
                  value={[topK]}
                  onValueChange={handleTopKChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="relevanceThreshold">Relevance Threshold: {relevanceThreshold}</Label>
                <Slider
                  id="relevanceThreshold"
                  min={1}
                  max={5}
                  step={1}
                  value={[relevanceThreshold]}
                  onValueChange={handleRelevanceThresholdChange}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="fullDocsSearch"
                  checked={fullDocsSearch}
                  onCheckedChange={handleFullDocsSearchChange}
                />
                <Label htmlFor="fullDocsSearch">Full Docs search</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="rerankMethod">Rerank Method</Label>
                <Select value={rerankMethod} onValueChange={handleRerankMethodChange}>
                  <SelectTrigger id="rerankMethod">
                    <SelectValue placeholder="Rerank method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="jina">Jina</SelectItem>
                    <SelectItem value="flash">Flash</SelectItem>
                    <SelectItem value="cohere">Cohere</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="contextual-embedding"
                  checked={contextualEmbedding}
                  onCheckedChange={onContextualEmbeddingChange}
                />
                <Label htmlFor="contextual-embedding">Contextual Embedding</Label>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="mt-6">
          <ResultDisplay results={searchResults} />
        </div>
      </CardContent>
    </Card>
  )
}
