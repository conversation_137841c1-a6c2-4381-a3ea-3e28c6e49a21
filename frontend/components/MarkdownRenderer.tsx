import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import 'katex/dist/katex.min.css';
import { cn } from '../lib/utils';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

/**
 * Enhanced markdown renderer component that supports:
 * - GitHub Flavored Markdown
 * - LaTeX math expressions (inline and block)
 * - Syntax highlighting for code blocks
 * - Raw HTML content
 * 
 * Uses react-markdown with plugins for better rendering
 */
export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className }) => {
  const processedContent = useMemo(() => {
    if (!content) return '';
    
    let processedContent = content;

    // Handle multi-line LaTeX with align environment
    processedContent = processedContent.replace(/\\begin\{align\}([\s\S]*?)\\end\{align\}/g, '$$\\begin{align}$1\\end{align}$$');
    processedContent = processedContent.replace(/\\begin\{aligned\}([\s\S]*?)\\end\{aligned\}/g, '$$\\begin{aligned}$1\\end{aligned}$$');
    
    // Handle cases environment
    processedContent = processedContent.replace(/\\begin\{cases\}([\s\S]*?)\\end\{cases\}/g, '$$\\begin{cases}$1\\end{cases}$$');
    
    // Handle matrix environments with equation assignment (like A = \begin{pmatrix}...)
    processedContent = processedContent.replace(/([A-Za-z][A-Za-z0-9_]*)\s*=\s*\\begin\{([a-z]*matrix)\}([\s\S]*?)\\end\{([a-z]*matrix)\}/g, (match, variable, begin, matrixContent, end) => {
      if (begin === end) {
        // Add extra spacing between matrix rows for better readability
        const formattedContent = matrixContent.replace(/\\\\/g, '\\\\ ');
        return `$$${variable} = \\begin{${begin}}${formattedContent}\\end{${end}}$$`;
      }
      return match;
    });
    
    // Handle standalone matrix environments
    processedContent = processedContent.replace(/\\begin\{([a-z]*matrix)\}([\s\S]*?)\\end\{([a-z]*matrix)\}/g, (match, begin, matrixContent, end) => {
      if (begin === end) {
        // Add extra spacing between matrix rows for better readability
        const formattedContent = matrixContent.replace(/\\\\/g, '\\\\ ');
        return `$$\\begin{${begin}}${formattedContent}\\end{${end}}$$`;
      }
      return match;
    });
    
    // Handle array environments with column specifications
    processedContent = processedContent.replace(/\\begin\{array\}\{([^}]*)\}([\s\S]*?)\\end\{array\}/g, (match, columnSpec, arrayContent) => {
      // Add extra spacing between array rows for better readability
      const formattedContent = arrayContent.replace(/\\\\/g, '\\\\ ');
      return `$$\\begin{array}{${columnSpec}}${formattedContent}\\end{array}$$`;
    });
    
    // Handle determinant notation with vmatrix
    processedContent = processedContent.replace(/\\det\([A-Za-z][A-Za-z0-9_]*\)\s*=\s*\\begin\{vmatrix\}([\s\S]*?)\\end\{vmatrix\}/g, (match) => {
      return `$$${match}$$`;
    });
    
    // Handle standalone determinant with vmatrix
    processedContent = processedContent.replace(/\\begin\{vmatrix\}([\s\S]*?)\\end\{vmatrix\}/g, (match, content) => {
      // Add extra spacing between matrix rows for better readability
      const formattedContent = content.replace(/\\\\/g, '\\\\ ');
      return `$$\\begin{vmatrix}${formattedContent}\\end{vmatrix}$$`;
    });
    
    // Process Bmatrix, Vmatrix, etc. (capitalized variants)
    processedContent = processedContent.replace(/\\begin\{([A-Z][a-z]*matrix)\}([\s\S]*?)\\end\{([A-Z][a-z]*matrix)\}/g, (match, begin, matrixContent, end) => {
      if (begin === end) {
        // Add extra spacing between matrix rows for better readability
        const formattedContent = matrixContent.replace(/\\\\/g, '\\\\ ');
        return `$$\\begin{${begin}}${formattedContent}\\end{${end}}$$`;
      }
      return match;
    });
    
    // Handle matrices with equation number (like \begin{pmatrix}...\end{pmatrix} \tag{1})
    processedContent = processedContent.replace(/(\\begin\{([a-z]*matrix)\}[\s\S]*?\\end\{([a-z]*matrix)\})\s*\\tag\{([^}]*)\}/g, (match, matrixPart, begin, end, tag) => {
      if (begin === end) {
        return `$$${matrixPart}\\tag{${tag}}$$`;
      }
      return match;
    });
    
    // Make sure inline math with single $ is properly spaced for better rendering
    processedContent = processedContent.replace(/([^\s$])\$([^$]+)\$([^\s$])/g, '$1 $$$2$$ $3');
    
    return processedContent;
  }, [content]);

  return (
    <div className={cn('markdown-content prose dark:prose-invert max-w-none', className)}>
      <ReactMarkdown
        remarkPlugins={[remarkMath, remarkGfm]}
        rehypePlugins={[rehypeRaw, [rehypeKatex, { output: 'mathml' }]]}
        components={{
          code({ className, children, ...props }: any) {
            const match = /language-(\w+)/.exec(className || '');
            const isInline = !match;
            
            return !isInline ? (
              <SyntaxHighlighter
                style={vscDarkPlus as any}
                language={match ? match[1] : ''}
                PreTag="div"
                {...props}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            ) : (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          // Custom styling for other elements
          h1: ({ node, ...props }: any) => <h1 className="text-2xl font-bold my-4" {...props} />,
          h2: ({ node, ...props }: any) => <h2 className="text-xl font-bold my-3" {...props} />,
          h3: ({ node, ...props }: any) => <h3 className="text-lg font-bold my-2" {...props} />,
          h4: ({ node, ...props }: any) => <h4 className="text-base font-bold my-1" {...props} />,
          p: ({ node, ...props }: any) => <p className="my-2" {...props} />,
          ul: ({ node, ...props }: any) => <ul className="list-disc pl-5 my-2" {...props} />,
          ol: ({ node, ...props }: any) => <ol className="list-decimal pl-5 my-2" {...props} />,
          li: ({ node, ...props }: any) => <li className="my-1" {...props} />,
          a: ({ node, ...props }: any) => <a className="text-blue-500 hover:underline" {...props} />,
          blockquote: ({ node, ...props }: any) => (
            <blockquote className="border-l-4 border-gray-300 pl-4 italic my-2" {...props} />
          ),
          table: ({ node, ...props }: any) => (
            <div className="overflow-x-auto my-2">
              <table className="min-w-full divide-y divide-gray-200" {...props} />
            </div>
          ),
          thead: ({ node, ...props }: any) => <thead className="bg-gray-100" {...props} />,
          tbody: ({ node, ...props }: any) => <tbody className="divide-y divide-gray-200" {...props} />,
          tr: ({ node, ...props }: any) => <tr {...props} />,
          th: ({ node, ...props }: any) => (
            <th className="px-4 py-2 text-left text-sm font-medium text-gray-700" {...props} />
          ),
          td: ({ node, ...props }: any) => <td className="px-4 py-2 text-sm" {...props} />,
          hr: ({ node, ...props }: any) => <hr className="my-4 border-gray-300" {...props} />,
          img: ({ node, ...props }: any) => (
            <img className="max-w-full h-auto my-2 rounded" {...props} alt={props.alt || ''} />
          ),
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer; 