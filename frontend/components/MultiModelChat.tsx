'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Input } from './ui/input'
import { Button } from './ui/button'
import { ScrollArea } from './ui/scroll-area'
import { Send, ImagePlus } from 'lucide-react'
import { marked, Renderer } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'
import 'katex/dist/katex.min.css'
import katex from 'katex'
import { Textarea } from './ui/textarea'
import { fetchEnvConfig } from '../lib/api'

// Add these color constants near the top of the file
const MODEL_COLORS = {
  openai: {
    bg: 'bg-emerald-500/10',
    text: 'text-emerald-500',
    border: 'border-emerald-500/20',
    highlight: 'bg-emerald-500/20'
  },
  anthropic: {
    bg: 'bg-[#FF6B6C]/10',
    text: 'text-[#FF6B6C]',
    border: 'border-[#FF6B6C]/20',
    highlight: 'bg-[#FF6B6C]/20'
  },
  openrouter: {
    bg: 'bg-sky-500/10',
    text: 'text-sky-500',
    border: 'border-sky-500/20',
    highlight: 'bg-sky-500/20'
  }
} as const;

// 修改 renderLatex 函数
const renderLatex = (text: string): string => {
  try {
    // 预处理：标准化 LaTeX 代码
    let processedText = text
      // 清理多余的括号和转义字符
      .replace(/\(\\\(|\\\)\)/g, '')  // 移除多余的括号
      .replace(/\\\(\\(\(|\))\\\)/g, '\\$1')  // 清理嵌套的括号
      .replace(/\(\\\(([^)]+)\\\)\)/g, '\\($1\\)')  // 标准化行内数学模式
      .replace(/\\\[\\(\[|\])\\\]/g, '\\$1')  // 清理嵌套的方括号
      .replace(/\\\\([a-zA-Z]+)/g, '\\$1')  // 修复双反斜杠
      .replace(/\(\\\(\\lambda\\\)\)/g, '\\lambda')  // 修复特殊情况
      .replace(/\\text{([^}]+)}/g, '\\text{$1}')  // 修复文本命令
      .replace(/\\\(\\mathbf/g, '\\mathbf')  // 修复向量命令
      .replace(/\\\(\\text/g, '\\text');  // 修复文本命令

    // 处理行内数学模式
    processedText = processedText.replace(/\\\((.*?)\\\)/g, (match, content) => {
      try {
        return katex.renderToString(content, {
          displayMode: false,
          throwOnError: false
        });
      } catch (e) {
        console.error('KaTeX inline math error:', e);
        return match;
      }
    });

    // 处理显示数学模式
    processedText = processedText.replace(/\\\[(.*?)\\\]/g, (match, content) => {
      try {
        return katex.renderToString(content, {
          displayMode: true,
          throwOnError: false
        });
      } catch (e) {
        console.error('KaTeX display math error:', e);
        return match;
      }
    });

    // 处理特殊的 LaTeX 命令
    const latexCommands = {
      'mathbf': true,
      'lambda': true,
      'times': true,
      'cdot': true,
      'det': true,
      'text': true,
      'frac': true,
      'sum': true,
      'int': true,
      'infty': true,
      'partial': true,
      'nabla': true,
      'vec': true,
      'hat': true,
      'bar': true,
    } as const;

    type LatexCommand = keyof typeof latexCommands;

    // 处理独立的 LaTeX 命令
    processedText = processedText.replace(/\\([a-zA-Z]+)(?![{\(\[])/g, (match, cmd) => {
      if (latexCommands[cmd as LatexCommand]) {
        try {
          return katex.renderToString(`\\${cmd}`, {
            displayMode: false,
            throwOnError: false
          });
        } catch (e) {
          console.error(`KaTeX command error for ${cmd}:`, e);
          return match;
        }
      }
      return match;
    });

    return processedText;
  } catch (e) {
    console.error('Error rendering LaTeX:', e);
    return text;
  }
};

// 添加类型定义
interface Text {
  text: string;
  type?: string;
}

interface Escape {
  text: string;
  escaped: boolean;
}

// 修改 marked 渲染器
const customRenderer = new Renderer();

// 修改文本渲染器，正确处理 Text | Escape 类型
customRenderer.text = function(token: Text | Escape): string {
  const text = typeof token === 'string' ? token : token.text;
  return renderLatex(text);
};

// 修改代码渲染器
customRenderer.code = function(code: { text: string, lang?: string, escaped?: boolean }): string {
  // LaTeX 代码块处理
  if (code.lang === 'math' || code.lang === 'latex') {
    try {
      return `<div class="math math-display">${code.text}</div>`;
    } catch (e) {
      console.error('LaTeX code block error:', e);
      return `<pre><code>${code.text}</code></pre>`;
    }
  }

  // 其他代码的高亮处理
  const validLanguage = hljs.getLanguage(code.lang || '') ? code.lang : 'plaintext';
  const highlightedCode = validLanguage 
    ? hljs.highlight(code.text, { language: validLanguage as string }).value
    : hljs.highlightAuto(code.text).value;
  return `<pre><code class="hljs ${validLanguage}">${highlightedCode}</code></pre>`;
};

// 配置 marked
marked.use({ 
  renderer: customRenderer,
  gfm: true,
  breaks: true,
  pedantic: false,
});

// 添加 MathJax 类型定义
declare global {
  interface Window {
    MathJax?: {
      typesetPromise?: (elements: Array<HTMLElement | null>) => Promise<void>;
      tex?: {
        inlineMath: string[][];
        displayMath: string[][];
        processEscapes: boolean;
      };
      svg?: {
        fontCache: string;
      };
    };
  }
}

// 定义模型名称的类型
type ModelName = 'openai' | 'anthropic' | 'openrouter';

interface Message {
  sender: string;
  content: MessageContent[] | string;
  timestamp: string;
}

interface ChatWindowProps {
  title: string
  messages: Message[]
  streamContent: string
  modelNames: Record<string, string>
}

// 修改接口定义，使用 Record 类型而不是映射类型
type StreamContent = Record<ModelName, string>;
type MessageState = Record<ModelName, Message[]>;

// 添加历史消息类型
interface HistoryMessage {
  role: 'user' | 'assistant' | 'system'
  content: string | MessageContent[] | any
}

type ModelHistory = Record<ModelName, HistoryMessage[]>;

const ChatWindow: React.FC<ChatWindowProps> = ({ title, messages, streamContent, modelNames }) => {
  const chatRef = useRef<HTMLDivElement>(null);
  const modelKey = title.toLowerCase() as keyof typeof MODEL_COLORS;
  const colors = MODEL_COLORS[modelKey];

  useEffect(() => {
    if (chatRef.current) {
      chatRef.current.scrollTop = chatRef.current.scrollHeight;
      
      if (window.MathJax?.typesetPromise) {
        setTimeout(() => {
          window.MathJax?.typesetPromise?.([chatRef.current])
            .then(() => {
              return window.MathJax?.typesetPromise?.([chatRef.current]);
            })
            .catch(err => {
              console.error('MathJax rendering error:', err);
            });
        }, 1000);
      }
    }
  }, [messages, streamContent]);

  const renderContent = (content: string | any[]) => {
    if (typeof content === 'string') {
      return (
        <div 
          dangerouslySetInnerHTML={{ __html: marked(renderLatex(content)) }}
          className="prose prose-sm max-w-none dark:prose-invert math-content"
        />
      );
    }
    
    return content.map((item, idx) => {
      if (item.type === 'image_url') {
        return (
          <img 
            key={idx}
            src={item.image_url.url} 
            alt="User uploaded"
            className="max-h-60 rounded-md my-2"
          />
        );
      }
      return (
        <div 
          key={idx}
          dangerouslySetInnerHTML={{ __html: marked(renderLatex(item.text)) }}
          className="prose prose-sm max-w-none dark:prose-invert math-content"
        />
      );
    });
  };

  return (
    <Card className={`h-full border ${colors.border}`}>
      <CardHeader>
        <CardTitle className={`text-lg flex items-center justify-between ${colors.text}`}>
          <div className="flex items-center">
            {title}
            <span className="ml-2 text-sm text-gray-400">
              ({modelNames[title.toLowerCase()]})
            </span>
          </div>
          {streamContent && (
            <span className={`ml-2 inline-block w-2 h-2 rounded-full animate-pulse ${colors.highlight}`} />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div 
          className="h-[500px] overflow-y-auto pr-4" 
          ref={chatRef} 
          data-model={title.toLowerCase()}
        >
          {messages.map((msg, idx) => (
            <div
              key={idx}
              className={`mb-4 p-3 rounded-lg ${
                msg.sender === 'You' 
                  ? 'bg-blue-500/10 text-blue-400' 
                  : `${colors.bg} ${colors.text}`
              }`}
            >
              <div className="flex justify-between items-center mb-2">
                <strong>{msg.sender}</strong>
                <span className="text-xs text-gray-500">{msg.timestamp}</span>
              </div>
              {renderContent(msg.content)}
            </div>
          ))}
          {streamContent && (
            <div className={`stream-message mb-4 p-3 rounded-lg ${colors.bg} ${colors.text}`}>
              <div className="flex justify-between items-center mb-2">
                <strong>{title}</strong>
                <span className="text-xs text-gray-500">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
              <div 
                dangerouslySetInnerHTML={{ __html: marked(renderLatex(streamContent)) }}
                className="prose prose-sm max-w-none dark:prose-invert math-content"
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// 添加 WebSocket 消息类型定义
interface WebSocketMessage {
  type: 'stream' | 'end' | 'error'
  modelName: 'openai' | 'anthropic' | 'openrouter'
  content?: string
  error?: string
}

// Add interface for image data
interface ImageData {
  type: 'image_url'
  image_url: {
    url: string
  }
}

// Add type definitions for message content
interface TextContent {
  type: 'text';
  text: string;
}

interface ImageContent {
  type: 'image_url';
  image_url: {
    url: string;
  };
}

type MessageContent = TextContent | ImageContent;

const MultiModelChat: React.FC = () => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<MessageState>({
    openai: [],
    anthropic: [],
    openrouter: []
  });
  const [streamContent, setStreamContent] = useState<StreamContent>({
    openai: '',
    anthropic: '',
    openrouter: ''
  });
  const [messageHistory, setMessageHistory] = useState<ModelHistory>({
    openai: [],
    anthropic: [],
    openrouter: []
  });
  const [isSending, setIsSending] = useState(false);
  const activeModels = useRef(new Set<string>());
  const [backendUrl, setBackendUrl] = useState<string>('http://localhost:3201');
  const [modelNames, setModelNames] = useState<Record<string, string>>({
    openai: 'GPT-4o-mini',
    anthropic: 'Claude-3-5-Sonnet',
    openrouter: 'Qwen-2.5-Coder-32B'
  });
  const [imageData, setImageData] = useState<ImageData | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const loadConfig = async () => {
      const config = await fetchEnvConfig();
      setBackendUrl(config.backendUrl);
      setModelNames(config.llmModels);
    };
    loadConfig();
  }, []);

  useEffect(() => {
    const wsUrl = backendUrl.replace(/^http/, 'ws') + '/ws';
    const newSocket = new WebSocket(wsUrl);
    setSocket(newSocket);
    
    newSocket.onopen = () => {
      console.log('WebSocket Connected');
    };

    newSocket.onmessage = (event) => {
      const data = JSON.parse(event.data) as WebSocketMessage;
      console.log('Received message:', data);
      
      if (data.type === 'stream' && data.content) {
        setStreamContent(prev => ({
          ...prev,
          [data.modelName]: prev[data.modelName] + data.content
        }));
      } else if (data.type === 'end') {
        // 直接更新消息历史
        setMessages(prev => ({
          ...prev,
          [data.modelName]: [
            ...prev[data.modelName],
            {
              sender: data.modelName,
              content: data.content || '',
              timestamp: new Date().toLocaleTimeString()
            }
          ]
        }));

        // 更新消息历史
        setMessageHistory(prev => ({
          ...prev,
          [data.modelName]: [
            ...prev[data.modelName],
            { role: 'assistant', content: data.content || '' }
          ]
        }));

        // 清理流内容
        setStreamContent(prev => ({
          ...prev,
          [data.modelName]: ''
        }));

        activeModels.current.delete(data.modelName);
        if (activeModels.current.size === 0) {
          setIsSending(false);
        }
      }
    };

    newSocket.onerror = (error) => {
      console.error('WebSocket error:', error);
      setIsSending(false);
    };

    newSocket.onclose = () => {
      console.log('WebSocket disconnected');
      setIsSending(false);
    };

    return () => {
      newSocket.close();
    };
  }, [backendUrl]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64Data = reader.result as string;
        setImageData({
          type: 'image_url',
          image_url: {
            url: base64Data
          }
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    const items = Array.from(e.clipboardData.items);
    for (const item of items) {
      if (item.type.startsWith('image')) {
        const file = item.getAsFile();
        if (file) {
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64Data = reader.result as string;
            setImageData({
              type: 'image_url',
              image_url: {
                url: base64Data
              }
            });
          };
          reader.readAsDataURL(file);
        }
        break;
      }
    }
  };

  const handleSend = () => {
    if ((!message.trim() && !imageData) || !socket || isSending) return;

    setIsSending(true);
    const timestamp = new Date().toLocaleTimeString();
    
    // Create message content array
    const content: MessageContent[] = [];
    if (message.trim()) {
      content.push({ type: 'text', text: message });
    }
    if (imageData) {
      content.push(imageData);
    }

    // Create user message
    const userMessage: Message = {
      sender: 'You',
      content: content,
      timestamp
    };

    // Add user message to all chat windows
    setMessages(prev => {
      const newMessages = { ...prev } as MessageState;
      (Object.keys(newMessages) as ModelName[]).forEach(modelName => {
        newMessages[modelName] = [...newMessages[modelName], userMessage];
      });
      return newMessages;
    });

    // Clear stream content
    setStreamContent({
      openai: '',
      anthropic: '',
      openrouter: ''
    });

    // Track active models
    activeModels.current = new Set<ModelName>(['openai', 'anthropic', 'openrouter']);

    // Send WebSocket message
    if (socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify({
        message,
        imageData,
        models: modelNames
      }));
    }

    setMessage('');
    setImageData(null);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold text-center mb-4 text-white">Multi-Model Chat</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        {(['openai', 'anthropic', 'openrouter'] as const).map((modelName) => (
          <ChatWindow 
            key={modelName}
            title={modelName.charAt(0).toUpperCase() + modelName.slice(1)}
            messages={messages[modelName]}
            streamContent={streamContent[modelName]}
            modelNames={modelNames}
          />
        ))}
      </div>
      <div className="flex space-x-2">
        <input 
          type="file"
          accept="image/*"
          className="hidden"
          ref={fileInputRef}
          onChange={handleImageUpload}
        />
        <Button
          onClick={() => fileInputRef.current?.click()}
          className="h-[120px] px-4 bg-gray-700 hover:bg-gray-600"
        >
          <ImagePlus className="w-6 h-6" />
        </Button>
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onPaste={handlePaste}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              // Allow new lines with Enter
              return
            }
          }}
          placeholder="Type your message... (Enter for new line)"
          className="flex-grow text-white placeholder:text-gray-400 min-h-[120px] resize-vertical"
          disabled={isSending}
          rows={4}
        />
        <Button 
          onClick={handleSend} 
          disabled={!message.trim() || isSending}
          className="h-[120px] px-8 bg-[#8CD3FF] hover:bg-[#7BC0EC] text-black transition-all duration-300 group"
        >
          <Send className="w-6 h-6 transition-transform duration-300 group-hover:translate-x-1 group-hover:-translate-y-1" />
          <span className="ml-2 hidden md:inline">{isSending ? 'Sending...' : 'Send'}</span>
        </Button>
      </div>
      {imageData && (
        <div className="mt-2">
          <img 
            src={imageData.image_url.url} 
            alt="Uploaded preview" 
            className="max-h-40 rounded-md"
          />
          <Button
            onClick={() => setImageData(null)}
            className="mt-2"
            variant="destructive"
          >
            Remove Image
          </Button>
        </div>
      )}
    </div>
  );
};

export default MultiModelChat; 