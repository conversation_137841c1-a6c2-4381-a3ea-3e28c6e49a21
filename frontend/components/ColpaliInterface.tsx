import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { ScrollArea } from './ui/scroll-area';
import EnhancedZoomableImage from './EnhancedZoomableImage';
import { Label } from './ui/label';
import { Components } from 'react-markdown';
import { InlineMath, BlockMath } from 'react-katex';
import { fetchEnvConfig } from '../lib/api';

// Add this type definition
type CodeProps = React.ClassAttributes<HTMLElement> & 
  React.HTMLAttributes<HTMLElement> & 
  { inline?: boolean };

interface ColpaliInterfaceProps {
  indexes: string[];
  setIndexes: React.Dispatch<React.SetStateAction<string[]>>;
  selectedIndex: string;
  onSelectedIndexChange: (index: string) => void;
  query: string;
  onQueryChange: (query: string) => void;
  result: { merged_image: string; llm_response: string; result_info: string } | null;
  onResultChange: (result: { merged_image: string; llm_response: string; result_info: string } | null) => void;
}

const ColpaliInterface: React.FC<ColpaliInterfaceProps> = ({
  indexes,
  setIndexes,
  selectedIndex,
  onSelectedIndexChange,
  query,
  onQueryChange,
  result,
  onResultChange
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [backendUrl, setBackendUrl] = useState<string>('http://localhost:3201');

  useEffect(() => {
    const loadConfig = async () => {
      const config = await fetchEnvConfig();
      setBackendUrl(config.backendUrl);
    };
    loadConfig();
  }, []);

  useEffect(() => {
    fetchIndexes();
  }, [backendUrl]);

  // Load saved preferences on component mount
  useEffect(() => {
    try {
      const savedIndex = localStorage.getItem('colpali_selectedIndex');
      const savedQuery = localStorage.getItem('colpali_query');
      
      if (savedIndex) onSelectedIndexChange(savedIndex);
      if (savedQuery) onQueryChange(savedQuery);
    } catch (error) {
      console.error('Error loading saved Colpali preferences:', error);
    }
  }, []);

  // Save preferences when they change
  useEffect(() => {
    try {
      localStorage.setItem('colpali_selectedIndex', selectedIndex);
      localStorage.setItem('colpali_query', query);
    } catch (error) {
      console.error('Error saving Colpali preferences:', error);
    }
  }, [selectedIndex, query]);

  const fetchIndexes = async () => {
    try {
      const response = await fetch(`${backendUrl}/colpali/indexes`);
      const data = await response.json();
      setIndexes(data.indexes);
    } catch (error) {
      console.error('Error fetching indexes:', error);
    }
  };

  const handleSubmit = async () => {
    if (!selectedIndex || !query) return;

    setLoading(true);
    setShowConfetti(false);

    try {
      const response = await fetch(`${backendUrl}/colpali/query`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, index_name: selectedIndex }),
      });
      const data = await response.json();
      onResultChange(data);
      setShowConfetti(true);
    } catch (error) {
      console.error('Error querying Colpali:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  const renderContent = (text: string) => {
    const parts = text.split(/(\$\$[\s\S]*?\$\$|\$[\s\S]*?\$|\\\([\s\S]*?\\\)|\\\[[\s\S]*?\\\])/);
    return parts.map((part, index) => {
      if (part.startsWith('$$') && part.endsWith('$$')) {
        return <BlockMath key={index} math={part.slice(2, -2)} />;
      } else if (part.startsWith('$') && part.endsWith('$')) {
        return <InlineMath key={index} math={part.slice(1, -1)} />;
      } else if (part.startsWith('\\(') && part.endsWith('\\)')) {
        return <InlineMath key={index} math={part.slice(2, -2)} />;
      } else if (part.startsWith('\\[') && part.endsWith('\\]')) {
        return <BlockMath key={index} math={part.slice(2, -2)} />;
      } else {
        return (
          <ReactMarkdown
            key={index}
            remarkPlugins={[remarkMath]}
            rehypePlugins={[rehypeKatex]}
            components={{
              h1: (props) => <h1 className="text-2xl font-bold mt-4 mb-2" {...props} />,
              h2: (props) => <h2 className="text-xl font-bold mt-3 mb-2" {...props} />,
              h3: (props) => <h3 className="text-lg font-bold mt-2 mb-1" {...props} />,
              p: (props) => <p className="mb-2" {...props} />,
              ul: (props) => <ul className="list-disc list-inside mb-2" {...props} />,
              ol: (props) => <ol className="list-decimal list-inside mb-2" {...props} />,
              li: (props) => <li className="ml-4" {...props} />,
              code: ({ className, children, ...props }: CodeProps) => {
                const match = /language-(\w+)/.exec(className || '');
                return !match ? (
                  <code className="bg-gray-100 rounded px-1" {...props}>
                    {children}
                  </code>
                ) : (
                  <pre className="bg-gray-100 rounded p-2 overflow-x-auto">
                    <code className={className} {...props}>
                      {children}
                    </code>
                  </pre>
                );
              },
            }}
          >
            {part}
          </ReactMarkdown>
        );
      }
    });
  };

  return (
    <div className="space-y-4 bg-white p-4 rounded-lg shadow relative">
      <div className="flex items-center space-x-2">
        <Label htmlFor="file-select" className="w-24">Select File:</Label>
        <Select onValueChange={onSelectedIndexChange} value={selectedIndex}>
          <SelectTrigger id="file-select">
            <SelectValue placeholder="Select an index" />
          </SelectTrigger>
          <SelectContent>
            {indexes.map((index) => (
              <SelectItem key={index} value={index}>
                {index}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2">
        <Label htmlFor="query-input" className="w-24">Query:</Label>
        <Input
          id="query-input"
          placeholder="Enter your query"
          value={query}
          onChange={(e) => onQueryChange(e.target.value)}
          onKeyDown={handleKeyDown}
        />
      </div>

      <Button
        onClick={handleSubmit}
        disabled={!selectedIndex || !query || loading}
        className="w-full transition-all duration-300 bg-gradient-to-r from-teal-400 to-blue-500 text-white font-semibold py-2 px-4 rounded-md relative overflow-hidden group"
      >
        <span className="relative z-10">
          {loading ? 'Querying...' : 'Submit Query'}
        </span>
        <div className="absolute top-0 -inset-full h-full w-1/2 z-5 block transform -skew-x-12 bg-gradient-to-r from-transparent to-white opacity-40 group-hover:animate-shine" />
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-teal-400 to-blue-500 group-hover:animate-pause animate-gradient-x" />
      </Button>

      {result && (
        <div className="space-y-4">
          <div className="border border-gray-300 rounded-lg overflow-hidden">
            <EnhancedZoomableImage
              src={`data:image/png;base64,${result.merged_image}`}
              alt="Query result"
              showConfetti={showConfetti}
            />
          </div>

          <ScrollArea className="h-60 w-full rounded-md border p-4">
            <h3 className="font-semibold mb-2">LLM Response:</h3>
            <div className="prose prose-sm max-w-none mb-4">
              {renderContent(result.llm_response)}
            </div>
            <h3 className="font-semibold mb-2">Search Results:</h3>
            <pre className="whitespace-pre-wrap">{result.result_info}</pre>
          </ScrollArea>
        </div>
      )}
    </div>
  );
};

export default ColpaliInterface;
