'use client'

import type { KeyboardEvent, ChangeEvent } from 'react'
import { useState, useEffect, useRef } from 'react'
import { Card, CardContent } from './ui/card'
import { Input } from './ui/input'
import { Button } from './ui/button'
import { ScrollArea } from './ui/scroll-area'
import { Send, User, Bot, Trash2, Image as ImageIcon, Settings } from 'lucide-react'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'
import 'katex/dist/katex.min.css'
import katex from 'katex'
import { fetchEnvConfig } from '../lib/api'
import { useToast } from "../hooks/use-toast"
import { ToastAction } from "./ui/toast"
import { Textarea } from './ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Label } from './ui/label'

interface Message {
  role: 'user' | 'assistant'
  content: string
  timestamp: string
  images?: string[]
}

interface ChatHistory {
  id: string
  title: string
  timestamp: string
}

interface LocalLLMConfig {
  api_url: string
  ollama: string
}

interface UserPrompt {
  id: string
  name: string
  content: string
}

const DEBUG = false

const LocalChat = () => {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState('')
  const [ws, setWs] = useState<WebSocket | null>(null)
  const [streamContent, setStreamContent] = useState('')
  const [histories, setHistories] = useState<ChatHistory[]>([])
  const [selectedHistory, setSelectedHistory] = useState<string | null>(null)
  const [backendUrl, setBackendUrl] = useState('')
  const [config, setConfig] = useState<LocalLLMConfig>({
    api_url: '',
    ollama: ''
  })
  const [userPrompts, setUserPrompts] = useState<UserPrompt[]>([])
  const [selectedPromptId, setSelectedPromptId] = useState<string | null>(null)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [newPromptName, setNewPromptName] = useState('')
  const [newPromptContent, setNewPromptContent] = useState('')
  const scrollRef = useRef<HTMLDivElement>(null)
  const thinkingContentRef = useRef<HTMLDivElement>(null)
  const thinkingContainersRef = useRef<{[key: number]: HTMLDivElement | null}>({})
  const { toast } = useToast()
  const [showAllHistories, setShowAllHistories] = useState(false)
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [isThinking, setIsThinking] = useState(false)
  const [thinkingContent, setThinkingContent] = useState('')
  const isThinkingRef = useRef(false)
  const contentBufferRef = useRef('')
  const [isAddingNew, setIsAddingNew] = useState(false)

  useEffect(() => {
    const loadConfig = async () => {
      try {
        const envConfig = await fetchEnvConfig()
        DEBUG && console.log('Env config:', envConfig)
        setBackendUrl(envConfig.backendUrl)
        
        // Get local LLM config from NEXT_LOCAL_LLM_MODELS
        const localLLMConfig = envConfig.localLLMModels
        DEBUG && console.log('Local LLM config:', localLLMConfig)
        
        if (localLLMConfig) {
          try {
            const parsedConfig = JSON.parse(localLLMConfig)
            DEBUG && console.log('Parsed config:', parsedConfig)
            if (parsedConfig.api_url && parsedConfig.ollama) {
              setConfig(parsedConfig)
            } else {
              DEBUG && console.error('Invalid config structure:', parsedConfig)
            }
          } catch (e) {
            DEBUG && console.error('Failed to parse NEXT_LOCAL_LLM_MODELS:', e)
          }
        } else {
          DEBUG && console.error('NEXT_LOCAL_LLM_MODELS not found in env config')
        }
      } catch (error) {
        DEBUG && console.error('Error loading config:', error)
      }
    }
    void loadConfig()
  }, [])

  useEffect(() => {
    console.log('Current config:', config)
  }, [config])

  const getTitle = () => {
    if (!config.api_url || !config.ollama) {
      return 'Loading configuration...'
    }
    const modelName = config.ollama.charAt(0).toUpperCase() + config.ollama.slice(1)
    const shortUrl = config.api_url.replace(/^https?:\/\//, '')
    return `${modelName} @ ${shortUrl}`
  }

  const parseThinkContent = (content: string) => {
    const thinkMatch = content.match(/<think>([^]*?)<\/think>/)
    if (thinkMatch) {
      return {
        thinking: thinkMatch[1].trim(),
        remaining: content.replace(/<think>[^]*?<\/think>/, '').trim()
      }
    }
    return null
  }

  useEffect(() => {
    if (!backendUrl) return

    const wsUrl = `${backendUrl.replace(/^http/, 'ws')}/ws/local-chat`
    const websocket = new WebSocket(wsUrl)

    websocket.onopen = () => {
      DEBUG && console.log('WebSocket connected')
      websocket.send(JSON.stringify({ type: 'get_histories' }))
    }

    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        switch (data.type) {
          case 'stream':
            if (data.content) {
              DEBUG && console.log('%cReceived content:', 'color: blue; font-weight: bold', data.content)
              
              // 如果是新的消息开始，重置所有状态
              if (!contentBufferRef.current) {
                setThinkingContent('')
                setStreamContent('')
                isThinkingRef.current = false
                setIsThinking(false)
              }
              
              contentBufferRef.current += data.content
              
              const hasCompleteThinkStart = /<think>/i.test(contentBufferRef.current)
              const hasCompleteThinkEnd = /<\/think>/i.test(contentBufferRef.current)
              
              if (hasCompleteThinkStart && !isThinkingRef.current) {
                isThinkingRef.current = true
                setIsThinking(true)
                // 提取 <think> 之后的内容，去掉之前的所有内容
                const thinkStartIndex = contentBufferRef.current.indexOf('<think>') + '<think>'.length
                const thinkContent = contentBufferRef.current.slice(thinkStartIndex)
                setThinkingContent(thinkContent)
                setStreamContent('')
              } else if (hasCompleteThinkEnd && isThinkingRef.current) {
                isThinkingRef.current = false
                setIsThinking(false)
                // 提取 </think> 之后的内容作为新的 buffer
                const thinkEndIndex = contentBufferRef.current.indexOf('</think>') + '</think>'.length
                const remainingContent = contentBufferRef.current.slice(thinkEndIndex)
                contentBufferRef.current = remainingContent
                setStreamContent(remainingContent)
                // 清理 thinking content 中的标签
                setThinkingContent(prev => prev.replace(/<\/?think>/g, ''))
              } else {
                if (!isThinkingRef.current) {
                  setStreamContent(prev => prev + data.content)
                } else {
                  setThinkingContent(prev => prev + data.content)
                }
              }
            }
            break
          case 'end':
            if (data.content) {
              // 重置所有状态
              isThinkingRef.current = false
              setIsThinking(false)
              setThinkingContent('')
              contentBufferRef.current = ''
              
              const message: Message = {
                role: 'assistant',
                content: data.content,
                timestamp: new Date().toISOString()
              }
              setMessages(prev => [...prev, message])
              setStreamContent('')
              
              if (data.historyId && !selectedHistory) {
                setSelectedHistory(data.historyId)
              }
              
              if (!selectedHistory) {
                websocket.send(JSON.stringify({ type: 'get_histories' }))
              }
            }
            break
          case 'history':
            if (data.histories) {
              setHistories(data.histories)
            }
            break
          case 'message':
            if (data.role && data.content && data.timestamp) {
              // 如果是用户消息，检查是否包含系统提示
              let displayContent = data.content
              if (data.role === 'user' && selectedPromptId) {
                const selectedPrompt = userPrompts.find(p => p.id === selectedPromptId)
                if (selectedPrompt && data.content.startsWith(selectedPrompt.content)) {
                  // 移除系统提示部分，只保留用户输入
                  displayContent = data.content.slice(selectedPrompt.content.length).trim()
                }
              }

              const message: Message = {
                role: data.role,
                content: displayContent,
                timestamp: data.timestamp,
                images: data.images
              }
              setMessages(prev => [...prev, message])
            }
            break
          case 'error':
            console.error('WebSocket error:', data.content)
            toast({
              title: "Error",
              description: data.content,
              variant: "destructive",
            })
            break
          case 'delete_response':
            handleDeleteResponse(data)
            break
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error)
      }
    }

    websocket.onerror = (error) => {
      DEBUG && console.error('WebSocket error:', error)
    }

    websocket.onclose = () => {
      DEBUG && console.log('WebSocket disconnected')
    }

    setWs(websocket)

    return () => {
      websocket.close()
    }
  }, [backendUrl])

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [messages, streamContent])

  const handleImageSelect = (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setSelectedImages(prev => [...prev, ...files])
  }

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index))
  }

  const convertImageToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const base64String = (reader.result as string).split(',')[1]
        resolve(base64String)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  const sendMessage = async () => {
    if (!ws || (!input.trim() && selectedImages.length === 0) || !config.api_url || !config.ollama) return

    const base64Images = await Promise.all(selectedImages.map(convertImageToBase64))
    const selectedPrompt = selectedPromptId ? userPrompts.find(p => p.id === selectedPromptId) : null
    const messageContent = selectedPrompt 
      ? `${selectedPrompt.content}\n\n${input}`
      : input

    const userMessage: Message = {
      role: 'user',
      content: input, // Show only the user input in the UI
      timestamp: new Date().toISOString(),
      images: base64Images
    }
    setMessages(prev => [...prev, userMessage])
    setInput('')
    setSelectedImages([])

    try {
      const messageData = {
        type: 'message',
        content: messageContent, // Send the combined prompt + input to the backend
        historyId: selectedHistory,
        images: base64Images,
        config: {
          ollamaUrl: config.api_url,
          ollamaModel: config.ollama
        }
      }
      DEBUG && console.log('Sending message with config:', messageData)
      ws.send(JSON.stringify(messageData))
    } catch (error) {
      DEBUG && console.error('Error sending message:', error)
    }
  }

  const startNewChat = () => {
    setMessages([])
    setSelectedHistory(null)
  }

  const loadChatHistory = (historyId: string) => {
    if (!ws) return
    setSelectedHistory(historyId)
    setMessages([])
    ws.send(JSON.stringify({
      type: 'load_history',
      historyId
    }))
  }

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value)
  }

  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      void sendMessage()
    }
  }

  const renderContent = (content: string) => {
    const renderer = new marked.Renderer()
    renderer.code = function({ text, lang }: { text: string; lang?: string }) {
      const validLanguage = hljs.getLanguage(lang || '') ? lang : 'plaintext'
      const highlightedCode = validLanguage ? hljs.highlight(text, { language: validLanguage }).value : text
      return `<pre><code class="hljs ${validLanguage}">${highlightedCode}</code></pre>`
    }

    marked.setOptions({
      renderer,
      gfm: true,
      breaks: true
    })

    let processedContent = content
    
    // 处理方括号中的matrix环境，将其转换为块级公式格式
    processedContent = processedContent.replace(/\[\s*\\begin\{([a-z]*matrix)\}([\s\S]*?)\\end\{([a-z]*matrix)\}\s*\]/g, (match, begin, content, end) => {
      if (begin === end) {
        return `$$\\begin{${begin}}${content}\\end{${end}}$$`;
      }
      return match;
    });
    
    // 处理 \[...\] 格式（块级公式）
    processedContent = processedContent.replace(/\\\[([\s\S]*?)\\\]/g, (_, tex) => {
      try {
        return katex.renderToString(tex, { displayMode: true })
      } catch (e) {
        DEBUG && console.error('KaTeX error in \\[...\\]:', e)
        return tex
      }
    });
    
    // 处理 \(...\) 格式（内联公式）
    processedContent = processedContent.replace(/\\\(([\s\S]*?)\\\)/g, (_, tex) => {
      try {
        return katex.renderToString(tex, { displayMode: false })
      } catch (e) {
        DEBUG && console.error('KaTeX error in \\(...\\):', e)
        return tex
      }
    });
    
    // 处理块级公式 ($$...$$)
    processedContent = processedContent.replace(/\$\$([\s\S]*?)\$\$/g, (_, tex) => {
      try {
        return katex.renderToString(tex, { displayMode: true })
      } catch (e) {
        DEBUG && console.error('KaTeX error in $$...$$:', e)
        return tex
      }
    })
    
    // 处理内联公式 ($...$)
    processedContent = processedContent.replace(/\$([^\$]+)\$/g, (_, tex) => {
      try {
        return katex.renderToString(tex, { displayMode: false })
      } catch (e) {
        DEBUG && console.error('KaTeX error in $...$:', e)
        return tex
      }
    })

    return marked(processedContent)
  }

  const handleDeleteResponse = (data: any) => {
    if (data.success) {
      if (selectedHistory === data.historyId) {
        setMessages([])
        setSelectedHistory(null)
      }
      
      toast({
        title: "Chat history deleted",
        description: "The conversation has been permanently removed.",
        variant: "default",
        duration: 5000,
        className: "bg-white text-gray-900 border-gray-200",
      })
    } else {
      toast({
        title: "Error",
        description: "Failed to delete chat history. Please try again.",
        variant: "destructive",
        duration: 5000,
        className: "bg-red-50 text-red-900 border-red-200",
        action: <ToastAction altText="Try again" className="text-red-900">Try again</ToastAction>,
      })
    }
  }

  const deleteHistory = (historyId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (!ws) return
    
    toast({
      title: "Delete chat history?",
      description: "This action cannot be undone.",
      duration: 5000,
      className: "bg-white text-gray-900 border-gray-200",
      action: (
        <ToastAction 
          altText="Delete" 
          className="text-red-900 hover:text-red-700"
          onClick={() => {
            ws.send(JSON.stringify({
              type: 'delete_history',
              historyId
            }))
          }}
        >
          Delete
        </ToastAction>
      ),
      variant: "destructive",
    })
  }

  const toggleShowMore = () => {
    setShowAllHistories(prev => !prev)
  }

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp)
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } catch (e) {
      return ''
    }
  }

  const renderMessage = (message: Message) => {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
          <span>{message.role === 'user' ? 'You' : 'Assistant'}</span>
          <span>{formatTimestamp(message.timestamp)}</span>
        </div>
        
        {message.images && message.images.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-2">
            {message.images.map((image, index) => (
              <div key={index} className="relative group">
                <img
                  src={`data:image/png;base64,${image}`}
                  alt={`Image ${index + 1}`}
                  className="max-w-[200px] rounded-lg hover:opacity-90 transition-opacity cursor-pointer"
                  onClick={() => {
                    window.open(`data:image/png;base64,${image}`, '_blank')
                  }}
                />
              </div>
            ))}
          </div>
        )}
        <div
          dangerouslySetInnerHTML={{
            __html: renderContent(message.content)
          }}
          className="prose max-w-none"
        />
      </div>
    )
  }

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  // Add useEffect to handle initial state based on screen size
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      setIsSidebarOpen(!mobile)
    }
    
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const getHistoryLimit = () => {
    return showAllHistories ? undefined : (isMobile ? 12 : 5)
  }

  const handlePaste = (e: React.ClipboardEvent) => {
    const items = Array.from(e.clipboardData.items);
    for (const item of items) {
      if (item.type.startsWith('image')) {
        const file = item.getAsFile();
        if (file) {
          setSelectedImages(prev => [...prev, file]);
        }
        break;
      }
    }
  };

  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      const adjustHeight = () => {
        textarea.style.height = 'auto'
        textarea.style.height = `${textarea.scrollHeight}px`
      }
      adjustHeight()
      textarea.addEventListener('input', adjustHeight)
      return () => textarea.removeEventListener('input', adjustHeight)
    }
  }, [input])

  // Add a useEffect to monitor isThinking changes
  useEffect(() => {
    DEBUG && console.log('%cisThinking changed to:', 'color: red; font-weight: bold', isThinking)
  }, [isThinking])

  // Load user prompts and selected prompt from localStorage
  useEffect(() => {
    try {
      // Load prompts
      const savedPrompts = localStorage.getItem('userPrompts')
      if (savedPrompts) {
        const parsedPrompts = JSON.parse(savedPrompts) as UserPrompt[]
        if (Array.isArray(parsedPrompts) && parsedPrompts.every(p => p.id && p.name && p.content)) {
          setUserPrompts(parsedPrompts)
          DEBUG && console.log('Loaded prompts:', parsedPrompts)
        }
      }

      // Load selected prompt ID
      const savedSelectedPromptId = localStorage.getItem('selectedPromptId')
      if (savedSelectedPromptId) {
        setSelectedPromptId(savedSelectedPromptId)
        DEBUG && console.log('Loaded selected prompt ID:', savedSelectedPromptId)
      }
    } catch (error) {
      DEBUG && console.error('Error loading prompts from localStorage:', error)
      // 如果出错，清除可能损坏的数据
      localStorage.removeItem('userPrompts')
      localStorage.removeItem('selectedPromptId')
    }
  }, [])

  // Save prompts to localStorage whenever they change
  useEffect(() => {
    try {
      if (userPrompts.length > 0) {
        localStorage.setItem('userPrompts', JSON.stringify(userPrompts))
        DEBUG && console.log('Saved prompts:', userPrompts)
      } else {
        localStorage.removeItem('userPrompts')
      }
    } catch (error) {
      DEBUG && console.error('Error saving prompts to localStorage:', error)
    }
  }, [userPrompts])

  // Save selected prompt ID to localStorage whenever it changes
  useEffect(() => {
    try {
      if (selectedPromptId) {
        localStorage.setItem('selectedPromptId', selectedPromptId)
        DEBUG && console.log('Saved selected prompt ID:', selectedPromptId)
      } else {
        localStorage.removeItem('selectedPromptId')
      }
    } catch (error) {
      DEBUG && console.error('Error saving selected prompt ID to localStorage:', error)
    }
  }, [selectedPromptId])

  const handleAddPrompt = () => {
    if (!newPromptName.trim() || !newPromptContent.trim()) return

    const newPrompt: UserPrompt = {
      id: Date.now().toString(),
      name: newPromptName.trim(),
      content: newPromptContent.trim()
    }

    try {
      setUserPrompts(prev => {
        const updatedPrompts = [...prev, newPrompt]
        localStorage.setItem('userPrompts', JSON.stringify(updatedPrompts))
        return updatedPrompts
      })
      setNewPromptName('')
      setNewPromptContent('')
      
      toast({
        title: "Prompt added",
        description: "Your new prompt has been saved.",
        variant: "default",
      })
    } catch (error) {
      DEBUG && console.error('Error adding prompt:', error)
      toast({
        title: "Error",
        description: "Failed to save prompt. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeletePrompt = (id: string) => {
    try {
      setUserPrompts(prev => {
        const updatedPrompts = prev.filter(prompt => prompt.id !== id)
        localStorage.setItem('userPrompts', JSON.stringify(updatedPrompts))
        return updatedPrompts
      })
      
      if (selectedPromptId === id) {
        setSelectedPromptId(null)
        localStorage.removeItem('selectedPromptId')
      }
      
      toast({
        title: "Prompt deleted",
        description: "The prompt has been removed.",
        variant: "default",
      })
    } catch (error) {
      DEBUG && console.error('Error deleting prompt:', error)
      toast({
        title: "Error",
        description: "Failed to delete prompt. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleSelectPrompt = (id: string | null) => {
    const newId = id === 'none' ? null : id
    setSelectedPromptId(newId)
  }

  // 添加 useEffect 来监听思考内容的变化，并自动滚动到底部
  useEffect(() => {
    if (thinkingContent && thinkingContentRef.current) {
      thinkingContentRef.current.scrollTop = thinkingContentRef.current.scrollHeight;
    }
  }, [thinkingContent]);

  // 添加 useEffect 来监听消息变化，对每个消息中的思考内容进行自动滚动
  useEffect(() => {
    // 对每个包含思考内容的消息容器执行滚动
    Object.values(thinkingContainersRef.current).forEach(container => {
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    });
  }, [messages]);

  return (
    <div className="flex h-full relative">
      {/* Mobile Sidebar Toggle Button */}
      <button
        className="md:hidden fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-lg"
        onClick={toggleSidebar}
      >
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          {isSidebarOpen ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          )}
        </svg>
      </button>

      {/* Sidebar */}
      <div className={`
        md:w-64 w-80 border-r border-border/40 bg-card flex flex-col
        md:relative fixed
        transition-transform duration-300 ease-in-out
        md:translate-x-0
        ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        md:h-full h-[100dvh]
        top-0 left-0
        z-40
      `}>
        <div className="p-4 border-b border-border/40">
          <div className="font-semibold text-sm mb-2">Local LLM Chat</div>
          <Button 
            className="w-full bg-primary hover:bg-primary/90" 
            onClick={startNewChat}
          >
            New Chat
          </Button>
        </div>
        <ScrollArea 
          className="flex-1 md:h-[calc(100vh-8rem)] overflow-auto"
          style={{ 
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(155, 155, 155, 0.5) transparent'
          }}
        >
          <div className="p-2 space-y-2">
            {histories
              .slice(0, getHistoryLimit())
              .map(history => (
                <div
                  key={history.id}
                  className={`group flex items-center justify-between p-2 cursor-pointer rounded-lg hover:bg-green-50 ${
                    selectedHistory === history.id ? 'bg-yellow-50' : ''
                  }`}
                  onClick={() => loadChatHistory(history.id)}
                >
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{history.title}</div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(history.timestamp).toLocaleDateString()}
                    </div>
                  </div>
                  <button
                    onClick={(e) => deleteHistory(history.id, e)}
                    className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-100 rounded transition-opacity"
                    title="Delete conversation"
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </button>
                </div>
            ))}
            
            {histories.length > (isMobile ? 12 : 5) && (
              <button
                onClick={toggleShowMore}
                className="w-full py-2 px-4 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              >
                {showAllHistories ? 'Show Less' : `Show ${histories.length - (isMobile ? 12 : 5)} More`}
              </button>
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/20 z-30 md:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col h-full relative">
        <div className="sticky top-0 left-0 right-0 p-4 bg-white border-b border-gray-200 shadow-sm z-10">
          <div className="flex items-center justify-between">
            <div className="font-semibold text-lg text-gray-800 ml-12 md:ml-0">
              {getTitle()}
            </div>
            <div className="flex items-center gap-2">
              {selectedPromptId && (
                <div className="text-sm text-gray-600">
                  Using prompt: {userPrompts.find(p => p.id === selectedPromptId)?.name}
                </div>
              )}
              <Button
                variant="outline"
                size="icon"
                onClick={() => setIsSettingsOpen(true)}
                className="hover:bg-gray-100"
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Settings Dialog */}
        <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <DialogContent className="sm:max-w-[500px] p-4 flex flex-col">
            <div className="absolute right-4 top-4">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => setIsSettingsOpen(false)}
                className="hover:bg-gray-100 rounded-full h-8 w-8 p-0"
              >
                <svg
                  width="15"
                  height="15"
                  viewBox="0 0 15 15"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                >
                  <path
                    d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"
                    fill="currentColor"
                    fillRule="evenodd"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
            
            <DialogHeader className="mb-2">
              <DialogTitle>System Prompts</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-2 flex-1">
              <div className="flex items-center justify-between">
                <Label>Select a prompt</Label>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    setSelectedPromptId(null)
                    setNewPromptName('')
                    setNewPromptContent('')
                    setIsAddingNew(true)
                  }}
                  className="text-blue-600 hover:text-blue-700"
                >
                  + Add New
                </Button>
              </div>

              <Select 
                value={selectedPromptId || undefined} 
                onValueChange={(value) => {
                  handleSelectPrompt(value)
                  setIsAddingNew(false)
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a prompt" />
                </SelectTrigger>
                <SelectContent>
                  {userPrompts.map(prompt => (
                    <div key={prompt.id} className="relative group">
                      <SelectItem value={prompt.id} className="pr-8">
                        {prompt.name}
                      </SelectItem>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          handleDeletePrompt(prompt.id)
                        }}
                        className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 hover:bg-red-100 h-6 w-6 p-0 rounded-sm"
                      >
                        <Trash2 className="h-3 w-3 text-red-500" />
                      </Button>
                    </div>
                  ))}
                </SelectContent>
              </Select>

              {isAddingNew ? (
                <div className="space-y-2">
                  <Label>System Prompt:</Label>
                  <Input
                    placeholder="Prompt Name"
                    value={newPromptName}
                    onChange={(e) => setNewPromptName(e.target.value)}
                    className="mb-2"
                  />
                  <Textarea
                    placeholder="Prompt Content"
                    value={newPromptContent}
                    onChange={(e) => setNewPromptContent(e.target.value)}
                    rows={4}
                    className="min-h-[100px]"
                  />
                  <Button onClick={handleAddPrompt} className="w-full mt-2">
                    Add Prompt
                  </Button>
                </div>
              ) : selectedPromptId && (
                <div className="space-y-2">
                  <Label>System Prompt:</Label>
                  <Textarea
                    value={userPrompts.find(p => p.id === selectedPromptId)?.content || ''}
                    onChange={(e) => {
                      setUserPrompts(prev => prev.map(p => 
                        p.id === selectedPromptId 
                          ? { ...p, content: e.target.value }
                          : p
                      ))
                    }}
                    rows={4}
                    className="min-h-[100px]"
                  />
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>

        <Card className="flex-1 mx-2 md:mx-4 my-2">
          <CardContent className="h-full flex flex-col pt-6">
            <ScrollArea ref={scrollRef} className="flex-1 pr-4">
              <div className="space-y-4">
                {messages.map((message, index) => (
                  <div key={index} className={`flex items-start gap-3 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      message.role === 'user' ? 'bg-blue-100' : 'bg-amber-100'
                    }`}>
                      {message.role === 'user' ? (
                        <User className="w-5 h-5 text-blue-600" />
                      ) : (
                        <Bot className="w-5 h-5 text-amber-700" />
                      )}
                    </div>
                    <div className={`relative max-w-[80%] rounded-lg p-3 ${
                      message.role === 'user' ? 'bg-blue-600/90 text-white shadow-sm' : 'bg-green-50 text-gray-800'
                    }`}>
                      <div className="space-y-2">
                        <div className={`flex items-center justify-between text-xs ${
                          message.role === 'user' ? 'text-white/90' : ''
                        } mb-1`}>
                          <span className={message.role === 'user' ? '' : 'text-amber-700 font-medium'}>
                            {message.role === 'user' ? 'You' : 'Assistant'}
                          </span>
                          <span className={message.role === 'user' ? '' : 'text-gray-500'}>
                            {formatTimestamp(message.timestamp)}
                          </span>
                        </div>
                        
                        {/* 图片显示部分 */}
                        {message.images && message.images.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-2">
                            {message.images.map((image, index) => (
                              <div key={index} className="relative group">
                                <img
                                  src={`data:image/png;base64,${image}`}
                                  alt={`Image ${index + 1}`}
                                  className="max-w-[200px] rounded-lg hover:opacity-90 transition-opacity cursor-pointer border border-white/20"
                                  onClick={() => {
                                    window.open(`data:image/png;base64,${image}`, '_blank')
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Reasoning 显示部分 */}
                        {message.role === 'assistant' && message.content.includes('<think>') && (
                          <div className="bg-amber-50 rounded-lg border border-amber-200 shadow-sm mb-4">
                            <details className="rounded p-2" open>
                              <summary className="cursor-pointer font-medium text-amber-700 flex items-center gap-2 text-xs">
                                <svg className="w-3 h-3 transition-transform duration-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                                Assistant's Reasoning
                              </summary>
                              <div 
                                ref={el => { thinkingContainersRef.current[index] = el; }}
                                className="mt-2 text-gray-700 max-h-40 overflow-y-auto font-mono whitespace-pre-wrap text-xs"
                              >
                                {message.content.match(/<think>([^]*?)<\/think>/)?.[1] || ''}
                              </div>
                            </details>
                          </div>
                        )}

                        {/* 内容显示部分 */}
                        <div
                          dangerouslySetInnerHTML={{
                            __html: renderContent(
                              message.role === 'assistant' 
                                ? message.content.replace(/<think>[^]*?<\/think>/, '')
                                : message.content
                            )
                          }}
                          className={`prose max-w-none ${
                            message.role === 'user' ? 'prose-invert prose-p:text-white/90' : ''
                          }`}
                        />
                      </div>
                    </div>
                  </div>
                ))}
                {isThinking && (
                  <div className="flex items-center gap-2 px-1">
                    <span className="text-xs font-medium bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-green-500 to-red-500 animate-gradient-x" style={{
                      backgroundSize: '300% 100%',
                      animation: 'gradient 3s linear infinite'
                    }}>Thinking</span>
                    <div className="flex gap-1">
                      <div className="w-1 h-1 bg-gradient-to-r from-blue-500 via-green-500 to-red-500 rounded-full animate-bounce [animation-delay:-0.45s]" />
                      <div className="w-1 h-1 bg-gradient-to-r from-blue-500 via-green-500 to-red-500 rounded-full animate-bounce [animation-delay:-0.3s]" />
                      <div className="w-1 h-1 bg-gradient-to-r from-blue-500 via-green-500 to-red-500 rounded-full animate-bounce [animation-delay:-0.15s]" />
                      <div className="w-1 h-1 bg-gradient-to-r from-blue-500 via-green-500 to-red-500 rounded-full animate-bounce" />
                      <div className="w-1 h-1 bg-gradient-to-r from-blue-500 via-green-500 to-red-500 rounded-full animate-bounce [animation-delay:0.15s]" />
                    </div>
                  </div>
                )}
                {thinkingContent && (
                  <div className="bg-amber-50 rounded-lg border border-amber-200 shadow-sm">
                    <details className="rounded p-2" open>
                      <summary className="cursor-pointer font-medium text-amber-700 flex items-center gap-2 text-sm">
                        <svg className="w-4 h-4 transition-transform duration-200" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        Assistant's Reasoning
                      </summary>
                      <div 
                        ref={thinkingContentRef}
                        className="mt-2 text-gray-700 max-h-40 overflow-y-auto font-mono whitespace-pre-wrap text-sm"
                      >
                        {thinkingContent}
                      </div>
                    </details>
                  </div>
                )}
                {!isThinking && streamContent && (
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div
                      dangerouslySetInnerHTML={{
                        __html: renderContent(streamContent)
                      }}
                      className="text-gray-800 whitespace-pre-wrap"
                    />
                  </div>
                )}
              </div>
            </ScrollArea>
            
            {selectedImages.length > 0 && (
              <div className="flex flex-wrap gap-2 p-2 border-t">
                {selectedImages.map((file, index) => (
                  <div key={index} className="relative">
                    <img
                      src={URL.createObjectURL(file)}
                      alt={`Selected ${index + 1}`}
                      className="h-16 w-16 object-cover rounded"
                    />
                    <button
                      onClick={() => removeImage(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
            
            <div className="flex items-center gap-2 pt-4">
              <input
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                ref={fileInputRef}
                onChange={handleImageSelect}
              />
              <Button
                onClick={() => fileInputRef.current?.click()}
                variant="outline"
                size="icon"
                className="flex-shrink-0"
              >
                <ImageIcon className="h-4 w-4" />
              </Button>
              <Textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onPaste={handlePaste}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    void sendMessage();
                  }
                }}
                placeholder="Type a message..."
                className="flex-grow min-h-[44px] max-h-[200px] resize-none py-3 px-4"
                rows={1}
                style={{
                  height: 'auto',
                  overflow: 'hidden'
                }}
                disabled={!ws}
              />
              <Button 
                onClick={() => void sendMessage()}
                disabled={(!input.trim() && selectedImages.length === 0) || !ws}
                className="bg-green-500 hover:bg-green-600"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default LocalChat