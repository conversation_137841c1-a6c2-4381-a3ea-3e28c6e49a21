'use client'

import { useEffect, useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { fetchEnvConfig } from '@/lib/api'
import { motion, AnimatePresence } from 'framer-motion'
import { Sparkles, Wand2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function FluxplorerInterface() {
  const [artStyles, setArtStyles] = useState<string[]>([])
  const [things, setThings] = useState<string[]>([])
  const [prompt, setPrompt] = useState('')
  const [aspect, setAspect] = useState('square')
  const [model, setModel] = useState('black-forest-labs/FLUX.1-schnell')
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState('')
  const [generatedImages, setGeneratedImages] = useState<any[]>([])
  const [backendUrl, setBackendUrl] = useState('')
  const [mode, setMode] = useState<'image' | 'video'>('image')
  const [videoUrl, setVideoUrl] = useState<string>('')
  const [isVideoGenerating, setIsVideoGenerating] = useState(false)
  const [generatedVideos, setGeneratedVideos] = useState<Array<{url: string, prompt: string, timestamp: number}>>([])
  const { toast } = useToast()

  useEffect(() => {
    const loadConfig = async () => {
      try {
        const config = await fetchEnvConfig()
        console.log('Config loaded:', config)
        setBackendUrl(config.backendUrl)
        await loadInitialItems(config.backendUrl)
      } catch (error) {
        console.error('Error loading config:', error)
      }
    }
    loadConfig()
  }, [])

  const loadInitialItems = async (url: string) => {
    try {
      console.log('Loading items from:', url)
      const artStylesResponse = await fetch(`${url}/fluxplorer/load-more-items?start=0&category=art_styles`)
      const thingsResponse = await fetch(`${url}/fluxplorer/load-more-items?start=0&category=things`)
      
      if (!artStylesResponse.ok || !thingsResponse.ok) {
        throw new Error(`HTTP error! status: ${artStylesResponse.status}`)
      }

      const artStylesData = await artStylesResponse.json()
      const thingsData = await thingsResponse.json()
      
      console.log('Loaded art styles:', artStylesData)
      console.log('Loaded things:', thingsData)
      
      setArtStyles(artStylesData.items)
      setThings(thingsData.items)
    } catch (error) {
      console.error('Error loading initial items:', error)
      setError('Failed to load items')
    }
  }

  const handleKeywordClick = (keyword: string) => {
    setPrompt(prev => prev ? `${prev}, ${keyword}` : keyword)
  }

  const handleGenerate = async () => {
    if (mode === 'image') {
      await generateImage()
    } else {
      await generateVideo()
    }
  }

  const generateVideo = async () => {
    try {
      setIsVideoGenerating(true)
      // Convert aspect ratio format for video
      const aspectMap: Record<string, string> = {
        'square': '1:1',
        'landscape': '16:9',
        'portrait': '9:16'
      }
      const videoAspect = aspectMap[aspect as string] || '16:9'
      
      const response = await fetch(`${backendUrl}/fluxplorer/generate-video`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: prompt,
          aspect: videoAspect,
          cfg: 3,
          steps: 30,
          length: 97,
          target_size: 640
        })
      })
      
      const data = await response.json()
      if (data.error) throw new Error(data.error)
      
      setGeneratedVideos(prev => [{
        url: data.url,
        prompt: data.prompt,
        timestamp: data.timestamp
      }, ...prev])
      
      toast({
        title: "Success",
        description: "Video generated successfully!",
        variant: "default",
      })
    } catch (err: unknown) {
      const error = err as Error
      toast({
        title: "Error",
        description: error.message || "Failed to generate video",
        variant: "destructive",
      })
    } finally {
      setIsVideoGenerating(false)
    }
  }

  const generateImage = async () => {
    if (!prompt.trim() || !backendUrl) return
    
    setIsGenerating(true)
    setError('')

    try {
      console.log('Generating image with URL:', backendUrl)
      const response = await fetch(`${backendUrl}/fluxplorer/generate-image`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, aspect, model })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      console.log('Generated image data:', data)
      
      if (data.error) throw new Error(data.error)

      setGeneratedImages(prev => [data, ...prev].slice(0, 36))
    } catch (error) {
      console.error('Error generating image:', error)
      setError('An error occurred while generating the image.')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="space-y-6 p-4 bg-black/40 rounded-xl backdrop-blur-sm border border-white/10">
      {/* Art Styles Section */}
      <div className="space-y-4">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg"
        >
          <h3 className="text-lg font-semibold text-white/90 mb-3 flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-400" />
            Art Styles
          </h3>
          <div className="flex flex-wrap gap-2">
            <AnimatePresence>
              {artStyles.map((style, i) => (
                <motion.div
                  key={`style-${i}`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ delay: i * 0.02 }}
                >
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleKeywordClick(style)}
                    className="text-xs bg-white/10 hover:bg-white/20 text-white/90 border-purple-500/50 hover:border-purple-400 transition-all duration-300 hover:scale-105"
                  >
                    {style}
                  </Button>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </motion.div>

        {/* Things Section */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="p-4 bg-gradient-to-r from-blue-900/50 to-cyan-900/50 rounded-lg"
        >
          <h3 className="text-lg font-semibold text-white/90 mb-3 flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-400" />
            Things
          </h3>
          <div className="flex flex-wrap gap-2">
            <AnimatePresence>
              {things.map((thing, i) => (
                <motion.div
                  key={`thing-${i}`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ delay: i * 0.02 }}
                >
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleKeywordClick(thing)}
                    className="text-xs bg-white/10 hover:bg-white/20 text-white/90 border-blue-500/50 hover:border-blue-400 transition-all duration-300 hover:scale-105"
                  >
                    {thing}
                  </Button>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </motion.div>
      </div>

      {/* Prompt Input */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="relative"
      >
        <Textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Click buttons to add keywords to your prompt..."
          className="min-h-[100px] bg-black/20 border-white/10 text-white/90 resize-none"
        />

        {/* Control buttons group - aligned with right edge */}
        <div className="absolute top-2 right-2 flex gap-2">
          {/* Aspect Ratio Buttons */}
          <div className="flex gap-1 backdrop-blur-md bg-black/40 p-1 rounded-lg border border-white/10">
            <button
              onClick={() => setAspect('square')}
              className={`p-1.5 rounded transition-all duration-200 ${
                aspect === 'square' 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-transparent text-white/60 hover:bg-black/60'
              }`}
              title="Square (1:1)"
            >
              <div className="w-4 h-4 border-2 border-current" />
            </button>
            <button
              onClick={() => setAspect('landscape')}
              className={`p-1.5 rounded transition-all duration-200 ${
                aspect === 'landscape' 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-transparent text-white/60 hover:bg-black/60'
              }`}
              title={mode === 'image' ? 'Landscape (4:3)' : 'Landscape (16:9)'}
            >
              <div className="w-5 h-4 border-2 border-current" />
            </button>
            <button
              onClick={() => setAspect('portrait')}
              className={`p-1.5 rounded transition-all duration-200 ${
                aspect === 'portrait' 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-transparent text-white/60 hover:bg-black/60'
              }`}
              title={mode === 'image' ? 'Portrait (3:4)' : 'Portrait (9:16)'}
            >
              <div className="w-4 h-5 border-2 border-current" />
            </button>
          </div>

          {/* Mode Selection */}
          <div className="flex gap-1 backdrop-blur-md bg-black/40 p-1 rounded-lg border border-white/10">
            <button
              onClick={() => setMode('image')}
              className={`p-1.5 px-2 rounded transition-all duration-200 text-sm ${
                mode === 'image' 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-transparent text-white/60 hover:bg-black/60'
              }`}
            >
              Image
            </button>
            <button
              onClick={() => setMode('video')}
              className={`p-1.5 px-2 rounded transition-all duration-200 text-sm ${
                mode === 'video' 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-transparent text-white/60 hover:bg-black/60'
              }`}
            >
              Video
            </button>
          </div>
        </div>

        {/* Generate Button */}
        <motion.div 
          className="absolute bottom-2 right-2"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || isVideoGenerating || !prompt.trim()}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-500 hover:to-blue-500 text-white font-semibold transition-all duration-300"
          >
            {isGenerating || isVideoGenerating ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="mr-2"
                >
                  <Wand2 className="h-4 w-4" />
                </motion.div>
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4 mr-2" />
                Generate
              </>
            )}
          </Button>
        </motion.div>
      </motion.div>

      {error && (
        <Alert variant="destructive" className="bg-red-900/50 border-red-500/50">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Generated Content Grid */}
      <div className="grid grid-cols-3 gap-4">
        <AnimatePresence>
          {mode === 'image' ? (
            generatedImages.map((image, index) => (
              <motion.div 
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ delay: index * 0.1 }}
                className="relative bg-black/40 rounded-lg overflow-hidden border border-white/10 hover:border-purple-500/50 transition-all duration-300 hover:scale-105"
              >
                <img 
                  src={image.url} 
                  alt={image.prompt}
                  className="w-full h-auto"
                  loading="lazy"
                />
              </motion.div>
            ))
          ) : (
            generatedVideos.map((video, index) => (
              <motion.div
                key={`${video.url}-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
                className="relative group aspect-square bg-black/20 rounded-lg overflow-hidden border border-white/10"
              >
                <video 
                  controls
                  className="absolute inset-0 w-full h-full object-cover"
                >
                  <source src={video.url} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/80 to-transparent">
                  <p className="text-xs text-white/80 line-clamp-2">{video.prompt}</p>
                </div>
              </motion.div>
            ))
          )}
        </AnimatePresence>
      </div>
    </div>
  )
} 