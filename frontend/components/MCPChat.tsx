'use client'

import type { KeyboardEvent, ChangeEvent } from 'react'
import { useState, useEffect, useRef, useCallback } from 'react'
import { Card, CardContent } from './ui/card'
import { Input } from './ui/input'
import { Button } from './ui/button'
import { ScrollArea } from './ui/scroll-area'
import { Send, User, Bot, Trash2, Settings, Box, RefreshCw, ChevronDown, ChevronRight, TrendingUp } from 'lucide-react'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'
import 'katex/dist/katex.min.css'
import katex from 'katex'
import { fetchEnvConfig } from '../lib/api'
import { useToast } from "../hooks/use-toast"
import { ToastAction } from "./ui/toast"
import { Textarea } from './ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from './ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Label } from './ui/label'
import { Badge } from './ui/badge'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion'
import { Tabs, TabsList, TabsTrigger, TabsContent } from './ui/tabs'
import confetti from 'canvas-confetti'
import { Slider } from './ui/slider'
import React from 'react'
import MarkdownRenderer from './MarkdownRenderer'

interface Message {
  role: 'user' | 'assistant' | 'tool'
  content: string
  timestamp: string
  id?: string
  isThinking?: boolean
  isThinkingComplete?: boolean
  name?: string
  toolId?: string
  toolInput?: any
  thinkingContent?: string
  result?: {
    success: boolean
    content: string
    error: string
  }
  status?: string
}

interface ChatHistory {
  id: string
  title: string
  timestamp: string
}

interface MCPTool {
  name: string
  description: string
  parameters?: {
    type: string
    properties: Record<string, any>
    required?: string[]
  }
}

interface MCPToolsByServer {
  [serverId: string]: MCPTool[];
}

interface ServerInfo {
  connected: boolean;
  url: string;
  tools_count: number;
}

interface ServerStatus {
  [serverId: string]: ServerInfo;
}

interface MCPServerConfig {
  url: string;
}

interface MCPConfig {
  mcpServers: {
    [key: string]: MCPServerConfig;
  };
}

// Debug mode - set to true for debugging
const DEBUG = false

// Add this near the top of the file, after other imports
const styles = {
  thinkingBubble: 'animate-pulse bg-opacity-60',
  toolBubble: 'bg-blue-100 border border-blue-300',
  toolName: 'font-bold text-blue-700 mb-1',
  toolContent: 'font-mono text-xs overflow-x-auto whitespace-pre',
  toolResultContainer: 'mt-2 border border-green-200 rounded overflow-hidden',
  toolErrorContainer: 'mt-2 border border-red-200 rounded overflow-hidden',
  toolResultHeader: 'p-2 bg-green-50 flex justify-between items-center cursor-pointer',
  toolErrorHeader: 'p-2 bg-red-50 flex justify-between items-center cursor-pointer',
  toolResultTitle: 'font-medium text-green-700',
  toolErrorTitle: 'font-medium text-red-700',
  toolResultContent: 'p-2 bg-white border-t border-gray-200 font-mono text-xs whitespace-pre-wrap overflow-x-auto max-h-64 overflow-y-auto'
}

// Global thinking data capture for debugging - independent of React state
let globalThinkingData = '';

// Create a custom hook to handle the Thinking element removal
const useHideThinkingElements = () => {
  useEffect(() => {
    // Find and hide elements directly containing "Thinking ***" text
    const hideElements = () => {
      // This specific selector aims at the thinking elements in the screenshot
      const thinkingElements = Array.from(document.querySelectorAll('div'))
        .filter(el => {
          // Match exactly elements with just "Thinking ***" text and nothing else
          return el.childNodes.length === 1 && 
                 el.childNodes[0].nodeType === Node.TEXT_NODE && 
                 el.textContent?.trim() === "Thinking ***";
        });
      
      // Hide these elements completely
      thinkingElements.forEach(el => {
        el.style.display = 'none';
        // Also hide parent elements if they only contain this text
        let parent = el.parentElement;
        if (parent && parent.childNodes.length === 1) {
          parent.style.display = 'none';
        }
      });
    };
    
    // Run immediately
    hideElements();
    
    // Create a mutation observer to catch any newly added elements
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          hideElements();
        }
      }
    });
    
    // Start observing the entire document
    observer.observe(document.body, { 
      childList: true, 
      subtree: true 
    });
    
    // Clean up
    return () => observer.disconnect();
  }, []);
};

// Dramatic thinking animation that will replace the existing ones
const ThinkingAnimation = ({ isActive }: { isActive: boolean }) => {
  if (!isActive) return null;
  
  return (
    <div className="fixed inset-0 z-40 pointer-events-none flex items-center justify-center">
      <div className="absolute bottom-28 right-28 bg-gradient-to-r from-amber-500/90 to-rose-500/90 rounded-full p-1 shadow-lg animate-pulse">
        <div className="bg-white rounded-full p-3 flex items-center space-x-2">
          <div className="w-8 h-8 rounded-full bg-emerald-500 flex items-center justify-center overflow-hidden">
            <Bot className="w-5 h-5 text-white" />
          </div>
          <div className="text-gray-800 font-medium flex items-center">
            <span>AI Thinking</span>
            <div className="flex ml-2">
              <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce [animation-delay:-0.3s] mx-0.5" />
              <div className="w-2 h-2 bg-amber-500 rounded-full animate-bounce [animation-delay:-0.15s] mx-0.5" />
              <div className="w-2 h-2 bg-amber-600 rounded-full animate-bounce mx-0.5" />
            </div>
          </div>
        </div>
      </div>
      
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-amber-500 via-rose-500 to-purple-500 animate-gradient-x"></div>
    </div>
  );
};

export default function MCPChat() {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState('')
  const [ws, setWs] = useState<WebSocket | null>(null)
  const [streamContent, setStreamContent] = useState('')
  const [histories, setHistories] = useState<ChatHistory[]>([])
  const [selectedHistory, setSelectedHistory] = useState<string | null>(null)
  const [backendUrl, setBackendUrl] = useState('')
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [tools, setTools] = useState<MCPTool[]>([])
  const [toolsByServer, setToolsByServer] = useState<MCPToolsByServer>({})
  const [serverStatus, setServerStatus] = useState<ServerStatus>({})
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [dialogPosition, setDialogPosition] = useState({ top: 0, right: 0 })
  const [model, setModel] = useState('claude-3-7-sonnet-20250219')
  const [apiKey, setApiKey] = useState('')
  const [systemPrompt, setSystemPrompt] = useState('')
  const [maxIterations, setMaxIterations] = useState(20) // Add state for max_iterations
  const scrollRef = useRef<HTMLDivElement>(null)
  const settingsButtonRef = useRef<HTMLButtonElement>(null)
  const { toast } = useToast()
  const [showAllHistories, setShowAllHistories] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [isThinking, setIsThinking] = useState(false)
  const [thinkingContent, setThinkingContent] = useState('')
  const isThinkingRef = useRef(false)
  const contentBufferRef = useRef('')
  const [isConnecting, setIsConnecting] = useState(false)
  const [expandedServers, setExpandedServers] = useState<Record<string, boolean>>({})
  const [isLoadingTools, setIsLoadingTools] = useState(false)
  const [mcpConfig, setMcpConfig] = useState<MCPConfig | null>(null)
  const [isConfigLoading, setIsConfigLoading] = useState(false)
  const [configError, setConfigError] = useState<string | null>(null)
  const [isConfigChanged, setIsConfigChanged] = useState(false)
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false)
  const [serverToDelete, setServerToDelete] = useState<string | null>(null)
  const [newServerName, setNewServerName] = useState('')
  const [newServerUrl, setNewServerUrl] = useState('')
  const [isAddingServer, setIsAddingServer] = useState(false)
  const [isEditingServer, setIsEditingServer] = useState<string | null>(null)
  const [editServerUrl, setEditServerUrl] = useState('')
  const [currentThinking, setCurrentThinking] = useState<string>('')
  const [currentAssistantMessageId, setCurrentAssistantMessageId] = useState<string | null>(null)
  const [currentToolUseId, setCurrentToolUseId] = useState<string | null>(null)
  const [messageCounter, setMessageCounter] = useState<number>(0)
  const [toolResultMap, setToolResultMap] = useState<Record<string, string>>({})
  const hasShownThinkingToastRef = useRef(false)
  // Add a state for expanded tool messages
  const [expandedTools, setExpandedTools] = useState<Record<string, boolean>>({});
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [lastThinkingIndex, setLastThinkingIndex] = useState<number | null>(null);

  useEffect(() => {
    const loadConfig = async () => {
      try {
        const envConfig = await fetchEnvConfig()
        DEBUG && console.log('Env config:', envConfig)
        setBackendUrl(envConfig.backendUrl)
        
        // Try to load API key from localStorage
        const savedApiKey = localStorage.getItem('mcpApiKey')
        if (savedApiKey) {
          setApiKey(savedApiKey)
        }
        
        // Try to load model from localStorage
        const savedModel = localStorage.getItem('mcpModel')
        if (savedModel) {
          setModel(savedModel)
        }

        // Try to load max iterations from localStorage
        const savedMaxIterations = localStorage.getItem('mcpMaxIterations')
        if (savedMaxIterations) {
          setMaxIterations(parseInt(savedMaxIterations, 10))
        }
      } catch (error) {
        DEBUG && console.error('Error loading config:', error)
      }
    }
    void loadConfig()
  }, [])

  useEffect(() => {
    if (!backendUrl) return

    const wsUrl = `${backendUrl.replace(/^http/, 'ws')}/ws/mcp-chat`
    const websocket = new WebSocket(wsUrl)

    websocket.onopen = () => {
      DEBUG && console.log('WebSocket connected')
      websocket.send(JSON.stringify({ type: 'get_histories' }))
    }

    // Replace the existing onmessage with the handleMessage function
    websocket.onmessage = handleMessage

    websocket.onerror = (error) => {
      DEBUG && console.error('WebSocket error:', error)
    }

    websocket.onclose = () => {
      DEBUG && console.log('WebSocket disconnected')
    }

    setWs(websocket)

    return () => {
      websocket.close()
    }
  }, [backendUrl])

  const handleInitializeResponse = (data: any) => {
    if (data.success) {
      const sessionId = data.sessionId
      setSessionId(sessionId)
      setTools(data.tools || [])
      // Load the system prompt if provided
      if (data.system_prompt !== undefined) {
        setSystemPrompt(data.system_prompt)
      }
      
      // Load max iterations if provided
      if (data.max_iterations !== undefined) {
        setMaxIterations(data.max_iterations)
      }
      
      // Disable connect button
      setIsConnecting(false)
      
      // Show confetti on successful connection
      createConfetti()
      
      // Auto-focus text input
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus()
        }
      }, 100)
      
      // Toast notification
      toast({
        title: "Connected to Agentic AI",
        description: "You are now connected and can start chatting with AI agents.",
        duration: 5000
      })
      
      // Close settings dialog
      setIsSettingsOpen(false)
      
      // Request status to get server info
      requestStatus()
    } else {
      toast({
        title: "Connection Failed",
        description: "Failed to connect to Agentic AI. Please check your settings.",
        variant: "destructive",
        duration: 5000,
      })
    }
  }

  const handleStatusUpdate = (data: any) => {
    setIsLoadingTools(false)
    
    if (data.status === "ok") {
      console.log("Received status update:", data)
      
      if (data.tools_by_server && Object.keys(data.tools_by_server).length > 0) {
        console.log(`Tools received from server: ${Object.entries(data.tools_by_server).map(([server, tools]) => `${server}:${(tools as any[]).length}`).join(', ')}`)
        
        try {
          // Validate and sanitize tools data before setting state
          const sanitizedToolsByServer: MCPToolsByServer = {};
          
          // Loop through each server and its tools with proper typing
          for (const [serverId, serverToolsData] of Object.entries(data.tools_by_server)) {
            if (!Array.isArray(serverToolsData)) {
              console.warn(`Tools for server ${serverId} is not an array`, serverToolsData);
              continue;
            }
            
            // Ensure we capture all tools from the server
            sanitizedToolsByServer[serverId] = serverToolsData.map(tool => ({
              name: tool.name || 'Unnamed Tool',
              description: tool.description || 'No description available',
              parameters: tool.parameters
            }));
            
            console.log(`Processed ${sanitizedToolsByServer[serverId].length} tools for server ${serverId}`);
          }
          
          setToolsByServer(sanitizedToolsByServer);
          
          // Check if servers status changed and any newly connected
          if (data.servers) {
            const newServerStatus = { ...serverStatus };
            let showConfetti = false;
            
            for (const serverId in data.servers) {
              if (data.servers[serverId].connected && 
                  (!serverStatus[serverId] || !serverStatus[serverId].connected)) {
                // Server just connected, we'll trigger confetti
                showConfetti = true;
              }
              newServerStatus[serverId] = data.servers[serverId];
            }
            
            setServerStatus(newServerStatus);
            
            // Show confetti if any server newly connected
            if (showConfetti) {
              createConfetti();
            }
          }
          
          // For backward compatibility, also update flat tools list
          const allTools: MCPTool[] = [];
          Object.values(sanitizedToolsByServer).forEach((serverTools: any) => {
            allTools.push(...serverTools);
          });
          
          if (allTools.length > 0) {
            setTools(allTools);
            console.log(`Successfully processed ${allTools.length} total tools`);
          }
        } catch (err) {
          console.error("Error processing tools data:", err);
          toast({
            title: "Error processing tools",
            description: "There was an error processing the tools data",
            variant: "destructive",
            duration: 3000,
          });
        }
      } else {
        console.warn("Received empty tools list in status update");
      }
    } else {
      console.error("Status update error:", data.message);
      toast({
        title: "Error loading tools",
        description: data.message || "Failed to load available tools",
        variant: "destructive",
        duration: 3000,
      });
    }
  }

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight
    }
  }, [messages, streamContent])

  const sendMessage = async () => {
    if (!input.trim() || !ws || !sessionId) return
    
    // Add the message to the chat
    const userMessage: Message = {
      role: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString()
    }
    
    // Add to messages state
    setMessages(prev => [...prev, userMessage])
    
    // Clear input
    setInput('')
    
    // Reset textarea height properly
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.overflowY = 'hidden'
    }
    
    // Log historyId state for debugging
    if (DEBUG) {
      console.log('Sending message with conversation state:', { 
        sessionId, 
        historyId: selectedHistory,
        messageCount: messages.length
      })
    }
    
    try {
      // Send message to server with system prompt if available
      const messagePayload: any = {
        type: 'message',
        content: input.trim(),
        sessionId,
        historyId: selectedHistory // This is critical for conversation continuity
      }
      
      // Add system prompt if it exists
      if (systemPrompt.trim()) {
        messagePayload.systemPrompt = systemPrompt.trim()
      }
      
      // Log exact payload being sent for debugging
      if (DEBUG) {
        console.log('Sending message payload:', JSON.stringify(messagePayload))
      }
      
      ws.send(JSON.stringify(messagePayload))
    } catch (error) {
      console.error('Error sending message:', error)
      setConfigError('Failed to send message to server')
    }
  }

  const connectToMCP = () => {
    if (!ws) return
    
    // Save API key and model to localStorage
    if (apiKey) {
      localStorage.setItem('mcpApiKey', apiKey)
    }
    if (model) {
      localStorage.setItem('mcpModel', model)
    }
    // Save max iterations to localStorage
    localStorage.setItem('mcpMaxIterations', maxIterations.toString())
    
    setIsConnecting(true)
    
    try {
      const initData = {
        type: 'initialize',
        config: {
          api_key: apiKey,
          model: model,
          max_iterations: maxIterations
        }
      }
      ws.send(JSON.stringify(initData))
    } catch (error) {
      setIsConnecting(false)
      DEBUG && console.error('Error initializing Agentic AI:', error)
      toast({
        title: "Connection Error",
        description: "Failed to connect to Agentic AI server.",
        variant: "destructive",
        duration: 5000
      })
    }
  }

  const startNewChat = () => {
    setMessages([])
    setSelectedHistory(null)
    setThinkingContent('') // Clear thinking content
    hasShownThinkingToastRef.current = false // Reset the toast flag
  }

  const loadChatHistory = (historyId: string) => {
    if (!ws) return
    setSelectedHistory(historyId)
    setMessages([])
    ws.send(JSON.stringify({
      type: 'load_history',
      historyId
    }))
  }

  const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value)
  }

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      void sendMessage()
    }
    // Shift+Enter will create a new line (default textarea behavior)
  }

  const renderContent = (content: string) => {
    return <MarkdownRenderer content={content} />;
  }

  const handleDeleteResponse = (data: any) => {
    if (data.success) {
      if (selectedHistory === data.historyId) {
        setMessages([])
        setSelectedHistory(null)
      }
      
      // Also remove the deleted history from local state to ensure UI is updated
      setHistories(prev => prev.filter(h => h.id !== data.historyId))
      
      toast({
        title: "Chat history deleted",
        description: "The conversation has been permanently removed.",
        variant: "default",
        duration: 5000,
        className: "bg-white text-gray-900 border-gray-200",
      })
    } else {
      toast({
        title: "Error",
        description: "Failed to delete chat history. Please try again.",
        variant: "destructive",
        duration: 5000,
        className: "bg-red-50 text-red-900 border-red-200",
        action: <ToastAction altText="Try again" className="text-red-900">Try again</ToastAction>,
      })
    }
  }

  const deleteHistory = (historyId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (!ws) return
    
    toast({
      title: "Delete chat history?",
      description: "This action cannot be undone.",
      duration: 5000,
      className: "bg-white text-gray-900 border-gray-200",
      action: (
        <ToastAction 
          altText="Delete" 
          className="text-red-900 hover:text-red-700"
          onClick={() => {
            try {
              console.log(`Sending delete request for history: ${historyId}`)
              ws.send(JSON.stringify({
                type: 'delete_history',
                historyId
              }))
            } catch (error) {
              console.error('Error sending delete request:', error)
              toast({
                title: "Error",
                description: "Failed to send delete request.",
                variant: "destructive",
                duration: 3000,
              })
            }
          }}
        >
          Delete
        </ToastAction>
      ),
      variant: "destructive",
    })
  }

  const toggleShowMore = () => {
    setShowAllHistories(prev => !prev)
  }

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp)
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } catch (e) {
      return ''
    }
  }

  // Fix the tool expansion toggle function to ensure it works both ways
  const toggleToolExpanded = useCallback((toolId: string) => {
    // Use the functional form of setState to avoid closure issues
    setExpandedTools(prev => {
      // Create a copy to avoid direct state mutation
      const newState = { ...prev };
      // Toggle the value - explicitly check if it exists first
      newState[toolId] = prev[toolId] === true ? false : true;
      return newState;
    });
  }, []);

  // Use a simpler approach that's more reliable
  const renderToolMessage = useCallback((message: Message, index: number) => {
    const toolId = message.toolId || 'unknown-tool';
    const fullToolId = `tool-${toolId}-${index}`;
    const isSuccess = message.result?.success !== false;
    const isExpanded = expandedTools[fullToolId] || false;
    
    // Format tool input if available
    let toolInputDisplay = '';
    if (message.toolInput) {
      try {
        if (typeof message.toolInput === 'object') {
          // Format object nicely for display
          const input = message.toolInput;
          if (Object.keys(input).length === 1 && input.query) {
            // Special case for search queries
            toolInputDisplay = `"${input.query}"`;
          } else if (Object.keys(input).length === 1) {
            // Single key object - display simply
            const key = Object.keys(input)[0];
            toolInputDisplay = `${key}: ${JSON.stringify(input[key]).replace(/"/g, '')}`;
          } else {
            // Multi-key object - display as JSON
            toolInputDisplay = JSON.stringify(input);
          }
        } else if (typeof message.toolInput === 'string') {
          toolInputDisplay = message.toolInput;
        }
      } catch (e) {
        console.error('Error formatting tool input:', e);
      }
    }

    // Modified to be nested under AI bubble instead of independent message
    return (
      <div className="ml-10 my-2 pl-8 border-l-2 border-gray-200">
        <div 
          className="tool-result-header px-3 py-2 bg-gray-100 rounded-t-xl flex justify-between items-center cursor-pointer"
          onClick={() => toggleToolExpanded(fullToolId)}
        >
          <div className="flex flex-col overflow-hidden w-full">
            <div className="flex items-center gap-2">
              <Box className="w-4 h-4 text-purple-500" />
              <span className="font-medium text-sm">{message.name || 'Tool'}</span>
              <Badge variant={isSuccess ? "default" : "destructive"} className={isSuccess ? "bg-green-100 text-green-800 hover:bg-green-200" : ""}>
                {isSuccess ? 'SUCCESS' : 'ERROR'}
              </Badge>
            </div>
            {toolInputDisplay && (
              <span className="text-xs text-gray-600 break-all mt-0.5 pr-6">{toolInputDisplay}</span>
            )}
          </div>
          <div className="toggle-icon flex-shrink-0">
            <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} />
          </div>
        </div>
        
        {isExpanded && (
          <div className="tool-result-content px-3 py-2 text-sm max-h-[800px] overflow-y-auto bg-white border border-t-0 border-gray-200 rounded-b-xl">
            <MarkdownRenderer content={message.content} />
          </div>
        )}
      </div>
    );
  }, [expandedTools, toggleToolExpanded]);

  // Add this helper function to determine if a message is the latest AI message that should show thinking
  const isLatestThinkingMessage = useCallback((index: number) => {
    if (!isThinking) return false;
    
    // If we have a currentAssistantMessageId, only that message should show thinking
    if (currentAssistantMessageId) {
      const message = messages[index];
      const isLatest = message && message.id === currentAssistantMessageId;
      
      // Track the latest thinking message index
      if (isLatest && lastThinkingIndex !== index) {
        setLastThinkingIndex(index);
      }
      
      return isLatest;
    }
    
    // If no current assistant message ID, only show thinking on the standalone indicator
    return false;
  }, [isThinking, currentAssistantMessageId, messages, lastThinkingIndex]);

  const renderMessage = (message: Message, index: number) => {
    // Handle the different message roles
    if (message.role === 'user') {
      return (
        <div key={index} className="flex justify-end mb-4 px-4 md:px-10">
          <div className="flex items-start gap-2 max-w-[85%]">
            <div className="bg-blue-100 rounded-xl rounded-tr-none shadow-sm px-3 py-2 text-gray-800">
              <MarkdownRenderer content={message.content} />
            </div>
            <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center overflow-hidden flex-shrink-0">
              <User className="w-5 h-5 text-white" />
            </div>
          </div>
        </div>
      );
    } else if (message.role === 'assistant') {
      const hasThinking = message.thinkingContent && message.thinkingContent.trim().length > 0;
      const reasoningId = `reasoning-${message.id || index}`;
      const isThinkingExpanded = expandedTools[reasoningId] !== false; // Default to expanded
      
      // Only show thinking animation on the latest message
      const showThinkingAnimation = isLatestThinkingMessage(index) || (message.status === 'streaming' && isThinking);
      
      // Find any tool messages that follow this assistant message
      const toolMessages: Message[] = [];
      let i = index + 1;
      while (i < messages.length && messages[i].role === 'tool') {
        toolMessages.push(messages[i]);
        i++;
      }
      
      return (
        <div key={index} className="flex justify-start mb-4 px-4 md:px-10">
          <div className="flex items-start gap-2 max-w-[85%]">
            <div className="w-8 h-8 rounded-full bg-emerald-500 flex items-center justify-center overflow-hidden flex-shrink-0">
              <Bot className="w-5 h-5 text-white" />
            </div>
            <div className="relative bg-emerald-50 border border-emerald-200 rounded-xl rounded-tl-none shadow-sm px-3 py-2 text-gray-800">
              {/* Remove the local thinking indicator that appeared above messages */}
              
              {/* Thinking content in collapsible section */}
              {hasThinking && (
                <div className={`bg-amber-50 rounded-lg border ${showThinkingAnimation ? 'border-amber-300 border-l-4 border-l-amber-400' : 'border-amber-200'} shadow-sm mb-4`}>
                  <div 
                    className="cursor-pointer font-medium text-amber-700 flex items-center justify-between text-xs p-2"
                    onClick={() => toggleToolExpanded(reasoningId)}
                  >
                    <div className="flex items-center gap-2">
                      <ChevronDown 
                        className={`w-3 h-3 transition-transform duration-200 ${isThinkingExpanded ? 'rotate-180' : ''}`} 
                      />
                      <span>Assistant's Reasoning</span>
                    </div>
                    
                    {showThinkingAnimation && (
                      <span className="px-1.5 py-0.5 bg-amber-100 rounded-full text-xs font-normal animate-pulse">
                        streaming...
                      </span>
                    )}
                  </div>
                  <div 
                    ref={(el) => {
                      // Auto-scroll when thinking content updates during streaming
                      if (el && showThinkingAnimation) {
                        el.scrollTop = el.scrollHeight;
                      }
                    }}
                    className={`mt-2 text-gray-700 max-h-40 overflow-y-auto font-mono whitespace-pre-wrap text-xs px-2 pb-2 ${isThinkingExpanded ? 'block' : 'hidden'}`}
                  >
                    {message.thinkingContent}
                  </div>
                </div>
              )}
              
              {/* Assistant message content */}
              <MarkdownRenderer content={message.content} />
              
              {/* Render tools indented under this message */}
              {toolMessages.length > 0 && (
                <div className="mt-4 space-y-2">
                  {toolMessages.map((toolMsg, toolIdx) => {
                    return renderToolMessage(toolMsg, index + toolIdx + 1);
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      );
    } else if (message.role === 'tool') {
      // Skip rendering tool messages directly since they're handled in the assistant message
      return null;
    }

    return null;
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Add useEffect to handle initial state based on screen size
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      setIsSidebarOpen(!mobile)
    }
    
    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const getHistoryLimit = () => {
    return showAllHistories ? undefined : (isMobile ? 12 : 5)
  }

  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      const adjustHeight = () => {
        textarea.style.height = 'auto'
        const newHeight = Math.min(textarea.scrollHeight, 200) // Respect max-height of 200px
        textarea.style.height = `${newHeight}px`
        
        // Add/remove scrollbar based on content height
        if (textarea.scrollHeight > 200) {
          textarea.style.overflowY = 'auto'
        } else {
          textarea.style.overflowY = 'hidden'
        }
      }
      
      // Run adjustHeight on initial render and input changes
      adjustHeight()
      textarea.addEventListener('input', adjustHeight)
      
      // Also adjust on window resize
      window.addEventListener('resize', adjustHeight)
      
      return () => {
        textarea.removeEventListener('input', adjustHeight)
        window.removeEventListener('resize', adjustHeight)
      }
    }
  }, [input])

  // Add a useEffect to monitor isThinking changes
  useEffect(() => {
    DEBUG && console.log('isThinking changed to:', isThinking)
  }, [isThinking])

  // Request status
  const requestStatus = () => {
    if (!ws) return
    
    setIsLoadingTools(true)
    try {
      ws.send(JSON.stringify({ 
        type: 'status_request',
        sessionId: sessionId
      }))
    } catch (error) {
      setIsLoadingTools(false)
      DEBUG && console.error('Error requesting status:', error)
    }
  }

  // Update the useEffect for session ID - remove the auto-refresh interval
  useEffect(() => {
    if (sessionId && ws) {
      // Initial request on connection only
      setIsLoadingTools(true)
      requestStatus()
      
      // Remove the interval timer for auto-refresh
    }
  }, [sessionId, ws])

  // Add effect to initialize expanded state when servers change
  useEffect(() => {
    if (Object.keys(toolsByServer).length > 0) {
      // Initialize all servers as collapsed (not expanded)
      const initialExpandState: Record<string, boolean> = {}
      Object.keys(toolsByServer).forEach(serverId => {
        initialExpandState[serverId] = false
      })
      setExpandedServers(initialExpandState)
      
      // Log the total number of tools for debugging
      let totalTools = 0
      Object.entries(toolsByServer).forEach(([serverId, tools]) => {
        console.log(`Server ${serverId} has ${tools.length} tools`)
        totalTools += tools.length
      })
      console.log(`Total tools across all servers: ${totalTools}`)
    }
  }, [toolsByServer])

  // Add toggle function
  const toggleServerExpanded = (serverId: string) => {
    setExpandedServers(prev => ({
      ...prev,
      [serverId]: !prev[serverId]
    }))
  }

  // Add more informative toast for refresh to show actual tools count
  const refreshTools = () => {
    if (ws && sessionId) {
      requestStatus()
      toast({
        title: "Refreshing tools",
        description: "Fetching the latest tools from Agentic AI servers",
        duration: 2000,
      })
    }
  }

  // Load MCP configuration
  const loadMcpConfig = async () => {
    if (!ws) return;
    
    try {
      setIsConfigLoading(true);
      setConfigError(null);
      
      ws.send(JSON.stringify({
        type: 'get_mcp_config'
      }));
      
      // 消息处理在WebSocket onmessage事件中
    } catch (error) {
      console.error('Error requesting Agentic AI configuration:', error);
      setConfigError('Failed to request Agentic AI configuration');
      setIsConfigLoading(false);
    }
  };
  
  // Save MCP configuration
  const saveMcpConfig = async () => {
    if (!ws || !mcpConfig) return;
    
    try {
      setIsConfigLoading(true);
      
      ws.send(JSON.stringify({
        type: 'update_mcp_config',
        config: mcpConfig
      }));
      
      // 消息处理在WebSocket onmessage事件中
    } catch (error) {
      console.error('Error saving Agentic AI config:', error);
      toast({
        title: "Error",
        description: "Failed to save Agentic AI configuration",
        variant: "destructive",
        duration: 5000
      });
      setIsConfigLoading(false);
    }
  };
  
  // Add new server
  const addServer = () => {
    if (!mcpConfig || !newServerName.trim() || !newServerUrl.trim()) return;
    
    if (mcpConfig.mcpServers[newServerName]) {
      toast({
        title: "Error",
        description: `Server '${newServerName}' already exists`,
        variant: "destructive",
        duration: 5000
      });
      return;
    }
    
    const updatedConfig = {
      ...mcpConfig,
      mcpServers: {
        ...mcpConfig.mcpServers,
        [newServerName]: {
          url: newServerUrl
        }
      }
    };
    
    setMcpConfig(updatedConfig);
    setNewServerName('');
    setNewServerUrl('');
    setIsAddingServer(false);
    setIsConfigChanged(true);
  };
  
  // Start editing server
  const startEditServer = (serverName: string) => {
    if (!mcpConfig) return;
    
    setIsEditingServer(serverName);
    setEditServerUrl(mcpConfig.mcpServers[serverName].url);
  };
  
  // Save server edit
  const saveServerEdit = () => {
    if (!mcpConfig || !isEditingServer || !editServerUrl.trim()) return;
    
    const updatedConfig = {
      ...mcpConfig,
      mcpServers: {
        ...mcpConfig.mcpServers,
        [isEditingServer]: {
          url: editServerUrl
        }
      }
    };
    
    setMcpConfig(updatedConfig);
    setIsEditingServer(null);
    setEditServerUrl('');
    setIsConfigChanged(true);
  };
  
  // Cancel server edit
  const cancelServerEdit = () => {
    setIsEditingServer(null);
    setEditServerUrl('');
  };
  
  // Confirm deleting server
  const confirmDeleteServer = (serverName: string) => {
    setServerToDelete(serverName);
    setIsConfirmDialogOpen(true);
  };
  
  // Delete server
  const deleteServer = () => {
    if (!mcpConfig || !serverToDelete) return;
    
    const updatedMcpServers = { ...mcpConfig.mcpServers };
    delete updatedMcpServers[serverToDelete];
    
    setMcpConfig({
      ...mcpConfig,
      mcpServers: updatedMcpServers
    });
    
    setServerToDelete(null);
    setIsConfirmDialogOpen(false);
    setIsConfigChanged(true);
  };
  
  // Cancel delete
  const cancelDelete = () => {
    setServerToDelete(null);
    setIsConfirmDialogOpen(false);
  };
  
  // Handle MCP configuration response
  const handleMcpConfigResponse = (data: any) => {
    setIsConfigLoading(false);
    
    if (data.status === 'ok') {
      setMcpConfig(data.config);
      setIsConfigChanged(false);
    } else {
      setConfigError(data.message || 'Failed to load Agentic AI configuration');
      toast({
        title: "Error",
        description: data.message || "Failed to load Agentic AI configuration",
        variant: "destructive",
        duration: 5000
      });
    }
  };
  
  // Handle MCP configuration update response
  const handleMcpConfigUpdateResponse = (data: any) => {
    setIsConfigLoading(false);
    
    if (data.status === 'ok') {
      setIsConfigChanged(false);
      toast({
        title: "Success",
        description: "Agentic AI configuration saved successfully",
        variant: "default",
        duration: 5000
      });
      
      // Reload tools if the current session was restarted
      if (sessionId && data.restarted_sessions && data.restarted_sessions.includes(sessionId)) {
        requestStatus();
      }
    } else {
      toast({
        title: "Error",
        description: data.message || "Failed to save Agentic AI configuration",
        variant: "destructive",
        duration: 5000
      });
    }
  };
  
  // Load MCP configuration when settings dialog opens
  useEffect(() => {
    if (isSettingsOpen && ws && !mcpConfig && !isConfigLoading) {
      loadMcpConfig();
    }
  }, [isSettingsOpen, ws, mcpConfig, isConfigLoading]);

  // Add an effect to add toggle functionality to tool results
  useEffect(() => {
    document.querySelectorAll('.tool-result-header').forEach(header => {
      const contentDiv = header.nextElementSibling as HTMLElement
      const toggleIcon = header.querySelector('.toggle-icon')
      
      if (header && contentDiv && toggleIcon) {
        header.addEventListener('click', function() {
          const isHidden = contentDiv.style.display === 'none'
          contentDiv.style.display = isHidden ? 'block' : 'none'
          toggleIcon.classList.toggle('open', isHidden)
          header.setAttribute('aria-expanded', isHidden ? 'true' : 'false')
        })
      }
    })
  }, [messages])

  // Add helper function for unique IDs
  const getUniqueId = (prefix: string) => {
    setMessageCounter(prev => prev + 1)
    return `${prefix}-${Date.now()}-${messageCounter}`
  }

  // Update the handleMessage function to properly process all message types
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const data = JSON.parse(event.data)
      // Add better debugging for all incoming messages
      if (DEBUG) {
        if (data.type === 'llm_update') {
          console.log('%c WebSocket Message', 'background: #333; color: #bada55', {
            type: data.type,
            update_type: data.update_type,
            delta_type: data.delta_type || 'none',
            has_thinking: !!data.thinking,
            thinking_length: data.thinking ? data.thinking.length : 0,
          })
        } else {
          console.log('%c WebSocket Message', 'background: #333; color: #bada55', data)
        }
      }

      // Process different message types
      if (data.type === 'history') {
        setHistories(data.histories || [])
      } else if (data.type === 'message') {
        // Handle incoming messages from history
        if (data.role && data.content) {
          if (DEBUG) console.log('Adding message:', data.role, data.content.substring(0, 100))
          const newMsg: Message = {
            id: data.id || getUniqueId(data.role),
            role: data.role as 'user' | 'assistant',
            content: data.content,
            timestamp: data.timestamp || new Date().toISOString()
          }
          setMessages(prev => [...prev, newMsg])
        }
      } else if (data.type === 'history_metadata') {
        // Handle history metadata when loading a conversation
        if (data.historyId) {
          setSelectedHistory(data.historyId)
          // If we receive title info, update the chat title in the UI
          if (data.title) {
            DEBUG && console.log('Received history title:', data.title)
            // We could potentially update the histories array with this title
            // but we'll receive a full histories list update anyway
          }
        }
      } else if (data.type === 'initialize_response') {
        handleInitializeResponse(data)
      } else if (data.type === 'system_prompt_saved') {
        if (data.success) {
          // Already handled in the button click handler
          DEBUG && console.log('System prompt saved successfully')
        } else {
          toast({
            title: "Error",
            description: "Failed to save system prompt. Please try again.",
            variant: "destructive",
            duration: 5000
          })
        }
      } else if (data.type === 'status_update') {
        handleStatusUpdate(data)
      } else if (data.type === 'error') {
        if (data.content) {
          setConfigError(data.content)
          toast({
            title: "Error",
            description: data.content,
            duration: 3000,
            variant: "destructive"
          })
        }
        setIsThinking(false)
      } else if (data.type === 'stream') {
        if (data.content) {
          DEBUG && console.log('Received stream content:', data.content)
          
          // If this is the start of a new message, reset all states
          if (!contentBufferRef.current) {
            setThinkingContent('')
            setStreamContent('')
            isThinkingRef.current = false
            setIsThinking(false)
          }
          
          contentBufferRef.current += data.content
          
          const hasCompleteThinkStart = /<think>/i.test(contentBufferRef.current)
          const hasCompleteThinkEnd = /<\/think>/i.test(contentBufferRef.current)
          
          if (hasCompleteThinkStart && !isThinkingRef.current) {
            isThinkingRef.current = true
            setIsThinking(true)
            // Extract content after <think> tag
            const thinkStartIndex = contentBufferRef.current.indexOf('<think>') + '<think>'.length
            const thinkContent = contentBufferRef.current.slice(thinkStartIndex)
            setThinkingContent(thinkContent)
            setStreamContent('')
          } else if (hasCompleteThinkEnd && isThinkingRef.current) {
            isThinkingRef.current = false
            setIsThinking(false)
            // Extract content after </think> tag
            const thinkEndIndex = contentBufferRef.current.indexOf('</think>') + '</think>'.length
            const remainingContent = contentBufferRef.current.slice(thinkEndIndex)
            contentBufferRef.current = remainingContent
            setStreamContent(remainingContent)
            // Clean up thinking content tags
            setThinkingContent(prev => prev.replace(/<\/?think>/g, ''))
          } else {
            if (!isThinkingRef.current) {
              setStreamContent(prev => prev + data.content)
            } else {
              setThinkingContent(prev => prev + data.content)
            }
          }
        }
      } else if (data.type === 'end') {
        if (data.content) {
          // Reset all states
          isThinkingRef.current = false
          setIsThinking(false)
          setThinkingContent('')
          contentBufferRef.current = ''
          
          const message: Message = {
            role: 'assistant',
            content: data.content,
            timestamp: new Date().toISOString()
          }
          setMessages(prev => [...prev, message])
          setStreamContent('')
          
          if (data.historyId && !selectedHistory) {
            setSelectedHistory(data.historyId)
          }
          
          if (!selectedHistory && ws) {
            ws.send(JSON.stringify({ type: 'get_histories' }))
          }
        }
      } else if (data.type === 'processing_started') {
        setIsThinking(true)
        // If assistant_message_id is provided, store it
        if (data.assistant_message_id) {
          setCurrentAssistantMessageId(data.assistant_message_id)
        }
      } else if (data.type === 'processing_complete') {
        if (DEBUG) console.log('Processing complete. isThinking set to false')
        setIsThinking(false)
        setCurrentAssistantMessageId(null)
        setCurrentToolUseId(null)
        
        // Ensure the final message is marked as complete if it exists
        if (data.assistant_message_id) {
          setMessages(prev => {
            return prev.map(msg => {
              if (msg.id === data.assistant_message_id) {
                return {
                  ...msg,
                  status: 'complete'
                }
              }
              return msg
            })
          })
        }
        
        // Always update historyId to ensure message continuity
        if (data.historyId) {
          // Always update the selected history to maintain conversation continuity
          setSelectedHistory(data.historyId)
          if (DEBUG) console.log('Updated historyId for conversation:', data.historyId)
        }
        
        // Only add a new message if we don't already have one with this ID
        if (data.content && data.assistant_message_id && 
            !messages.some(msg => msg.id === data.assistant_message_id)) {
          console.log('Adding final assistant response:', 
                     data.content.substring(0, 100) + (data.content.length > 100 ? '...' : ''))
          const newAssistantMsg: Message = {
            id: data.assistant_message_id,
            role: 'assistant',
            content: data.content,
            status: 'complete',
            timestamp: new Date().toISOString()
          }
          setMessages(prev => [...prev, newAssistantMsg])
        }
        
        // Request updated history list
        if (ws) {
          ws.send(JSON.stringify({ type: 'get_histories' }))
        }
      } else if (data.type === 'llm_update') {
        handleLLMUpdate(data)
      } else if (data.type === 'tool_call_result') {
        handleToolCallResult(data)
      } else if (data.type === 'mcp_config_response') {
        handleMcpConfigResponse(data)
      } else if (data.type === 'mcp_config_update_response') {
        handleMcpConfigUpdateResponse(data)
      } else {
        console.warn('Unknown message type:', data.type, data)
      }
    } catch (err) {
      console.error('Error parsing websocket message:', err, event.data)
    }
  }, [ws, messages, selectedHistory]);

  // Simplify the handleLLMUpdate function to directly update the messages state
  const handleLLMUpdate = (data: any) => {
    if (DEBUG) {
      console.log('LLM Update:', data.update_type, data);
      
      // Special logging for thinking events
      if (data.update_type === 'content_block_delta' && data.delta_type === 'thinking_delta') {
        console.log('THINKING DELTA RECEIVED:', data.thinking?.substring(0, 50), '...');
      } else if (data.update_type === 'thinking') {
        console.log('THINKING UPDATE RECEIVED:', data.thinking?.substring(0, 50), '...');
      }
    }
    
    const updateType = data.update_type;
    const messageId = data.assistant_message_id || currentAssistantMessageId;
    
    // Handle message_start updates - create a new message
    if (updateType === 'message_start') {
      // Create a new assistant message when a new message starts
      const newMessageId = data.assistant_message_id || getUniqueId('assistant');
      setCurrentAssistantMessageId(newMessageId);
      
      // Reset temporary thinking data for new message
      resetThinkingContent();
      
      // Add a placeholder message to the UI that will be updated
      addOrUpdateMessage({
        id: newMessageId,
        role: 'assistant',
        content: '', // Empty content initially
        thinkingContent: '', // Initialize empty thinking content
        status: 'streaming',
        timestamp: new Date().toISOString()
      });
      return;
    }
    
    // For thinking updates, attach thinking content to the current assistant message
    if (updateType === 'thinking') {
      setIsThinking(true);
      const thinkingText = data.thinking || '';
      
      if (thinkingText && messageId) {
        // Update the assistant message with thinking content
        setMessages(prev => {
          return prev.map(msg => {
            if (msg.id === messageId) {
              return {
                ...msg, 
                thinkingContent: thinkingText
              };
            }
            return msg;
          });
        });
        
        // Also update global state for backward compatibility
        globalThinkingData = thinkingText;
        setThinkingContent(thinkingText);
        
        // // Show toast only on first thinking content
        // if (!hasShownThinkingToastRef.current) {
        //   hasShownThinkingToastRef.current = true;
        //   toast({
        //     title: "Assistant is thinking",
        //     description: "The assistant's reasoning process is now visible",
        //     duration: 3000,
        //   });
        // }
      }
      return;
    }
    
    // For content_block_start with thinking type, initialize thinking state
    if (updateType === 'content_block_start' && data.content_block_type === 'thinking') {
      console.log('Content block start - thinking detected');
      setIsThinking(true);
      return;
    }
    
    // For thinking deltas, append to the current message's thinking content
    if (updateType === 'content_block_delta' && data.delta_type === 'thinking_delta') {
      if (DEBUG) console.log('Thinking delta received, updating content');
      setIsThinking(true);
      
      const thinkingDelta = data.thinking || '';
      if (thinkingDelta && messageId) {
        // Find and update the corresponding message
        setMessages(prev => {
          return prev.map(msg => {
            if (msg.id === messageId) {
              const updatedThinking = (msg.thinkingContent || '') + thinkingDelta;
              return {
                ...msg,
                thinkingContent: updatedThinking
              };
            }
            return msg;
          });
        });
        
        // Also update global thinking for backward compatibility
        globalThinkingData += thinkingDelta;
        setThinkingContent(globalThinkingData);
        
        // Show a toast for the first thinking delta
        // if (!hasShownThinkingToastRef.current) {
        //   hasShownThinkingToastRef.current = true;
        //   toast({
        //     title: "Assistant is thinking",
        //     description: "The assistant's reasoning process is now visible",
        //     duration: 3000,
        //   });
        // }
      }
      return;
    }
    
    // For the text delta (actual content from the LLM), add it directly to the UI
    if (updateType === 'content_block_delta' && data.delta_type === 'text_delta') {
      if (!messageId) {
        // If we don't have a message ID yet, create a new message
        const newId = getUniqueId('assistant');
        if (DEBUG) console.log('No message ID, creating new message:', newId);
        setCurrentAssistantMessageId(newId);
        
        // Create a new message with the text
        addOrUpdateMessage({
          id: newId,
          role: 'assistant',
          content: data.text || '',
          thinkingContent: globalThinkingData, // Attach current thinking content
          status: 'streaming',
          timestamp: new Date().toISOString()
        });
      } else {
        // Find the existing message to update it
        const existingMessage = messages.find(msg => msg.id === messageId);
        
        if (existingMessage) {
          // Update the existing message with new content
          addOrUpdateMessage({
            ...existingMessage,
            content: existingMessage.content + (data.text || ''),
            status: 'streaming'
          });
        } else {
          // Create a new message if it doesn't exist yet
          addOrUpdateMessage({
            id: messageId,
            role: 'assistant',
            content: data.text || '',
            thinkingContent: globalThinkingData, // Attach current thinking content
            status: 'streaming',
            timestamp: new Date().toISOString()
          });
        }
      }
      return;
    }
    
    // For tool use, create a tool message
    if (updateType === 'content_block_start' && data.content_block_type === 'tool_use') {
      const toolId = data.tool_id || getUniqueId('tool');
      if (DEBUG) console.log('Setting tool use ID:', toolId);
      setCurrentToolUseId(toolId);
      
      // Store the mapping for later result handling
      if (data.tool_id) {
        setToolResultMap(prev => ({
          ...prev,
          [data.tool_id]: toolId
        }));
      }
      
      // Format the tool input for display
      let toolInputForMessage = data.tool_input || {};
      
      // Create a new tool message
      addOrUpdateMessage({
        id: toolId,
        role: 'tool',
        toolId: data.tool_id,
        name: data.tool_name || data.name || 'Tool',
        toolInput: toolInputForMessage,
        content: `Running ${data.tool_name || data.name || 'Tool'}...`,
        status: 'running',
        timestamp: new Date().toISOString()
      });
    }
  }

  // Add this function back to handle tool call results
  const handleToolCallResult = (data: any) => {
    if (DEBUG) console.log('Received tool call result:', data);
    
    if (!data.tool_id) {
      console.error('Tool result missing tool_id');
      return;
    }
    
    const toolBubbleId = toolResultMap[data.tool_id];
    if (!toolBubbleId) {
      if (DEBUG) console.error('No tool bubble found for tool_id:', data.tool_id);
      
      // Create a standalone tool message with result
      const newToolMsg: Message = {
        id: getUniqueId('tool-result'),
        role: 'tool',
        content: data.content || '',
        name: data.name || 'Tool Result',
        timestamp: new Date().toISOString(),
        toolId: data.tool_id,
        status: 'complete',
        toolInput: data.input || {},
        result: {
          success: data.success,
          content: data.content || '',
          error: data.error || ''
        }
      };
      
      setMessages(prev => [...prev, newToolMsg]);
      return;
    }
    
    // Add tool result to the appropriate message
    if (DEBUG) console.log('Updating tool message with result:', toolBubbleId);
    setMessages(prev => {
      return prev.map(msg => {
        if (msg.id === toolBubbleId || msg.toolId === data.tool_id) {
          return {
            ...msg,
            name: data.name || msg.name,
            content: data.content || msg.content,
            toolInput: data.input || msg.toolInput,
            result: {
              success: data.success,
              content: data.content || '',
              error: data.error || ''
            }
          };
        }
        return msg;
      });
    });
  };

  // Add this effect to log all messages for debugging
  useEffect(() => {
    if (DEBUG) {
      console.log('Current messages state:', messages);
    }
  }, [messages]);

  // Add this effect to update the websocket handler when it changes
  useEffect(() => {
    if (ws) {
      // Update the message handler
      ws.onmessage = handleMessage;
      
      return () => {
        // Cleanup if needed
      };
    }
  }, [ws, handleMessage]);

  // Create a separate function to add a message to avoid duplication
  const addOrUpdateMessage = useCallback((newMessage: Message) => {
    setMessages(prev => {
      // Check if we already have this message (by ID)
      const existingIndex = prev.findIndex(m => m.id === newMessage.id);
      
      if (existingIndex >= 0) {
        // Update existing message
        const updatedMessages = [...prev];
        updatedMessages[existingIndex] = {
          ...updatedMessages[existingIndex],
          ...newMessage,
          // For content, we might want to append rather than replace in some cases
          content: newMessage.content || updatedMessages[existingIndex].content
        };
        return updatedMessages;
      } else {
        // Add new message
        return [...prev, newMessage];
      }
    });
  }, []);

  // Add more debugging to identify when thinking content is updated
  useEffect(() => {
    if (DEBUG && thinkingContent) {
      console.log(`Thinking content updated, length: ${thinkingContent.length}`);
    }
  }, [thinkingContent]);

  // Add a debug function to reset thinking content
  const resetThinkingContent = () => {
    console.log('Resetting thinking content state');
    setThinkingContent('');
    hasShownThinkingToastRef.current = false;
    globalThinkingData = ''; // Reset global var too
  };

  // Add a new function to ensure thinking data is captured globally
  const updateThinkingContent = (newContent: string, append = false) => {
    // Always update the global var first
    if (append) {
      globalThinkingData += newContent;
    } else {
      globalThinkingData = newContent;
    }
    
    // Then update the state
    console.log(`Updating thinking content to ${globalThinkingData.length} chars`);
    setThinkingContent(globalThinkingData);
  };

  // Add a function to keep the DOM updated with latest thinking status, called in useEffect
  const updateDebugDom = useCallback(() => {
    const debugEl = document.getElementById('raw-debug-thinking');
    if (debugEl) {
      debugEl.textContent = `Global thinking: ${globalThinkingData.length} chars / State thinking: ${thinkingContent.length} chars`;
    }
  }, [thinkingContent]);

  // Update useEffect for thinking content to also track the global var
  useEffect(() => {
    if (DEBUG && thinkingContent) {
      console.log(`Thinking content state updated, length: ${thinkingContent.length}`);
      console.log(`Global thinking length: ${globalThinkingData.length}`);
      updateDebugDom();
    }
  }, [thinkingContent, updateDebugDom]);

  // Calculate position of settings dialog when button is clicked
  const handleOpenSettings = () => {
    if (settingsButtonRef.current) {
      const rect = settingsButtonRef.current.getBoundingClientRect();
      // Calculate better positioning that ensures visibility
      const top = rect.bottom + 10;
      const right = window.innerWidth - rect.right;
      
      // Ensure the dialog doesn't go off-screen
      const adjustedTop = Math.min(top, window.innerHeight - 400);
      
      console.log('Dialog position:', { top: adjustedTop, right });
      
      setDialogPosition({
        top: adjustedTop,
        right: right
      });
    }
    setIsSettingsOpen(true);
  };

  const createConfetti = () => {
    if (canvasRef.current) {
      const myConfetti = confetti.create(canvasRef.current, {
        resize: true,
        useWorker: true
      });

      myConfetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.1, y: 0.6 }
      });
      myConfetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.9, y: 0.6 }
      });
    }
  }

  // Hide all thinking text shown at the top level - add this style to hide them
  const hiddenStyle = { display: 'none' };

  useEffect(() => {
    // Add a direct DOM manipulation to hide all the "Thinking ***" elements
    const hideThinkingElements = () => {
      // Find elements containing "Thinking ***" text
      document.querySelectorAll('div').forEach(el => {
        if (el.innerText && el.innerText.includes('Thinking ***')) {
          el.style.display = 'none';
          el.classList.add('thinking-text');
        }
      });
    };
    
    // Run this effect whenever thinking state changes or messages change
    hideThinkingElements();
    
    // Also set up a periodic cleanup
    const interval = setInterval(hideThinkingElements, 300);
    return () => clearInterval(interval);
  }, [isThinking, messages]);

  // Add this useEffect to find and hide "Thinking ***" elements
  useEffect(() => {
    // Helper function to find and hide the thinking elements
    const hideThinkingTexts = () => {
      const elements = document.querySelectorAll('div');
      elements.forEach(el => {
        const text = el.textContent || '';
        if (text.includes('Thinking ***')) {
          // Add both class and inline style for maximum compatibility
          el.classList.add('thinking-text');
          el.style.display = 'none';
        }
      });
    };
    
    // Initial execution
    hideThinkingTexts();
    
    // Set up interval to keep checking (in case new elements are added)
    const intervalId = setInterval(hideThinkingTexts, 500);
    
    return () => {
      clearInterval(intervalId);
    };
  }, [messages, isThinking]); // Re-run when messages or thinking state changes

  // Use our custom hook
  useHideThinkingElements();

  return (
    <div className="flex h-full relative">
      <canvas 
        ref={canvasRef}
        className="absolute inset-0 pointer-events-none"
        style={{ width: '100%', height: '100%', zIndex: 50 }}
      />
      
      {/* Keep only our dramatic thinking animation overlay */}
      <ThinkingAnimation isActive={isThinking} />
      
      {/* Enhanced CSS to hide thinking elements - we need this to handle any elements added dynamically */}
      <style jsx global>{`
        /* Target specifically the "Thinking ***" elements */
        div:not(.allowed-thinking) > div:empty + div:contains("Thinking ***"),
        div:not(.allowed-thinking) > span:contains("Thinking ***"),
        div:not(.allowed-thinking) > div > div:contains("Thinking ***") {
          display: none !important;
        }
        
        /* Hide specifically the "Thinking ***" text that appears as a separate element */
        div:empty + div:contains("Thinking ***") {
          display: none !important;
        }
        
        /* Create a more specific rule for the circled elements in the screenshot */
        body div > div:first-child:contains("Thinking ***") {
          display: none !important;
        }
        
        /* Additional rules */
        .thinking-text {
          display: none !important;
        }
        
        /* Animation for the thinking indicator gradient bar */
        @keyframes gradient-x {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        .animate-gradient-x {
          background-size: 200% 200%;
          animation: gradient-x 3s linear infinite;
        }
      `}</style>
      
      {/* Mobile Sidebar Toggle Button */}
      <button
        className="md:hidden fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-lg"
        onClick={toggleSidebar}
      >
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          {isSidebarOpen ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          )}
        </svg>
      </button>

      {/* Sidebar */}
      <div className={`
        md:w-64 w-80 border-r border-border/40 bg-card flex flex-col
        md:relative fixed
        transition-transform duration-300 ease-in-out
        md:translate-x-0
        ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        md:h-full h-[100dvh]
        top-0 left-0
        z-40
      `}>
        <div className="p-4 border-b border-border/40">
          <div className="font-semibold text-sm mb-2">Agentic AI Chat</div>
          <Button 
            className="w-full bg-primary hover:bg-primary/90 mb-2" 
            onClick={startNewChat}
          >
            New Chat
          </Button>
          
          <Button 
            className={`w-full ${sessionId ? 'bg-emerald-600 hover:bg-emerald-700' : 'bg-emerald-600 hover:bg-emerald-700 animate-pulse shadow-md'}`}
            onClick={handleOpenSettings}
            ref={settingsButtonRef}
          >
            {sessionId ? 'Connect to Agentic AI' : 'Connect to Agentic AI →'}
          </Button>
        </div>
        
        <ScrollArea 
          className="flex-1 md:h-[calc(100vh-8rem)] overflow-auto"
          style={{ 
            scrollbarWidth: 'thin',
            scrollbarColor: 'rgba(155, 155, 155, 0.5) transparent'
          }}
        >
          <div className="p-2 space-y-2">
            {histories
              .slice(0, getHistoryLimit())
              .map(history => (
                <div
                  key={history.id}
                  className={`group flex items-center justify-between p-2 cursor-pointer rounded-lg hover:bg-green-50 ${
                    selectedHistory === history.id ? 'bg-yellow-50' : ''
                  }`}
                  onClick={() => loadChatHistory(history.id)}
                >
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{history.title}</div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(history.timestamp).toLocaleDateString()}
                    </div>
                  </div>
                  <button
                    onClick={(e) => deleteHistory(history.id, e)}
                    className="opacity-0 group-hover:opacity-100 p-1 hover:bg-red-100 rounded transition-opacity"
                    title="Delete conversation"
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </button>
                </div>
            ))}
            
            {histories.length > (isMobile ? 12 : 5) && (
              <button
                onClick={toggleShowMore}
                className="w-full py-2 px-4 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              >
                {showAllHistories ? 'Show Less' : `Show ${histories.length - (isMobile ? 12 : 5)} More`}
              </button>
            )}
          </div>
        </ScrollArea>
        
        {/* Tools section - always show with appropriate state */}
        <div className="border-t border-border/40 p-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium flex items-center">
              <Box className="w-4 h-4 mr-1.5" />
              Available Tools
            </h3>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-7 w-7 p-0" 
              onClick={refreshTools}
              title="Refresh tools"
              disabled={isLoadingTools || !sessionId}
            >
              <RefreshCw className={`h-3.5 w-3.5 ${isLoadingTools ? 'animate-spin' : ''}`} />
            </Button>
          </div>
          
          {!sessionId ? (
            <div className="py-3 px-2 text-xs text-muted-foreground text-center bg-muted/20 rounded-md">
              Connect to Agentic AI to view available tools
            </div>
          ) : isLoadingTools ? (
            <div className="py-4 flex flex-col items-center justify-center space-y-2">
              <div className="w-5 h-5 border-2 border-t-primary border-l-primary border-primary/30 rounded-full animate-spin"></div>
              <p className="text-xs text-muted-foreground">Loading tools...</p>
            </div>
          ) : Object.keys(toolsByServer).length === 0 ? (
            <div className="py-3 px-2 text-xs text-muted-foreground text-center bg-muted/20 rounded-md">
              No tools available. Make sure Agentic AI servers are connected.
            </div>
          ) : (
            <>
              <div className="text-xs text-muted-foreground mb-3">
                {Object.values(toolsByServer).flat().length} tools from {Object.keys(toolsByServer).length} servers
              </div>
              
              <div className="server-tools-container w-full space-y-2">
                {Object.entries(toolsByServer).map(([serverId, serverTools]) => (
                  <div 
                    key={serverId} 
                    className="border rounded-md overflow-hidden bg-card shadow-sm"
                  >
                    <div 
                      className="px-3 py-2 hover:bg-muted/50 text-sm cursor-pointer flex items-center bg-muted/30"
                      onClick={() => toggleServerExpanded(serverId)}
                    >
                      <div className="flex items-center gap-2 w-full">
                        <Badge 
                          variant={serverStatus[serverId]?.connected ? "default" : "secondary"}
                          className={`text-xs px-2 py-0.5 ${
                            serverStatus[serverId]?.connected 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {serverStatus[serverId]?.connected ? 'Connected' : 'Disconnected'}
                        </Badge>
                        <span className="font-medium text-sm">{serverId}</span>
                        <span className="ml-auto text-xs text-muted-foreground bg-muted/40 rounded-full px-2">
                          {serverTools.length}
                        </span>
                        
                        <svg
                          width="15"
                          height="15"
                          viewBox="0 0 15 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-4 w-4 transition-transform duration-200 ml-1 ${
                            expandedServers[serverId] ? 'rotate-180' : ''
                          }`}
                        >
                          <path
                            d="M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"
                            fill="currentColor"
                            fillRule="evenodd"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                    
                    {expandedServers[serverId] && (
                      <div className="pt-0 pb-1 px-2">
                        <div className="tools-container max-h-[320px] overflow-auto pr-1 mt-1">
                          <div className="space-y-1.5 py-1">
                            {serverTools.map((tool, index) => (
                              <div 
                                key={index} 
                                className={`text-xs p-2 bg-muted/40 rounded-md hover:bg-muted/60 transition-colors border-l-2 ${index % 2 === 0 ? 'border-blue-300/50' : 'border-primary/20'}`}
                              >
                                <div className="font-medium text-primary flex items-center">
                                  <Box className="w-3 h-3 mr-1.5 flex-shrink-0" />
                                  <span className="truncate mr-1">{tool.name || "Unnamed Tool"}</span>
                                  <span className="ml-auto opacity-70 text-[10px] bg-gray-100 px-1 rounded-sm">#{index + 1}</span>
                                </div>
                                <div className="text-muted-foreground mt-1 text-xs leading-tight">
                                  {tool.description || "No description available"}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/20 z-30 md:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col h-full relative">
        <div className="sticky top-0 left-0 right-0 p-4 bg-white border-b border-gray-200 shadow-sm z-10">
          <div className="flex items-center justify-between">
            <div className="font-semibold text-lg text-gray-800 ml-12 md:ml-0">
              Agentic AI Chat {sessionId && <Badge variant="outline" className="ml-2 bg-green-100 text-green-700 border-green-400 hover:bg-green-200">Connected</Badge>}
            </div>
            <div className="flex items-center gap-3">
              {/* Add server and tool status */}
              {sessionId && Object.keys(toolsByServer).length > 0 && (
                <div className="flex items-center gap-2 text-xs">
                  <div className="flex items-center">
                    <span className="text-muted-foreground mr-1">Servers:</span>
                    <Badge variant="outline" className="font-mono">
                      {Object.values(serverStatus).filter(s => s.connected).length}/{Object.keys(serverStatus).length}
                    </Badge>
                  </div>
                  <div className="flex items-center">
                    <span className="text-muted-foreground mr-1">Tools:</span>
                    <Badge variant="outline" className="font-mono">
                      {Object.values(toolsByServer).flat().length}
                    </Badge>
                  </div>
                </div>
              )}
              {/* Settings button with ref for positioning dialog */}
              <Button
                ref={settingsButtonRef}
                variant="outline"
                size="icon"
                onClick={handleOpenSettings}
                className="hover:bg-gray-100"
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Settings Dialog */}
        <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <DialogContent 
            style={{
              position: 'fixed', // Change to fixed for viewport positioning
              top: '80px', // Position from top of viewport
              right: '20px', // Position from right of viewport
              transform: 'none', // Override default centering
              margin: 0,
              zIndex: 1000, // Ensure it's on top
              maxWidth: '500px', // Control width
              width: '90vw',
              maxHeight: 'calc(100vh - 100px)', // Prevent overflow
              overflowY: 'auto' // Allow scrolling for content
            }}
            className="p-4 bg-white shadow-lg rounded-lg border border-gray-200"
          >
            <div className="absolute right-4 top-4">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => setIsSettingsOpen(false)}
                className="hover:bg-gray-100 rounded-full h-8 w-8 p-0"
              >
                <svg
                  width="15"
                  height="15"
                  viewBox="0 0 15 15"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                >
                  <path
                    d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"
                    fill="currentColor"
                    fillRule="evenodd"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
            
            <DialogHeader className="mb-4">
              <DialogTitle className="text-xl font-bold text-gray-900">Agentic AI Connection Settings</DialogTitle>
              <p className="text-sm text-gray-600 mt-1">Configure your connection to Agentic AI services</p>
            </DialogHeader>
            
            <Tabs defaultValue="account" className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="account">Account</TabsTrigger>
                <TabsTrigger value="servers">Agentic AI Servers</TabsTrigger>
                <TabsTrigger value="prompt">System Prompt</TabsTrigger>
              </TabsList>
              
              <TabsContent value="account" className="space-y-5 flex-1">
                <form 
                  onSubmit={(e) => {
                    e.preventDefault();
                    connectToMCP();
                  }}
                  className="space-y-5"
                >
                  <div className="space-y-2">
                    <Label htmlFor="api-key" className="text-sm font-medium">Anthropic API Key</Label>
                    <Input
                      id="api-key"
                      type="password"
                      placeholder="sk-ant-api..."
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      className="border-2 focus:border-emerald-500"
                      name="api-key"
                      autoComplete="off"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="model-select" className="text-sm font-medium">Model</Label>
                    <Select value={model} onValueChange={setModel} name="model">
                      <SelectTrigger id="model-select" className="border-2 focus:border-emerald-500">
                        <SelectValue placeholder="Select a model" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="claude-3-5-sonnet-20240620">Claude 3.5 Sonnet</SelectItem>
                        <SelectItem value="claude-3-opus-20240229">Claude 3 Opus</SelectItem>
                        <SelectItem value="claude-3-sonnet-20240229">Claude 3 Sonnet</SelectItem>
                        <SelectItem value="claude-3-haiku-20240307">Claude 3 Haiku</SelectItem>
                        <SelectItem value="claude-3-7-sonnet-20250219">Claude 3.7 Sonnet</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="max-iterations" className="text-sm font-medium">Max Tool Iterations: {maxIterations}</Label>
                    </div>
                    <Slider 
                      id="max-iterations"
                      min={5} 
                      max={30} 
                      step={1}
                      value={[maxIterations]}
                      onValueChange={(values) => setMaxIterations(values[0])}
                      className="py-2"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>5</span>
                      <span>30</span>
                    </div>
                  </div>
                  
                  <Button 
                    type="submit"
                    className="w-full mt-6 bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-2" 
                    disabled={isConnecting}
                  >
                    {isConnecting ? "Connecting..." : "Connect to Agentic AI"}
                  </Button>
                </form>
              </TabsContent>
              
              <TabsContent value="servers" className="space-y-4">
                {isConfigLoading ? (
                  <div className="flex justify-center items-center h-40">
                    <div className="w-8 h-8 border-2 border-t-blue-500 border-blue-500/30 rounded-full animate-spin"></div>
                  </div>
                ) : configError ? (
                  <div className="text-center py-4">
                    <p className="text-red-500 mb-2">{configError}</p>
                    <Button variant="outline" onClick={loadMcpConfig}>
                      Try Again
                    </Button>
                  </div>
                ) : mcpConfig ? (
                  <>
                    <div className="flex justify-between items-center">
                      <h3 className="text-base font-medium">Agentic AI Servers</h3>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => setIsAddingServer(true)}
                        disabled={isAddingServer}
                        className="text-xs"
                      >
                        Add Server
                      </Button>
                    </div>
                    
                    {isAddingServer && (
                      <div className="bg-gray-50 p-3 rounded-md border">
                        <h4 className="text-sm font-medium mb-2">Add New Server</h4>
                        <div className="space-y-2">
                          <div>
                            <Label className="text-xs">Server Name</Label>
                            <Input
                              value={newServerName}
                              onChange={(e) => setNewServerName(e.target.value)}
                              placeholder="e.g., my_server"
                              className="h-8 text-sm"
                            />
                          </div>
                          <div>
                            <Label className="text-xs">Server URL</Label>
                            <Input
                              value={newServerUrl}
                              onChange={(e) => setNewServerUrl(e.target.value)}
                              placeholder="http://server-address:port/sse"
                              className="h-8 text-sm"
                            />
                          </div>
                          <div className="flex justify-end gap-2 mt-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setIsAddingServer(false);
                                setNewServerName('');
                                setNewServerUrl('');
                              }}
                              className="h-7 text-xs"
                            >
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              onClick={addServer}
                              disabled={!newServerName.trim() || !newServerUrl.trim()}
                              className="h-7 text-xs"
                            >
                              Add
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div className="border rounded-md overflow-hidden">
                      <div className="bg-gray-50 px-4 py-2 text-sm font-medium text-gray-700 border-b flex">
                        <div className="w-1/4">Server Name</div>
                        <div className="flex-1">URL</div>
                        <div className="w-20 text-right">Actions</div>
                      </div>
                      
                      <div className="divide-y">
                        {Object.entries(mcpConfig.mcpServers).map(([serverName, server]) => (
                          <div key={serverName} className="px-4 py-2 text-sm flex items-center">
                            {isEditingServer === serverName ? (
                              <>
                                <div className="w-1/4 font-medium">{serverName}</div>
                                <div className="flex-1">
                                  <Input
                                    value={editServerUrl}
                                    onChange={(e) => setEditServerUrl(e.target.value)}
                                    className="h-7 text-xs"
                                  />
                                </div>
                                <div className="w-20 flex justify-end gap-1">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={cancelServerEdit}
                                    className="h-6 w-6 p-0"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                    </svg>
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={saveServerEdit}
                                    disabled={!editServerUrl.trim()}
                                    className="h-6 w-6 p-0 text-green-600"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  </Button>
                                </div>
                              </>
                            ) : (
                              <>
                                <div className="w-1/4 font-medium">{serverName}</div>
                                <div className="flex-1 truncate" title={server.url}>
                                  {server.url}
                                </div>
                                <div className="w-20 flex justify-end gap-1">
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => startEditServer(serverName)}
                                    className="h-6 w-6 p-0"
                                    title="Edit"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                    </svg>
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => confirmDeleteServer(serverName)}
                                    className="h-6 w-6 p-0 text-red-500"
                                    title="Delete"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                  </Button>
                                </div>
                              </>
                            )}
                          </div>
                        ))}
                        
                        {Object.keys(mcpConfig.mcpServers).length === 0 && (
                          <div className="px-4 py-3 text-center text-sm text-gray-500">
                            No servers configured. Add a server to connect.
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex justify-end mt-4">
                      <Button
                        onClick={saveMcpConfig}
                        disabled={!isConfigChanged || isConfigLoading}
                        className={isConfigChanged ? "bg-blue-600 hover:bg-blue-700" : ""}
                      >
                        {isConfigLoading ? "Saving..." : "Save Configuration"}
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Failed to load Agentic AI configuration</p>
                    <Button variant="outline" onClick={loadMcpConfig} className="mt-2">
                      Load Configuration
                    </Button>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="prompt" className="space-y-5 flex-1">
                <div className="space-y-2">
                  <Label htmlFor="system-prompt" className="text-sm font-medium">System Prompt</Label>
                  <div className="text-xs text-gray-500 mb-2">
                    This prompt will be sent to the LLM as the first message in each conversation, but won't be shown in the UI.
                  </div>
                  <Textarea
                    id="system-prompt"
                    placeholder="Enter a system prompt..."
                    value={systemPrompt}
                    onChange={(e) => setSystemPrompt(e.target.value)}
                    className="min-h-[200px] border-2 focus:border-emerald-500"
                  />
                </div>
                
                <Button 
                  onClick={() => {
                    // Save prompt to backend
                    if (ws) {
                      ws.send(JSON.stringify({
                        type: 'save_system_prompt',
                        prompt: systemPrompt
                      }));
                      
                      toast({
                        title: "System prompt saved",
                        description: "Your system prompt has been saved and will be used in future conversations.",
                        duration: 5000
                      });
                    }
                    
                    setIsSettingsOpen(false);
                  }}
                  className="w-full mt-6 bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-2"
                >
                  Save Prompt
                </Button>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>

        {/* Confirmation Dialog */}
        <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Confirm Delete</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete the server "{serverToDelete}"? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={cancelDelete}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={deleteServer}>
                Delete
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        <Card className="flex-1 mx-2 md:mx-4 my-2">
          <CardContent className="h-full flex flex-col pt-6">
            <ScrollArea ref={scrollRef} className="flex-1 pr-4">
              <div className="space-y-4">
                {!sessionId && messages.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-40 text-center">
                    <h3 className="text-lg font-medium mb-2">Welcome to Agentic AI Chat</h3>
                    <p className="text-gray-600 mb-4">Connect to Agentic AI to start chatting with AI agents equipped with powerful tools</p>
                    <Button 
                      onClick={handleOpenSettings}
                      className="bg-emerald-600 hover:bg-emerald-700"
                      ref={settingsButtonRef}
                    >
                      Connect to Agentic AI
                    </Button>
                  </div>
                )}
                
                {/* Messages */}
                {messages.map((message, index) => (
                  <div key={index}>
                    {renderMessage(message, index)}
                  </div>
                ))}
              </div>
            </ScrollArea>
            
            <div className="flex items-center gap-2 pt-4">
              <Textarea
                ref={textareaRef}
                value={input}
                onChange={handleInputChange}
                onKeyDown={handleKeyPress}
                placeholder={sessionId ? "Type a message... (Shift+Enter for new line)" : "Connect to Agentic AI to start chatting..."}
                className="flex-grow min-h-[44px] max-h-[200px] resize-none py-3 px-4"
                rows={1}
                style={{
                  height: 'auto',
                  overflow: 'hidden'
                }}
                disabled={!ws || !sessionId}
              />
              <Button 
                onClick={() => void sendMessage()}
                disabled={!input.trim() || !ws || !sessionId}
                className="bg-emerald-500 hover:bg-emerald-600"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}