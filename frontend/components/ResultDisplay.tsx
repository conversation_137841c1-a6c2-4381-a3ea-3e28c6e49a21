import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent } from './ui/card';
import { ScrollArea } from './ui/scroll-area';
import { createPortal } from 'react-dom';

interface ResultDisplayProps {
  results: string;
}

const ResultDisplay: React.FC<ResultDisplayProps> = ({ results }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const expandedContentRef = useRef<HTMLDivElement>(null);

  // Listen for custom scroll events when expanded
  useEffect(() => {
    if (isExpanded) {
      const handleCustomScroll = (e: CustomEvent) => {
        if (expandedContentRef.current) {
          expandedContentRef.current.scrollBy({
            top: e.detail.scrollAmount,
            behavior: 'smooth'
          });
        }
      };

      window.addEventListener('customScroll' as any, handleCustomScroll);
      return () => {
        window.removeEventListener('customScroll' as any, handleCustomScroll);
      };
    }
  }, [isExpanded]);

  const toggleExpand = (event: React.MouseEvent | React.TouchEvent) => {
    event.stopPropagation();
    setIsExpanded((prev) => !prev);
  };

  const formatResults = (text: string) => {
    if (!text) {
      return <div className="text-gray-500 text-center py-4">Search Result will be shown here.</div>;
    }

    try {
      const data = JSON.parse(text);
      return (
        <div>
          {data.summary && (
            <div className="mb-6 p-4 bg-gray-800 rounded-lg">
              <h3 className="font-semibold mb-2 text-white">Summary:</h3>
              <div className="text-gray-200 whitespace-pre-wrap">{data.summary}</div>
            </div>
          )}
          <h3 className="font-semibold mb-2">Results ({data.relevant_count || data.results.length} relevant):</h3>
          {data.results.map((result: any, index: number) => (
            <Card key={index} className="mb-4 bg-gray-900 text-white">
              <CardContent className="p-4">
                <p className="mb-2"><strong>File:</strong> {result.file_name}</p>
                <p className="mb-2"><strong>Text:</strong> {result.chunk_text}</p>
                {result.distance !== null && (
                  <p className="mb-2"><strong>Distance:</strong> {result.distance.toFixed(4)}</p>
                )}
                <p className="mb-2"><strong>Search Type:</strong> {result.search_type}</p>
                {result.relevance_score !== undefined && (
                  <p className="mb-2"><strong>Relevance Score:</strong> {result.relevance_score.toFixed(4)}</p>
                )}
                {result.url && (
                  <p className="mb-2">
                    <strong>URL:</strong>{' '}
                    <a href={result.url} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">
                      {result.url}
                    </a>
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      );
    } catch (error) {
      console.error('Error parsing results:', error);
      return <pre className="whitespace-pre-wrap text-red-500">Error parsing results: {error instanceof Error ? error.message : String(error)}</pre>;
    }
  };

  return (
    <div className="relative" ref={scrollAreaRef}>
      {!isExpanded ? (
        <div className="relative border rounded-md">
          <div className="sticky top-0 right-0 flex justify-end p-2 bg-white z-50 border-b">
            <button
              onClick={toggleExpand}
              onTouchEnd={toggleExpand}
              className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 active:bg-gray-200 touch-manipulation"
            >
              展开
            </button>
          </div>
          <ScrollArea className="h-[450px] px-4 pb-4">
            {formatResults(results)}
          </ScrollArea>
        </div>
      ) : null}

      {isExpanded && createPortal(
        <>
          <div className="fixed inset-0 bg-black bg-opacity-50 z-[9998]" />
          <div className="fixed inset-0 z-[9999] p-4 overflow-hidden">
            <div className="min-h-full bg-white rounded-lg relative max-w-[90%] mx-auto my-4">
              <div className="sticky top-0 right-0 flex justify-end p-2 bg-white z-50 border-b">
                <button
                  onClick={toggleExpand}
                  onTouchEnd={toggleExpand}
                  className="bg-white p-2 rounded-full shadow-md hover:bg-gray-100 active:bg-gray-200 touch-manipulation"
                >
                  收缩
                </button>
              </div>
              <div ref={expandedContentRef} className="p-4 overflow-y-auto max-h-[calc(100vh-8rem)]">
                {formatResults(results)}
              </div>
            </div>
          </div>
        </>,
        document.body
      )}
    </div>
  );
};

export default ResultDisplay;
