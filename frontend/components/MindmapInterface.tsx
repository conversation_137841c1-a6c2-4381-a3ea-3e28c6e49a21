import React, { useState, useRef, useEffect } from 'react';
import { Card } from './ui/card';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Alert } from './ui/alert';
import * as d3 from 'd3';
import { fetchEnvConfig } from '@/lib/api';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { ResizableBox } from 'react-resizable';
import 'react-resizable/css/styles.css';

interface MindmapNode {
    id: string;
    label: string;
    level: number;
    parent?: string;
    value?: number;
}

interface MindmapData {
    nodes: MindmapNode[];
}

export default function MindmapInterface() {
    const [topic, setTopic] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [backendUrl, setBackendUrl] = useState('');
    const [mindmapReady, setMindmapReady] = useState(false);
    const [height, setHeight] = useState(600);
    const mindmapRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const initConfig = async () => {
            try {
                const config = await fetchEnvConfig();
                setBackendUrl(config.backendUrl);
            } catch (err) {
                console.error('Failed to fetch config:', err);
                setError('Failed to initialize configuration');
            }
        };
        initConfig();
    }, []);

    const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && topic && !loading) {
            generateMindmap();
        }
    };

    const handleResize = (e: React.SyntheticEvent, { size }: { size: { width: number; height: number } }) => {
        setHeight(size.height);
    };

    const generateMindmap = async () => {
        if (!backendUrl) {
            setError('Backend URL not configured');
            return;
        }

        try {
            setLoading(true);
            setError('');
            setMindmapReady(false);
            const response = await fetch(`${backendUrl}/mindmap/generate`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ topic })
            });

            if (!response.ok) throw new Error('Failed to generate mindmap');
            
            const data = await response.json() as MindmapData;
            renderMindmap(data);
            setMindmapReady(true);
            if (mindmapRef.current) {
                mindmapRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        } catch (err: unknown) {
            if (err instanceof Error) {
                setError(err.message);
            } else {
                setError('An unknown error occurred');
            }
        } finally {
            setLoading(false);
        }
    };

    const renderMindmap = (data: MindmapData) => {
        d3.select("#mindmap-svg").selectAll("*").remove();

        const container = d3.select(".mindmap-container").node() as HTMLElement;
        const width = container.clientWidth;
        const height = container.clientHeight;
        
        const margin = {
            top: Math.max(30, height * 0.05),
            right: Math.max(60, width * 0.1),
            bottom: Math.max(30, height * 0.05),
            left: Math.max(60, width * 0.1)
        };

        const svg = d3.select("#mindmap-svg")
            .attr("width", width)
            .attr("height", height)
            .attr("viewBox", `0 0 ${width} ${height}`)
            .attr("preserveAspectRatio", "xMidYMid meet");

        svg.selectAll("*").remove();
        const g = svg.append("g")
            .attr("transform", `translate(${margin.left},${margin.top})`);

        const stratify = d3.stratify<MindmapNode>()
            .id(d => d.id)
            .parentId(d => d.parent || null);

        const root = stratify(data.nodes)
            .sum(d => d.value || 0);

        const treeLayout = d3.tree<MindmapNode>()
            .size([height - margin.top - margin.bottom, width - margin.left - margin.right])
            .separation((a, b) => (a.parent === b.parent ? 1.5 : 2.5));

        const nodes = treeLayout(root);

        // Add links with animation
        const links = g.selectAll<SVGPathElement, d3.HierarchyPointLink<MindmapNode>>(".link")
            .data(nodes.links())
            .join("path")
            .attr("class", "link")
            .attr("d", d3.linkHorizontal<d3.HierarchyPointLink<MindmapNode>, d3.HierarchyPointNode<MindmapNode>>()
                .x(d => d.y)
                .y(d => d.x))
            .style("fill", "none")
            .style("stroke", "#94a3b8")
            .style("stroke-width", "2px")
            .style("opacity", 0);

        links.transition()
            .duration(800)
            .style("opacity", 1);

        // Add nodes with animation
        const nodeGroups = g.selectAll<SVGGElement, d3.HierarchyPointNode<MindmapNode>>(".node")
            .data(nodes.descendants())
            .join("g")
            .attr("class", "node")
            .attr("transform", d => `translate(${d.y},${d.x})`)
            .style("opacity", 0);

        nodeGroups.transition()
            .duration(800)
            .style("opacity", 1);

        // Add node circles with hover effect
        nodeGroups.append("circle")
            .attr("r", 6)
            .style("fill", "#0ea5e9")
            .style("stroke", "#0284c7")
            .style("stroke-width", "2px")
            .style("cursor", "pointer")
            .on("mouseover", function(this: SVGCircleElement) {
                d3.select(this)
                    .transition()
                    .duration(200)
                    .attr("r", 8)
                    .style("fill", "#38bdf8");
            })
            .on("mouseout", function(this: SVGCircleElement) {
                d3.select(this)
                    .transition()
                    .duration(200)
                    .attr("r", 6)
                    .style("fill", "#0ea5e9");
            });

        // Add labels with animation
        const labels = nodeGroups.append("text")
            .attr("dy", ".35em")
            .attr("x", d => d.children ? -16 : 16)
            .style("text-anchor", d => d.children ? "end" : "start")
            .style("font-size", "14px")
            .style("fill", "#1e293b")
            .text(d => d.data.label)
            .style("opacity", 0)
            .each(function(this: SVGTextElement) {
                const bbox = this.getBBox();
                const padding = 4;
                const parent = this.parentElement;
                if (parent) {
                    d3.select(parent)
                        .insert("rect", "text")
                        .attr("x", bbox.x - padding)
                        .attr("y", bbox.y - padding)
                        .attr("width", bbox.width + (padding * 2))
                        .attr("height", bbox.height + (padding * 2))
                        .attr("fill", "white")
                        .attr("fill-opacity", 0.9)
                        .attr("rx", 4);
                }
            });

        labels.transition()
            .duration(800)
            .style("opacity", 1);
    };

    return (
        <Card className="p-6 space-y-6">
            <div className="space-y-4">
                <h2 className="text-2xl font-bold text-slate-900">AI Mindmap Generator</h2>
                <div className="flex gap-4">
                    <Input
                        placeholder="Enter a topic for your mindmap..."
                        value={topic}
                        onChange={(e) => setTopic(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="flex-1"
                    />
                    <Button 
                        onClick={generateMindmap}
                        disabled={loading || !topic}
                        className="min-w-[120px]"
                    >
                        {loading ? (
                            <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                className="flex items-center gap-2"
                            >
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span>Generating...</span>
                            </motion.div>
                        ) : (
                            'Generate'
                        )}
                    </Button>
                </div>
                <AnimatePresence>
                    {error && (
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                        >
                            <Alert variant="destructive">
                                {error}
                            </Alert>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
            
            <ResizableBox
                width={Infinity}
                height={height}
                minConstraints={[Infinity, 400]}
                maxConstraints={[Infinity, 800]}
                onResize={handleResize}
                resizeHandles={['s']}
                handle={<div className="custom-handle" />}
                className="w-full"
            >
                <motion.div 
                    ref={mindmapRef}
                    className="relative w-full h-full border rounded-lg bg-white overflow-hidden"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: mindmapReady ? 1 : 0 }}
                    transition={{ duration: 0.3 }}
                >
                    {loading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm z-10">
                            <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                            >
                                <Loader2 className="h-8 w-8 text-blue-500" />
                            </motion.div>
                        </div>
                    )}
                    <TransformWrapper
                        initialScale={1}
                        minScale={0.1}
                        maxScale={4}
                        limitToBounds={false}
                        centerOnInit={true}
                        wheel={{ smoothStep: 0.005 }}
                    >
                        {({ zoomIn, zoomOut, resetTransform }) => (
                            <div className="relative w-full h-full">
                                <div className="absolute top-4 left-4 z-10 flex gap-2">
                                    <Button
                                        variant="secondary"
                                        size="icon"
                                        onClick={() => zoomIn()}
                                        className="bg-white/50 hover:bg-white/75"
                                    >
                                        <ZoomIn className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant="secondary"
                                        size="icon"
                                        onClick={() => zoomOut()}
                                        className="bg-white/50 hover:bg-white/75"
                                    >
                                        <ZoomOut className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant="secondary"
                                        size="icon"
                                        onClick={() => resetTransform()}
                                        className="bg-white/50 hover:bg-white/75"
                                    >
                                        <RotateCcw className="h-4 w-4" />
                                    </Button>
                                </div>
                                <TransformComponent 
                                    wrapperStyle={{
                                        width: '100%',
                                        height: '100%'
                                    }}
                                    contentStyle={{
                                        width: '100%',
                                        height: '100%'
                                    }}
                                >
                                    <div className="mindmap-container w-full h-full flex items-center justify-center">
                                        <svg 
                                            id="mindmap-svg" 
                                            style={{
                                                width: '100%',
                                                height: '100%',
                                                display: 'block'
                                            }}
                                        />
                                    </div>
                                </TransformComponent>
                            </div>
                        )}
                    </TransformWrapper>
                    <div className="resize-handle absolute bottom-0 left-0 right-0 h-4 bg-gray-100 bg-opacity-50 cursor-ns-resize flex items-center justify-center">
                        <div className="w-16 h-1 bg-gray-300 rounded-full"></div>
                    </div>
                </motion.div>
            </ResizableBox>
        </Card>
    );
} 