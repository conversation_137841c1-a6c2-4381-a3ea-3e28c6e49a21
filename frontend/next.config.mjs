/** @type {import('next').NextConfig} */
const nextConfig = {
	logging: {
		fetches: {
		  fullUrl: true,
		},
		level: process.env.DEBUG_CONFIG === 'true' ? 'info' : 'error'
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
	reactStrictMode: true,
	env: {
		PORT: '8505', // Set your desired port here
	},
	productionBrowserSourceMaps: true,
	webpack: (config) => {
		config.devtool = 'source-map';
		return config;
	},
	async headers() {
		return [
			{
				source: '/api/:path*',
				headers: [
					{
						key: 'x-client-ip',
						value: '${req.socket.remoteAddress}'
					}
				]
			}
		];
	}
}

export default nextConfig;

