// Add type definitions at the top
interface EnvConfig {
  backendUrl: string;
  llmModels: Record<string, string>;
  localLLMModels: string;
}

// Helper function to check if an IP is internal
function isInternalIP(ip: string): boolean {
  try {
    // Remove port if present
    ip = ip.split(':')[0];
    
    // Check localhost
    if (ip === 'localhost' || ip === '127.0.0.1') return true;
    
    // Check private IP ranges
    const parts = ip.split('.');
    if (parts.length !== 4) return false;
    
    const firstOctet = parseInt(parts[0]);
    const secondOctet = parseInt(parts[1]);
    
    // // 10.0.0.0 - **************
    // if (firstOctet === 10) return true;
    
    // ********** - **************
    if (firstOctet === 172 && secondOctet >= 16 && secondOctet <= 31) return true;
    
    // *********** - ***************
    if (firstOctet === 192 && secondOctet === 168) return true;
    
    return false;
  } catch (error) {
    console.error('[API] Error checking IP:', error);
    return false;
  }
}

let lastConfigCheck = 0;
const CONFIG_CHECK_INTERVAL = process.env.NODE_ENV === 'development' ? 3000 : 15000; // More frequent checks
let cachedConfig: EnvConfig | null = null;
let consecutiveFailures = 0;
const MAX_FAILURES = 3;

// Debug logging helper
const debug = (message: string, data?: any) => {
  if (process.env.DEBUG_CONFIG === 'true') {
    if (data) {
      console.log(`[API] ${message}`, data);
    } else {
      console.log(`[API] ${message}`);
    }
  }
};

// Add retry logic for fetch operations
async function fetchWithRetry(url: string, options: RequestInit = {}, retries = 2): Promise<Response> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      consecutiveFailures++;
      if (consecutiveFailures >= MAX_FAILURES || retries > 0) {
        debug(`Fetch failed (${consecutiveFailures} consecutive failures), retrying...`);
        await refreshConfig();
        await new Promise(resolve => setTimeout(resolve, 1000));
        return fetchWithRetry(url, options, retries - 1);
      }
    } else {
      consecutiveFailures = 0;
    }
    
    return response;
  } catch (error) {
    consecutiveFailures++;
    if (consecutiveFailures >= MAX_FAILURES || retries > 0) {
      debug(`Fetch error (${consecutiveFailures} consecutive failures), retrying...`);
      await refreshConfig();
      await new Promise(resolve => setTimeout(resolve, 1000));
      return fetchWithRetry(url, options, retries - 1);
    }
    throw error;
  }
}

export async function fetchEnvConfig(): Promise<EnvConfig> {
  try {
    const now = Date.now();
    
    // Force refresh if we have consecutive failures
    if (!cachedConfig || consecutiveFailures >= MAX_FAILURES || (now - lastConfigCheck > CONFIG_CHECK_INTERVAL)) {
      debug('Fetching fresh config...');
      
      const response = await fetchWithRetry(`/api/config?t=${now}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        }
      });
      
      const data = await response.json();
      
      if (data.backendUrl) {
        // Check if we should use primary or secondary URL based on network environment
        const urlObj = new URL(data.backendUrl);
        const isInternal = isInternalIP(urlObj.hostname);
        
        // If external access and secondary URL is available, use it
        if (!isInternal && process.env.NEXT_PUBLIC_SECONDARY_BACKEND_URL) {
          data.backendUrl = process.env.NEXT_PUBLIC_SECONDARY_BACKEND_URL;
          debug('Using secondary backend URL for external access');
        }
        
        // If URL changed, log it
        if (cachedConfig && cachedConfig.backendUrl !== data.backendUrl) {
          debug(`Backend URL changed from ${cachedConfig.backendUrl} to ${data.backendUrl}`);
        }
        
        debug('Config updated:', {
          backendUrl: data.backendUrl,
          modelsAvailable: Object.keys(data.llmModels || {}),
          localLLMConfig: data.localLLMModels,
          isInternalNetwork: isInternal
        });
        
        cachedConfig = data;
        lastConfigCheck = now;
        consecutiveFailures = 0;  // Reset failures on successful update
      }
    }
    
    return cachedConfig || {
      backendUrl: process.env.NEXT_PUBLIC_PRIMARY_BACKEND_URL || 'http://localhost:3201',
      llmModels: {
        openai: 'gpt-4o-mini',
        anthropic: 'claude-3-5-sonnet',
        openrouter: 'meta-llama/llama-3.2-90b-vision-instruct'
      },
      localLLMModels: '{}'
    };
  } catch (error) {
    console.error('[API] Error fetching config:', error);
    // Clear cache on error to force refresh next time
    cachedConfig = null;
    lastConfigCheck = 0;
    return {
      backendUrl: process.env.NEXT_PUBLIC_SECONDARY_BACKEND_URL || 'http://localhost:3201',
      llmModels: {
        openai: 'gpt-4o-mini',
        anthropic: 'claude-3-5-sonnet',
        openrouter: 'meta-llama/llama-3.2-90b-vision-instruct'
      },
      localLLMModels: '{}'
    };
  }
}

// Function to force refresh config
export async function refreshConfig() {
  lastConfigCheck = 0;
  cachedConfig = null;
  return await fetchEnvConfig();
}

// Set up polling in production mode with debug control
if (typeof window !== 'undefined') {
  setInterval(async () => {
    debug('Polling for config updates...');
    await fetchEnvConfig();
  }, CONFIG_CHECK_INTERVAL);
}

// Ensure first load immediately get config
if (typeof window !== 'undefined') {
  fetchEnvConfig().catch(console.error);
}

// Helper function to make API calls with the current backend URL
export async function makeApiCall(endpoint: string, options: RequestInit = {}): Promise<Response> {
  const config = await fetchEnvConfig();
  const url = `${config.backendUrl}${endpoint}`;
  return fetchWithRetry(url, options);
}
 