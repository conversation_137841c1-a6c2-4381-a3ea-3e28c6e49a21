{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS='--inspect' next dev -p 8505", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@tsparticles/engine": "^3.7.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.7.1", "@types/animejs": "^3.1.12", "@types/axios": "^0.14.0", "@types/d3": "^7.4.3", "@types/react-resizable": "^3.0.8", "@types/react-syntax-highlighter": "^15.5.13", "animejs": "^3.2.2", "axios": "^1.7.7", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "d3": "^7.9.0", "dotenv": "^16.4.5", "framer-motion": "^11.11.17", "highlight.js": "^11.10.0", "katex": "^0.16.11", "lucide-react": "^0.445.0", "marked": "^15.0.1", "nanoid": "^5.0.8", "next": "14.2.15", "next-themes": "^0.4.4", "openai": "^4.72.0", "react": "^18", "react-dom": "^18", "react-katex": "^3.0.1", "react-latex-next": "^3.0.0", "react-markdown": "^9.0.1", "react-resizable": "^3.0.5", "react-syntax-highlighter": "^15.6.1", "react-zoom-pan-pinch": "^3.6.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@swc/jest": "^0.2.36", "@tailwindcss/typography": "^0.5.16", "@types/canvas-confetti": "^1.6.4", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-katex": "^3.0.4", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.2.15", "jest": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "jest": {"testEnvironment": "node", "transform": {"^.+\\.(t|j)sx?$": "@swc/jest"}, "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1"}}}