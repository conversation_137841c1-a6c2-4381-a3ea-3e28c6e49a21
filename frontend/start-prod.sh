#!/bin/bash

# Ensure .env exists and is readable
echo "Checking environment files..."
if [ -f .env.local ]; then
  echo "Found .env.local, copying to .env..."
  cp .env.local .env
elif [ ! -f .env ]; then
  echo "No .env file found, creating from template..."
  echo "NEXT_PUBLIC_BACKEND_API_URL=http://localhost:3201" > .env
  echo "NEXT_PUBLIC_LLM_MODELS='{\"openai\":\"GPT-4o-mini\",\"anthropic\":\"Claude-3-5-Sonnet\",\"openrouter\":\"Qwen-2.5-Coder-32B\"}'" >> .env
fi

# Ensure proper permissions
chmod 644 .env
echo "Environment file permissions set"

# Build the application
echo "Building Next.js application..."
npx next build

# Start the server with debug logging
echo "Starting production server..."
#NODE_ENV=production DEBUG=* NEXT_PUBLIC_CONFIG_DEBUG=true npx next start -p 8505 
NODE_ENV=development npx next start -p 8505
