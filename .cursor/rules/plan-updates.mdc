---
description: Guidelines for making surgical, focused updates to plan files
globs: *.md
---
# Plan Updates Rule
# Guidelines for making surgical, focused updates to plan files

<rule>
name: plan_updates
description: Rules for maintaining precision in plan updates
filters:
  - type: file_extension
    pattern: "\\.md$"
  - type: content
    pattern: "(Implementation Steps|Phase [0-9]|Current Status)"
actions:
  - type: suggest
    message: |
      Plan Update Guidelines:

      1. Surgical Focus:
         a) Phase Isolation:
            - Only update the current active phase
            - Leave future phases unchanged
            - Preserve original structure of other phases
            - Mark interrupted tasks as [INCOMPLETE]

         b) Task Status:
            - Use [x] for completed tasks
            - Use [ ] for pending tasks
            - Use [INCOMPLETE] for interrupted tasks
            - Maintain task hierarchy and indentation

         c) Task Additions:
            - Add new tasks only to current phase
            - Place new tasks in logical sequence
            - Maintain consistent formatting
            - Preserve task dependencies

         d) Task Updates:
            - Update only affected tasks
            - Keep unaffected tasks unchanged
            - Maintain original task descriptions
            - Preserve task IDs and references

      2. Change Documentation:
         a) Status Updates:
            - Document changes in updates.md
            - Reference specific tasks modified
            - Explain interruptions
            - Note any blockers

         b) Progress Tracking:
            - Update only completed tasks
            - Maintain task completion order
            - Document partial completions
            - Note dependencies affected

      3. Version Control:
         a) Commit Strategy:
            - Commit only changed phase
            - Use clear commit messages
            - Reference task IDs
            - Note incomplete tasks

         b) Review Process:
            - Verify only intended changes
            - Check phase isolation
            - Validate task status
            - Confirm formatting

      Remember:
      - Focus on current phase only
      - Document interrupted tasks
      - Maintain plan structure
      - Keep future phases intact
      - Update status in sync

examples:
  - input: |
      ### Phase 1: Foundation
      - [x] Task 1.1
      - [ ] Task 1.2
      - [INCOMPLETE] Task 1.3
      
      ### Phase 2: Implementation
      [Original content preserved]

  - input: |
      Commit message:
      plan(task-123): update Phase 1 tasks, mark Task 1.3 incomplete

metadata:
  priority: high
  version: 1.0
  tags:
    - planning
    - documentation
    - task-management
</rule> 