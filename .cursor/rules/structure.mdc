# Project Structure

```
.
|-- .ai
|   |-- end-session
|   `-- start-session
|-- AWS_apikeysetup.md
|-- backend
|   |-- background.png
|   |-- backup
|   |   |-- DiT2212.09748v2.pdf
|   |   |-- late-Chunking2409.04701v1.pdf
|   |   |-- mcp_client_v1.py
|   |   |-- mom_summary.pdf
|   |   `-- Rakuten202312.pdf
|   |-- chathist
|   |-- ColpaliProcessor.py
|   |-- fluxplorer.py
|   |-- image2base64.py
|   |-- images
|   |   |-- DiT2212.09748v2.pdf_0.png
|   |   |-- DiT2212.09748v2.pdf_1.png
|   |   `-- DiT2212.09748v2.pdf_2.png
|   |-- __init__.py
|   |-- lists.py
|   |-- local_chat_handler.ollamaapi
|   |-- local_chat_handler.py
|   |-- log
|   |   |-- app.log
|   |   |-- app.log.2025-03-22
|   |   |-- app.log.2025-03-23
|   |   |-- app.log.2025-03-25
|   |   |-- app.log.2025-03-26
|   |   |-- app.log.2025-03-29
|   |   |-- app.log.2025-04-01
|   |   |-- app.log.2025-04-02
|   |   |-- mcp_client.log
|   |   |-- websocket.log
|   |   |-- websocket.log.2025-03-18
|   |   |-- websocket.log.2025-03-19
|   |   |-- websocket.log.2025-03-20
|   |   |-- websocket.log.2025-03-22
|   |   |-- websocket.log.2025-03-23
|   |   |-- websocket.log.2025-03-29
|   |   `-- websocket.log.2025-04-02
|   |-- main_v2.py
|   |-- mcp_chat_handler.py
|   |-- mcp_client.py
|   |-- mindmap_handler.py
|   |-- models
|   |   `-- jina_embedding_v3 -> ../../model/
|   |-- PdfImage.py
|   |-- prompts.json
|   |-- prompts.json.bak
|   |-- prompts_template.json
|   |-- __pycache__
|   |-- requirements.txt
|   |-- search_engine.py
|   `-- websocket_handler.py
|-- .byaldi
|-- CHANGELOG.md
|-- chathist
|-- .cursor
|   |-- rules
|   |   |-- agent.mdc
|   |   |-- changelog.mdc
|   |   |-- cursor_rules_location.mdc
|   |   |-- folder_structure.mdc
|   |   |-- global.mdc
|   |   |-- good-behaviour.mdc
|   |   |-- memory-management.mdc
|   |   |-- plan-updates.mdc
|   |   |-- problem-solving.mdc
|   |   |-- project-dedicated.mdc
|   |   `-- structure.mdc
|   `-- script
|       |-- update_structure_folder.sh
|       `-- update_structure.sh
|-- .cursorignore
|-- .cursorrules
|-- docs
|   |-- templates
|   |   |-- task-plan-template.md
|   |   `-- task-updates-template.md
|   `-- working-memory
|       |-- closed
|       |   `-- markdown-latex-enhance-20250403
|       |       |-- plan.md
|       |       |-- README.md
|       |       `-- updates.md
|       |-- done
|       |   `-- anthropic-token-efficient-20230404
|       |       |-- plan.md
|       |       |-- README.md
|       |       `-- updates.md
|       `-- open
|-- frontend
|   |-- app
|   |   |-- api
|   |   |   `-- config
|   |   |       |-- route.ts
|   |   |       |-- state.ts
|   |   |       `-- __tests__
|   |   |           `-- route.test.ts
|   |   |-- favicon.ico
|   |   |-- fonts
|   |   |   |-- GeistMonoVF.woff
|   |   |   `-- GeistVF.woff
|   |   |-- globals.css
|   |   |-- layout.tsx
|   |   |-- page.tsx
|   |   `-- signup
|   |-- components
|   |   |-- ai-product-portal.tsx
|   |   |-- ChatInterface.tsx
|   |   |-- ColpaliInterface.tsx
|   |   |-- EnhancedZoomableImage.tsx
|   |   |-- FileUpload.tsx
|   |   |-- FluxplorerInterface.tsx
|   |   |-- HelpChatbot.tsx
|   |   |-- LocalChat.tsx
|   |   |-- MarkdownRenderer.tsx
|   |   |-- MCPChat.tsx
|   |   |-- MindmapInterface.tsx
|   |   |-- MultiModelChat.tsx
|   |   |-- ResultDisplay.tsx
|   |   `-- ui
|   |       |-- accordion.tsx
|   |       |-- alert.tsx
|   |       |-- avatar.tsx
|   |       |-- badge.tsx
|   |       |-- button.tsx
|   |       |-- card.tsx
|   |       |-- checkbox.tsx
|   |       |-- dialog.tsx
|   |       |-- input.tsx
|   |       |-- label.tsx
|   |       |-- progress.tsx
|   |       |-- scroll-area.tsx
|   |       |-- select.tsx
|   |       |-- slider.tsx
|   |       |-- sparkles.tsx
|   |       |-- switch.tsx
|   |       |-- tabs.tsx
|   |       |-- textarea.tsx
|   |       |-- toaster.tsx
|   |       |-- toast.tsx
|   |       `-- waves-background.tsx
|   |-- components.json
|   |-- .env
|   |-- .eslintrc.json
|   |-- hooks
|   |   `-- use-toast.ts
|   |-- images
|   |   |-- ai-agent.svg
|   |   |-- tool.svg
|   |   `-- user.svg
|   |-- lib
|   |   |-- api.ts
|   |   |-- background_image.ts
|   |   |-- constants.ts
|   |   `-- utils.ts
|   |-- .next
|   |   |-- app-build-manifest.json
|   |   |-- app-path-routes-manifest.json
|   |   |-- BUILD_ID
|   |   |-- build-manifest.json
|   |   |-- cache
|   |   |   |-- eslint
|   |   |   |   `-- .cache_173i68p
|   |   |   |-- fetch-cache
|   |   |   |   `-- 1f85d7744d4bcb6f809c56b5a9c231594c457094e98f223f51769327a4262905
|   |   |   |-- images
|   |   |   |   |-- 5n70hcvUCOShVezf4VhXDuKV044iA2K22rBe+eLzGE4=
|   |   |   |   |   `-- 60.1743596772279.WjzpzeQtCOQFDUlrGOtAEIFJLKQ27C3TxC5et319Usw=.ico
|   |   |   |   |-- JxfiDdH+cX2PjA5Btzw46NNcEveV7GaTPpPFKXKAlNQ=
|   |   |   |   |   `-- 60.1743641040575.WjzpzeQtCOQFDUlrGOtAEIFJLKQ27C3TxC5et319Usw=.ico
|   |   |   |   |-- p-XhPjYLa2xjTLNhcFksWA6gvhsQzuGQWiDC9LAbRMc=
|   |   |   |   |   `-- 60.1740570444773.WjzpzeQtCOQFDUlrGOtAEIFJLKQ27C3TxC5et319Usw=.ico
|   |   |   |   `-- -RSAFvQOS--p33RVxIA7bvoIgdkmcVbjmJIAwXDdxjs=
|   |   |   |       `-- 60.1743681625655.WjzpzeQtCOQFDUlrGOtAEIFJLKQ27C3TxC5et319Usw=.ico
|   |   |   |-- swc
|   |   |   |   `-- plugins
|   |   |   |       `-- v7_linux_aarch64_0.106.15
|   |   |   |-- .tsbuildinfo
|   |   |   `-- webpack
|   |   |       |-- client-development
|   |   |       |   |-- 0.pack.gz
|   |   |       |   |-- 10.pack.gz
|   |   |       |   |-- 11.pack.gz
|   |   |       |   |-- 12.pack.gz
|   |   |       |   |-- 13.pack.gz
|   |   |       |   |-- 14.pack.gz
|   |   |       |   |-- 15.pack.gz
|   |   |       |   |-- 16.pack.gz
|   |   |       |   |-- 17.pack.gz
|   |   |       |   |-- 18.pack.gz
|   |   |       |   |-- 19.pack.gz
|   |   |       |   |-- 1.pack.gz
|   |   |       |   |-- 20.pack.gz
|   |   |       |   |-- 21.pack.gz
|   |   |       |   |-- 22.pack.gz
|   |   |       |   |-- 2.pack.gz
|   |   |       |   |-- 3.pack.gz
|   |   |       |   |-- 4.pack.gz
|   |   |       |   |-- 5.pack.gz
|   |   |       |   |-- 6.pack.gz
|   |   |       |   |-- 7.pack.gz
|   |   |       |   |-- 8.pack.gz
|   |   |       |   |-- 9.pack.gz
|   |   |       |   |-- index.pack.gz
|   |   |       |   `-- index.pack.gz.old
|   |   |       |-- client-development-fallback
|   |   |       |   |-- 0.pack.gz
|   |   |       |   |-- 1.pack.gz
|   |   |       |   |-- 2.pack.gz
|   |   |       |   `-- index.pack.gz.old
|   |   |       |-- client-production
|   |   |       |   |-- 0.pack
|   |   |       |   |-- 10.pack
|   |   |       |   |-- 11.pack
|   |   |       |   |-- 12.pack
|   |   |       |   |-- 13.pack
|   |   |       |   |-- 14.pack
|   |   |       |   |-- 15.pack
|   |   |       |   |-- 16.pack
|   |   |       |   |-- 17.pack
|   |   |       |   |-- 18.pack
|   |   |       |   |-- 19.pack
|   |   |       |   |-- 1.pack
|   |   |       |   |-- 20.pack
|   |   |       |   |-- 21.pack
|   |   |       |   |-- 22.pack
|   |   |       |   |-- 23.pack
|   |   |       |   |-- 24.pack
|   |   |       |   |-- 25.pack
|   |   |       |   |-- 26.pack
|   |   |       |   |-- 27.pack
|   |   |       |   |-- 28.pack
|   |   |       |   |-- 29.pack
|   |   |       |   |-- 2.pack
|   |   |       |   |-- 30.pack
|   |   |       |   |-- 31.pack
|   |   |       |   |-- 32.pack
|   |   |       |   |-- 33.pack
|   |   |       |   |-- 34.pack
|   |   |       |   |-- 35.pack
|   |   |       |   |-- 36.pack
|   |   |       |   |-- 37.pack
|   |   |       |   |-- 38.pack
|   |   |       |   |-- 39.pack
|   |   |       |   |-- 3.pack
|   |   |       |   |-- 40.pack
|   |   |       |   |-- 41.pack
|   |   |       |   |-- 42.pack
|   |   |       |   |-- 43.pack
|   |   |       |   |-- 44.pack
|   |   |       |   |-- 45.pack
|   |   |       |   |-- 46.pack
|   |   |       |   |-- 47.pack
|   |   |       |   |-- 48.pack
|   |   |       |   |-- 49.pack
|   |   |       |   |-- 4.pack
|   |   |       |   |-- 50.pack
|   |   |       |   |-- 51.pack
|   |   |       |   |-- 52.pack
|   |   |       |   |-- 53.pack
|   |   |       |   |-- 54.pack
|   |   |       |   |-- 55.pack
|   |   |       |   |-- 56.pack
|   |   |       |   |-- 57.pack
|   |   |       |   |-- 58.pack
|   |   |       |   |-- 59.pack
|   |   |       |   |-- 5.pack
|   |   |       |   |-- 60.pack
|   |   |       |   |-- 61.pack
|   |   |       |   |-- 62.pack
|   |   |       |   |-- 63.pack
|   |   |       |   |-- 64.pack
|   |   |       |   |-- 65.pack
|   |   |       |   |-- 66.pack
|   |   |       |   |-- 67.pack
|   |   |       |   |-- 68.pack
|   |   |       |   |-- 6.pack
|   |   |       |   |-- 7.pack
|   |   |       |   |-- 8.pack
|   |   |       |   |-- 9.pack
|   |   |       |   |-- index.pack
|   |   |       |   `-- index.pack.old
|   |   |       |-- edge-server-production
|   |   |       |   |-- 0.pack
|   |   |       |   |-- index.pack
|   |   |       |   `-- index.pack.old
|   |   |       |-- server-development
|   |   |       |   |-- 0.pack.gz
|   |   |       |   |-- 10.pack.gz
|   |   |       |   |-- 11.pack.gz
|   |   |       |   |-- 12.pack.gz
|   |   |       |   |-- 13.pack.gz
|   |   |       |   |-- 14.pack.gz
|   |   |       |   |-- 15.pack.gz
|   |   |       |   |-- 16.pack.gz
|   |   |       |   |-- 17.pack.gz
|   |   |       |   |-- 18.pack.gz
|   |   |       |   |-- 19.pack.gz
|   |   |       |   |-- 1.pack.gz
|   |   |       |   |-- 20.pack.gz
|   |   |       |   |-- 21.pack.gz
|   |   |       |   |-- 2.pack.gz
|   |   |       |   |-- 3.pack.gz
|   |   |       |   |-- 4.pack.gz
|   |   |       |   |-- 5.pack.gz
|   |   |       |   |-- 6.pack.gz
|   |   |       |   |-- 7.pack.gz
|   |   |       |   |-- 8.pack.gz
|   |   |       |   |-- 9.pack.gz
|   |   |       |   |-- index.pack.gz
|   |   |       |   `-- index.pack.gz.old
|   |   |       `-- server-production
|   |   |           |-- 0.pack
|   |   |           |-- 10.pack
|   |   |           |-- 11.pack
|   |   |           |-- 12.pack
|   |   |           |-- 13.pack
|   |   |           |-- 14.pack
|   |   |           |-- 15.pack
|   |   |           |-- 16.pack
|   |   |           |-- 17.pack
|   |   |           |-- 18.pack
|   |   |           |-- 19.pack
|   |   |           |-- 1.pack
|   |   |           |-- 20.pack
|   |   |           |-- 21.pack
|   |   |           |-- 22.pack
|   |   |           |-- 23.pack
|   |   |           |-- 24.pack
|   |   |           |-- 25.pack
|   |   |           |-- 26.pack
|   |   |           |-- 27.pack
|   |   |           |-- 28.pack
|   |   |           |-- 29.pack
|   |   |           |-- 2.pack
|   |   |           |-- 30.pack
|   |   |           |-- 31.pack
|   |   |           |-- 32.pack
|   |   |           |-- 33.pack
|   |   |           |-- 34.pack
|   |   |           |-- 35.pack
|   |   |           |-- 36.pack
|   |   |           |-- 37.pack
|   |   |           |-- 38.pack
|   |   |           |-- 39.pack
|   |   |           |-- 3.pack
|   |   |           |-- 40.pack
|   |   |           |-- 4.pack
|   |   |           |-- 5.pack
|   |   |           |-- 6.pack
|   |   |           |-- 7.pack
|   |   |           |-- 8.pack
|   |   |           |-- 9.pack
|   |   |           |-- index.pack
|   |   |           `-- index.pack.old
|   |   |-- export-marker.json
|   |   |-- images-manifest.json
|   |   |-- next-minimal-server.js.nft.json
|   |   |-- next-server.js.nft.json
|   |   |-- package.json
|   |   |-- prerender-manifest.json
|   |   |-- react-loadable-manifest.json
|   |   |-- required-server-files.json
|   |   |-- routes-manifest.json
|   |   |-- server
|   |   |   |-- app
|   |   |   |   |-- api
|   |   |   |   |   `-- config
|   |   |   |   |       |-- route.js
|   |   |   |   |       |-- route.js.map
|   |   |   |   |       `-- route.js.nft.json
|   |   |   |   |-- favicon.ico
|   |   |   |   |   |-- route.js
|   |   |   |   |   |-- route.js.map
|   |   |   |   |   `-- route.js.nft.json
|   |   |   |   |-- favicon.ico.body
|   |   |   |   |-- favicon.ico.meta
|   |   |   |   |-- index.html
|   |   |   |   |-- index.meta
|   |   |   |   |-- index.rsc
|   |   |   |   |-- _not-found
|   |   |   |   |   |-- page_client-reference-manifest.js
|   |   |   |   |   |-- page.js
|   |   |   |   |   |-- page.js.map
|   |   |   |   |   `-- page.js.nft.json
|   |   |   |   |-- _not-found.html
|   |   |   |   |-- _not-found.meta
|   |   |   |   |-- _not-found.rsc
|   |   |   |   |-- page_client-reference-manifest.js
|   |   |   |   |-- page.js
|   |   |   |   |-- page.js.map
|   |   |   |   `-- page.js.nft.json
|   |   |   |-- app-paths-manifest.json
|   |   |   |-- chunks
|   |   |   |   |-- 161.js
|   |   |   |   |-- 161.js.map
|   |   |   |   |-- 349.js
|   |   |   |   |-- 349.js.map
|   |   |   |   |-- 379.js
|   |   |   |   |-- 379.js.map
|   |   |   |   `-- font-manifest.json
|   |   |   |-- font-manifest.json
|   |   |   |-- functions-config-manifest.json
|   |   |   |-- interception-route-rewrite-manifest.js
|   |   |   |-- middleware-build-manifest.js
|   |   |   |-- middleware-manifest.json
|   |   |   |-- middleware-react-loadable-manifest.js
|   |   |   |-- next-font-manifest.js
|   |   |   |-- next-font-manifest.json
|   |   |   |-- pages
|   |   |   |   |-- 404.html
|   |   |   |   |-- 500.html
|   |   |   |   |-- _app.js
|   |   |   |   |-- _app.js.map
|   |   |   |   |-- _app.js.nft.json
|   |   |   |   |-- _document.js
|   |   |   |   |-- _document.js.map
|   |   |   |   |-- _document.js.nft.json
|   |   |   |   |-- _error.js
|   |   |   |   |-- _error.js.map
|   |   |   |   `-- _error.js.nft.json
|   |   |   |-- pages-manifest.json
|   |   |   |-- server-reference-manifest.js
|   |   |   |-- server-reference-manifest.json
|   |   |   |-- webpack-runtime.js
|   |   |   `-- webpack-runtime.js.map
|   |   |-- static
|   |   |   |-- chunks
|   |   |   |   |-- 0e5ce63c-a2c6dade7d83b500.js
|   |   |   |   |-- 0e5ce63c-a2c6dade7d83b500.js.map
|   |   |   |   |-- 117-6606742fc74dc217.js
|   |   |   |   |-- 117-6606742fc74dc217.js.map
|   |   |   |   |-- 318-308d4b4ad86f9855.js
|   |   |   |   |-- 318-308d4b4ad86f9855.js.map
|   |   |   |   |-- 359-328a2fe0f89b0849.js
|   |   |   |   |-- 359-328a2fe0f89b0849.js.map
|   |   |   |   |-- app
|   |   |   |   |   |-- layout-9e0a39f017351458.js
|   |   |   |   |   |-- layout-9e0a39f017351458.js.map
|   |   |   |   |   |-- _not-found
|   |   |   |   |   |   |-- page-967be38c1013118f.js
|   |   |   |   |   |   `-- page-967be38c1013118f.js.map
|   |   |   |   |   |-- page-432fc65130006a61.js
|   |   |   |   |   `-- page-432fc65130006a61.js.map
|   |   |   |   |-- bdfe9574-efd6a4734d1a73dc.js
|   |   |   |   |-- bdfe9574-efd6a4734d1a73dc.js.map
|   |   |   |   |-- d3ac728e-b2a801cc4fdffd7a.js
|   |   |   |   |-- d3ac728e-b2a801cc4fdffd7a.js.map
|   |   |   |   |-- fd9d1056-f407056d7e5b3fac.js
|   |   |   |   |-- fd9d1056-f407056d7e5b3fac.js.map
|   |   |   |   |-- framework-bd5ff6d794af4cb2.js
|   |   |   |   |-- framework-bd5ff6d794af4cb2.js.map
|   |   |   |   |-- main-7bc149601488c11a.js
|   |   |   |   |-- main-7bc149601488c11a.js.map
|   |   |   |   |-- main-app-75adfac5e49d8e45.js
|   |   |   |   |-- main-app-75adfac5e49d8e45.js.map
|   |   |   |   |-- pages
|   |   |   |   |   |-- _app-24d053c4ce258bc2.js
|   |   |   |   |   |-- _app-24d053c4ce258bc2.js.map
|   |   |   |   |   |-- _error-2312390b9d603b03.js
|   |   |   |   |   `-- _error-2312390b9d603b03.js.map
|   |   |   |   |-- polyfills-42372ed130431b0a.js
|   |   |   |   |-- webpack-30842f4e13e6767d.js
|   |   |   |   `-- webpack-30842f4e13e6767d.js.map
|   |   |   |-- css
|   |   |   |   |-- 1b6e881145f5df6a.css
|   |   |   |   |-- 1b6e881145f5df6a.css.map
|   |   |   |   |-- 2ac702a5cef895f1.css
|   |   |   |   |-- 2ac702a5cef895f1.css.map
|   |   |   |   |-- 9926b0115860c028.css
|   |   |   |   |-- 9926b0115860c028.css.map
|   |   |   |   |-- 9fe77c0fea5d91de.css
|   |   |   |   |-- 9fe77c0fea5d91de.css.map
|   |   |   |   |-- b822323cc4d90994.css
|   |   |   |   `-- b822323cc4d90994.css.map
|   |   |   |-- media
|   |   |   |   |-- 4473ecc91f70f139-s.p.woff
|   |   |   |   |-- 463dafcda517f24f-s.p.woff
|   |   |   |   |-- KaTeX_AMS-Regular.1608a09b.woff
|   |   |   |   |-- KaTeX_AMS-Regular.4aafdb68.ttf
|   |   |   |   |-- KaTeX_AMS-Regular.a79f1c31.woff2
|   |   |   |   |-- KaTeX_Caligraphic-Bold.b6770918.woff
|   |   |   |   |-- KaTeX_Caligraphic-Bold.cce5b8ec.ttf
|   |   |   |   |-- KaTeX_Caligraphic-Bold.ec17d132.woff2
|   |   |   |   |-- KaTeX_Caligraphic-Regular.07ef19e7.ttf
|   |   |   |   |-- KaTeX_Caligraphic-Regular.55fac258.woff2
|   |   |   |   |-- KaTeX_Caligraphic-Regular.dad44a7f.woff
|   |   |   |   |-- KaTeX_Fraktur-Bold.9f256b85.woff
|   |   |   |   |-- KaTeX_Fraktur-Bold.b18f59e1.ttf
|   |   |   |   |-- KaTeX_Fraktur-Bold.d42a5579.woff2
|   |   |   |   |-- KaTeX_Fraktur-Regular.7c187121.woff
|   |   |   |   |-- KaTeX_Fraktur-Regular.d3c882a6.woff2
|   |   |   |   |-- KaTeX_Fraktur-Regular.ed38e79f.ttf
|   |   |   |   |-- KaTeX_Main-Bold.b74a1a8b.ttf
|   |   |   |   |-- KaTeX_Main-Bold.c3fb5ac2.woff2
|   |   |   |   |-- KaTeX_Main-Bold.d181c465.woff
|   |   |   |   |-- KaTeX_Main-BoldItalic.6f2bb1df.woff2
|   |   |   |   |-- KaTeX_Main-BoldItalic.70d8b0a5.ttf
|   |   |   |   |-- KaTeX_Main-BoldItalic.e3f82f9d.woff
|   |   |   |   |-- KaTeX_Main-Italic.47373d1e.ttf
|   |   |   |   |-- KaTeX_Main-Italic.8916142b.woff2
|   |   |   |   |-- KaTeX_Main-Italic.9024d815.woff
|   |   |   |   |-- KaTeX_Main-Regular.0462f03b.woff2
|   |   |   |   |-- KaTeX_Main-Regular.7f51fe03.woff
|   |   |   |   |-- KaTeX_Main-Regular.b7f8fe9b.ttf
|   |   |   |   |-- KaTeX_Math-BoldItalic.572d331f.woff2
|   |   |   |   |-- KaTeX_Math-BoldItalic.a879cf83.ttf
|   |   |   |   |-- KaTeX_Math-BoldItalic.f1035d8d.woff
|   |   |   |   |-- KaTeX_Math-Italic.5295ba48.woff
|   |   |   |   |-- KaTeX_Math-Italic.939bc644.ttf
|   |   |   |   |-- KaTeX_Math-Italic.f28c23ac.woff2
|   |   |   |   |-- KaTeX_SansSerif-Bold.8c5b5494.woff2
|   |   |   |   |-- KaTeX_SansSerif-Bold.94e1e8dc.ttf
|   |   |   |   |-- KaTeX_SansSerif-Bold.bf59d231.woff
|   |   |   |   |-- KaTeX_SansSerif-Italic.3b1e59b3.woff2
|   |   |   |   |-- KaTeX_SansSerif-Italic.7c9bc82b.woff
|   |   |   |   |-- KaTeX_SansSerif-Italic.b4c20c84.ttf
|   |   |   |   |-- KaTeX_SansSerif-Regular.74048478.woff
|   |   |   |   |-- KaTeX_SansSerif-Regular.ba21ed5f.woff2
|   |   |   |   |-- KaTeX_SansSerif-Regular.d4d7ba48.ttf
|   |   |   |   |-- KaTeX_Script-Regular.03e9641d.woff2
|   |   |   |   |-- KaTeX_Script-Regular.07505710.woff
|   |   |   |   |-- KaTeX_Script-Regular.fe9cbbe1.ttf
|   |   |   |   |-- KaTeX_Size1-Regular.e1e279cb.woff
|   |   |   |   |-- KaTeX_Size1-Regular.eae34984.woff2
|   |   |   |   |-- KaTeX_Size1-Regular.fabc004a.ttf
|   |   |   |   |-- KaTeX_Size2-Regular.57727022.woff
|   |   |   |   |-- KaTeX_Size2-Regular.5916a24f.woff2
|   |   |   |   |-- KaTeX_Size2-Regular.d6b476ec.ttf
|   |   |   |   |-- KaTeX_Size3-Regular.9acaf01c.woff
|   |   |   |   |-- KaTeX_Size3-Regular.a144ef58.ttf
|   |   |   |   |-- KaTeX_Size3-Regular.b4230e7e.woff2
|   |   |   |   |-- KaTeX_Size4-Regular.10d95fd3.woff2
|   |   |   |   |-- KaTeX_Size4-Regular.7a996c9d.woff
|   |   |   |   |-- KaTeX_Size4-Regular.fbccdabe.ttf
|   |   |   |   |-- KaTeX_Typewriter-Regular.6258592b.woff
|   |   |   |   |-- KaTeX_Typewriter-Regular.a8709e36.woff2
|   |   |   |   `-- KaTeX_Typewriter-Regular.d97aaf4a.ttf
|   |   |   `-- px7RLhCTSVOVikbzZXmdp
|   |   |       |-- _buildManifest.js
|   |   |       `-- _ssgManifest.js
|   |   |-- trace
|   |   `-- types
|   |       |-- app
|   |       |   |-- api
|   |       |   |   `-- config
|   |       |   |       `-- route.ts
|   |       |   |-- layout.ts
|   |       |   `-- page.ts
|   |       `-- package.json
|   |-- next.config.mjs
|   |-- next-env.d.ts
|   |-- .nvmrc
|   |-- package.json
|   |-- package-lock.json
|   |-- postcss.config.mjs
|   |-- README.md
|   |-- start-prod.sh
|   |-- tailwind.config.ts
|   `-- tsconfig.json
|-- .gitignore
|-- home
|   `-- ray
|       `-- .cache
|           `-- huggingface
|               `-- modules
|                   `-- transformers_modules
|                       `-- jinaai
|                           `-- xlm-roberta-flash-implementation
|                               `-- 82b68d65447e856379361e8d4054b21f63c97dbc
|                                   `-- modeling_lora.py
|-- jina.code-workspace
|-- model -> /opt/workspace/aibase/jina-embeddings-v3
|-- next.config.js
|-- notepad
|   |-- chathist_copilot.txt
|   |-- Claude_Simple.md
|   |-- CurrentUI.png
|   |-- db_operation_ex.txt
|   |-- fast_memo.txt
|   |-- folder_structure.txt
|   |-- handy_copilot_quickmemo.md
|   |-- HelpChatbot.txt
|   |-- http_retry.txt
|   |-- ImprovedUI.png
|   |-- LaTex_Test_Prompt.txt
|   |-- npmspeedup-debug
|   |-- signupform.txt
|   |-- svelte_workflow
|   `-- table_design.txt
|-- pdfs
|-- README.md
|-- requirements.txt
|-- requirement.txt
|-- tsconfig.json
`-- .vscode
    |-- launch.json
    `-- launch.json_nodejs

86 directories, 516 files
```
