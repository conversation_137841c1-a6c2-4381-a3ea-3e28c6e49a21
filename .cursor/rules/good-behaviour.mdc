---
description: 
globs: 
alwaysApply: true
---
---
description: Rules of good behaviour
globs: 
alwaysApply: true
---
Do not hard code data into code unless specifically requested by me.

If you have an idea on why something is failing, always validate that idea with testing before changing code.

Do not write stub or fake methods. Write production ready code first time.

Do not cheat on tests and checks by adding hard-coded values or assertions.

Do not make statements about functionality working unless you have tested it. Perform your own tests first, do not assume that your changes or my changes just work until you have seen evidence from tests.