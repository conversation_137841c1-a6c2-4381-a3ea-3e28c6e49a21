---
description: 
globs: 
alwaysApply: true
---

# Rules must follow:
1. After you create new file/folder or remove file/folder, pls follow [agent.mdc](mdc:.cursor/rules/agent.mdc) rule

2. [structure.mdc](mdc:.cursor/rules/structure.mdc) and [folder_structure.mdc](mdc:.cursor/rules/folder_structure.mdc) rules contains folders/files structure in this project, pls always aware the same and follow the structure.

3. After made changes in files, pls sync into README.md file for documentation, if the README.md file not existing, pls create.

4. user has created conda environment for you and dont create new environment, pls ask user conda environment name,  and use conda run to make sure you are working under this environment.

5. follow [good-behaviour.mdc](mdc:.cursor/rules/good-behaviour.mdc)

6. Memory Management System rules [memory-management.mdc](mdc:.cursor/rules/memory-management.mdc)
   Comprehensive rules for managing working memory, project memory, and documentation memory

After change, pls update changlog/readme as well as problem-solving doc and memory management doc if necessary.