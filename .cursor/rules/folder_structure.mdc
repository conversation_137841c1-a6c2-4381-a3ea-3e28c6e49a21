# Project Structure

```
.
|-- .ai
|-- backend
|   |-- backup
|   |-- chathist
|   |-- images
|   |-- log
|   |-- models
|   |   `-- jina_embedding_v3 -> ../../model/
|   `-- __pycache__
|-- .byaldi
|-- chathist
|-- .cursor
|   |-- rules
|   `-- script
|-- docs
|   |-- templates
|   `-- working-memory
|       |-- closed
|       |   `-- markdown-latex-enhance-20250403
|       |-- done
|       |   `-- anthropic-token-efficient-20230404
|       `-- open
|-- frontend
|   |-- app
|   |   |-- api
|   |   |   `-- config
|   |   |       `-- __tests__
|   |   |-- fonts
|   |   `-- signup
|   |-- components
|   |   `-- ui
|   |-- hooks
|   |-- images
|   |-- lib
|   `-- .next
|       |-- cache
|       |   |-- eslint
|       |   |-- fetch-cache
|       |   |-- images
|       |   |   |-- 5n70hcvUCOShVezf4VhXDuKV044iA2K22rBe+eLzGE4=
|       |   |   |-- JxfiDdH+cX2PjA5Btzw46NNcEveV7GaTPpPFKXKAlNQ=
|       |   |   |-- p-XhPjYLa2xjTLNhcFksWA6gvhsQzuGQWiDC9LAbRMc=
|       |   |   `-- -RSAFvQOS--p33RVxIA7bvoIgdkmcVbjmJIAwXDdxjs=
|       |   |-- swc
|       |   |   `-- plugins
|       |   |       `-- v7_linux_aarch64_0.106.15
|       |   `-- webpack
|       |       |-- client-development
|       |       |-- client-development-fallback
|       |       |-- client-production
|       |       |-- edge-server-production
|       |       |-- server-development
|       |       `-- server-production
|       |-- server
|       |   |-- app
|       |   |   |-- api
|       |   |   |   `-- config
|       |   |   |-- favicon.ico
|       |   |   `-- _not-found
|       |   |-- chunks
|       |   `-- pages
|       |-- static
|       |   |-- chunks
|       |   |   |-- app
|       |   |   |   `-- _not-found
|       |   |   `-- pages
|       |   |-- css
|       |   |-- media
|       |   `-- px7RLhCTSVOVikbzZXmdp
|       `-- types
|           `-- app
|               `-- api
|                   `-- config
|-- home
|   `-- ray
|       `-- .cache
|           `-- huggingface
|               `-- modules
|                   `-- transformers_modules
|                       `-- jinaai
|                           `-- xlm-roberta-flash-implementation
|                               `-- 82b68d65447e856379361e8d4054b21f63c97dbc
|-- model -> /opt/workspace/aibase/jina-embeddings-v3
|-- notepad
|-- pdfs
`-- .vscode

86 directories
```
