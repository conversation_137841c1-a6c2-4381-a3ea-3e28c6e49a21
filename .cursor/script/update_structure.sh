#!/bin/bash
# save to .scripts/update_structure.sh
# best way to use is with tree: `brew install tree`

# Create directory if it doesn't exist
mkdir -p .cursor/rules

# Create the output file with header
echo "# Project Structure" > .cursor/rules/structure.mdc
echo "" >> .cursor/rules/structure.mdc
echo "\`\`\`" >> .cursor/rules/structure.mdc

# Check if tree command is available
if command -v tree &> /dev/null; then
  # Use tree command for better visualization
  # Use gitignore patterns if .gitignore exists
  if [ -f ".gitignore" ]; then
    # Create a temp exclude pattern file from .gitignore
    TMPFILE=$(mktemp)
    cat .gitignore | grep -v "^#" | grep -v "^\s*$" > "$TMPFILE"
    # Always exclude .git and node_modules explicitly
    echo ".git" >> "$TMPFILE"
    echo "node_modules" >> "$TMPFILE"
    
    # Use the patterns file with tree
    tree -a --gitignore -I "$(paste -sd'|' "$TMPFILE")" --charset=ascii >> .cursor/rules/structure.mdc
    rm "$TMPFILE"
    echo "Using tree command with .gitignore patterns for structure visualization."
  else
    # Fallback to basic exclusions if no .gitignore
    tree -a -I ".git|node_modules" --charset=ascii >> .cursor/rules/structure.mdc
    echo "Using tree command for structure visualization (no .gitignore found)."
  fi
else
  # Fallback to simple find command if tree is not available
  echo "Tree command not found. Using fallback approach with find command."
  
  # Create exclude patterns from .gitignore if it exists
  EXCLUDE_OPTS=""
  if [ -f ".gitignore" ]; then
    while IFS= read -r pattern; do
      # Skip comments and empty lines
      [[ "$pattern" =~ ^# ]] && continue
      [[ -z "$pattern" ]] && continue
      
      # Add as exclusion pattern
      EXCLUDE_OPTS="$EXCLUDE_OPTS -not -path \"*/$pattern/*\" -not -path \"*/$pattern\""
    done < .gitignore
  fi
  
  echo "." >> .cursor/rules/structure.mdc
  # Use eval to properly handle the exclude options
  eval "find . -type d,f -not -path \"*/\.*\" -not -path \"*/node_modules/*\" $EXCLUDE_OPTS" | sort | while read -r line; do
    if [ "$line" != "." ]; then
      indent=$(($(echo "$line" | tr -cd '/' | wc -c) * 4))
      spaces=$(printf '%*s' "$indent" '')
      basename=$(basename "$line")
      if [ -d "$line" ]; then
        basename="$basename/"
      fi
      echo "$spaces├── $basename" >> .cursor/rules/structure.mdc
    fi
  done
fi

# Close the code block
echo "\`\`\`" >> .cursor/rules/structure.mdc

echo "Project structure has been updated in .cursor/rules/structure.mdc"