# Project Target
Using the Jina Embedding v3 model, we chunk a novel and convert it into embedding values, which are then stored in memory and a JSON file. Then, we create a simple dialogue web UI to answer user's questions about the novel.

#Frontend (Shadcn style web UI)
Here, we need to construct a frontend for handling dialogue, using Shadcn and Tailwind. The interface should be in DaisyUI style and include an input box for user's query. Additionally, there should be a function to upload the article to the backend, where it will be converted into embeddings and stored in a JSON file. There should also be a dropdown menu for selecting the language of the article (zh or en) and a text box to display the results returned by the backend.

# Backend (Python)
The backend uses the FastAPI REST API framework. It must first accept the user's uploaded file and if no file is uploaded, it must return an error message saying "Please upload a file". All APIs must check if the user has uploaded a file. Below is a partial code that can be referenced, where:
sentence_chunker: used to chunk the uploaded file content, spacy.blank("en") is used to process English novels, so the frontend must pass the language selected by the user to the backend. We also need to add a "lang" parameter to this function to correspond to the two types of novels, zh and en.
document_to_token_embeddings: used to convert the novel content into individual token embedding vectors.
late_chunking: uses late chunking technology to convert into final chunk embeddings.
late_chunking_query_function_cosine_sim: based on the user's query, uses cosine to find the approximate chunk answer.
Overview of the process: 
Get user uploaded novel -> call Jina embedding model and convert into embedding json file as well as memory variable.
Get user query and use cosine similarity method and get related chunk , return to user 

#Jina Embedding v3 Model initialize
model_path = "/opt/workspace/app/cursor/jina/model"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
model     = AutoModel.from_pretrained(model_path, trust_remote_code=True)

# Reference Code
import spacy
from spacy.tokens import Doc
from spacy.language import Language
import transformers
from transformers import AutoModel
from transformers import AutoTokenizer

def sentence_chunker(document, batch_size=None):
    """
    Given a document (string), return the sentences as chunks and span annotations (start and end indices of chunks).
    Using spacy to do this sentence chunking.
    """

    if batch_size is None:
        batch_size = 10000 # no of characters

    # Batch with spacy
    nlp = spacy.blank("en")
    nlp.add_pipe("sentencizer", config={"punct_chars": None})
    doc = nlp(document)

    docs = []
    for i in range(0, len(document), batch_size):
        batch = document[i : i + batch_size]
        docs.append(nlp(batch))

    doc = Doc.from_docs(docs)

    span_annotations = []
    chunks = []
    for i, sent in enumerate(doc.sents):
        span_annotations.append((sent.start, sent.end))
        chunks.append(sent.text)

    return chunks, span_annotations

def document_to_token_embeddings(model, tokenizer, document, batch_size=4096):
    """
    Given a model and tokenizer from HuggingFace, return token embeddings of the input text document.
    """

    if batch_size > 8192: # no of tokens
        raise ValueError("Batch size is too large. Please use a batch size of 8192 or less.")

    tokenized_document = tokenizer(document, return_tensors="pt")
    tokens = tokenized_document.tokens()
    
    # Batch in sizes of batch_size
    outputs = []
    for i in range(0, len(tokens), batch_size):
        
        start = i
        end   = min(i + batch_size, len(tokens))

        # subset huggingface tokenizer outputs to i : i + batch_size
        batch_inputs = {k: v[:, start:end] for k, v in tokenized_document.items()}

        with torch.no_grad():
            model_output = model(**batch_inputs)

        outputs.append(model_output.last_hidden_state)

    model_output = torch.cat(outputs, dim=1)
    return model_output

def late_chunking(token_embeddings, span_annotation, max_length=None):
    """
    Given the token-level embeddings of document and their corresponding span annotations (start and end indices of chunks in terms of tokens),
    late chunking pools the token embeddings for each chunk.
    """
    outputs = []
    for embeddings, annotations in zip(token_embeddings, span_annotation):
        if (
            max_length is not None
        ):  # remove annotations which go beyond the max-length of the model
            annotations = [
                (start, min(end, max_length - 1))
                for (start, end) in annotations
                if start < (max_length - 1)
            ]
        pooled_embeddings = []
        for start, end in annotations:
            
            if (end - start) >= 1:
                # print(f"start: {start}, end: {end}")
                # print(f"{[e[:5] for e in embeddings[start:end]]}")
                pooled_embeddings.append(
                    embeddings[start:end].sum(dim=0) / (end - start)
                )
                    
        pooled_embeddings = [
            embedding.detach().cpu().numpy() for embedding in pooled_embeddings
        ]
        outputs.append(pooled_embeddings)

    return outputs	


with open("berlin.txt", "r") as f:
    document = f.read()

print(f"First 50 characters of the document:\n{document[:150]}...")

model_path = "/opt/workspace/app/cursor/jina/model"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
model     = AutoModel.from_pretrained(model_path, trust_remote_code=True)
chunks, span_annotations = sentence_chunker(document)
token_embeddings = document_to_token_embeddings(model, tokenizer, document)
chunk_embeddings = late_chunking(token_embeddings, [span_annotations])[0]

# add data with manual embeddings
data = []
for i in range(len(chunks)):
    data.append(wvc.data.DataObject(
            properties={
                "content": chunks[i]
            },
            vector = chunk_embeddings[i].tolist()
    )
)

def late_chunking_query_function_cosine_sim(query, k = 3):

    cos_sim = lambda x, y: np.dot(x, y) / (np.linalg.norm(x) * np.linalg.norm(y))

    query_vector = model(**tokenizer(query, return_tensors="pt")).last_hidden_state.mean(1).detach().cpu().numpy().flatten()

    results = np.empty(len(chunk_embeddings))
    for i, (chunk, embedding) in enumerate(zip(chunks, chunk_embeddings)):
        results[i] = cos_sim(query_vector, embedding)

    results_order = results.argsort()[::-1]
    return np.array(chunks)[results_order].tolist()[:k]	
	


--------------------------------------------------------------------
